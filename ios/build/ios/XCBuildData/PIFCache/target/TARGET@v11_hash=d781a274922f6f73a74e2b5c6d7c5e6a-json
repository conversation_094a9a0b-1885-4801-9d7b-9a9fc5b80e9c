{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9846e0a244eec72782f2b50e0603abf23f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982af7bbe2e998fd445db8e6cdc0b5e588", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873efae8b6fc789a8450002fdac9e9859", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987a9590b93f83f56bf274dd83138603bc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873efae8b6fc789a8450002fdac9e9859", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98de7305c4b5c8a9006638529dd1237cd2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f15d21dde531de81e387a480d23d093b", "guid": "bfdfe7dc352907fc980b868725387e98421e7908325aadc83efe87d34fc8c160"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b03ed2b01655cead58128bd9ceac01d", "guid": "bfdfe7dc352907fc980b868725387e98e2cb369855f601c1261e7c1cb56ea937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98687780dcc42e157117154523627e846c", "guid": "bfdfe7dc352907fc980b868725387e982b5ef046a9874ddeb7d5f1f1379abd9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b5c48f370a4e566b55a0e7639e1a442", "guid": "bfdfe7dc352907fc980b868725387e98e7d5034a04d8731fc3c7cbfec95a1075"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4a11900ee0def87dde13b2572b0ea0f", "guid": "bfdfe7dc352907fc980b868725387e98dde8ebf89bfdac22a0c7d3ac09074c01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e739b52cb83c5ca624bc75546320820", "guid": "bfdfe7dc352907fc980b868725387e984d638dc149b0cd0afd2f70b0ba0a02f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e6cb9af8a649d78d3a2ac35a27e76c1", "guid": "bfdfe7dc352907fc980b868725387e981e038a728ed86396ba30b1ee01738eed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880a3f035fb9b56e3627b59225e5fd48f", "guid": "bfdfe7dc352907fc980b868725387e985d7eee36954fee65a3a731c0f32023a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98129590de23accc8da6c404a20059bff1", "guid": "bfdfe7dc352907fc980b868725387e986c192263e221be6bd66f50a550ceabf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800c25c920b60347029610288fcfff0ee", "guid": "bfdfe7dc352907fc980b868725387e986b38b9abffd4c0295f7e352749b17d5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc5255d2632017a0b53183cd50435d44", "guid": "bfdfe7dc352907fc980b868725387e989fe9002478e71e0e145e20bac8db7d80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a0cce546e4d25667c39475fbe41ba9d", "guid": "bfdfe7dc352907fc980b868725387e98ba9ece118b42154e3abbba2206d17af6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d079bff28f7447b45c485543b499c15", "guid": "bfdfe7dc352907fc980b868725387e98c1c76040f6871f15b797b6b1259ba94a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850364437c1bcb29bfee11035eb137de9", "guid": "bfdfe7dc352907fc980b868725387e98c1e3453ee65d9840b13a18f02a4bdc91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f6269fbd58f462162f794ab9353057e", "guid": "bfdfe7dc352907fc980b868725387e98c38566c67fb7dcf42b045e2a11373de8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877c6a35dcf1bbb19f94cd893971f95e0", "guid": "bfdfe7dc352907fc980b868725387e985e29e59f568f0abb3366694127d185aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986caf99dcd2efa406afcdae033021a2f1", "guid": "bfdfe7dc352907fc980b868725387e98b0cd4d1e4a26c76e8ae1382f71c8d1e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898ec01f8409e9f0426885ccdc6f4c80a", "guid": "bfdfe7dc352907fc980b868725387e9827f871c6c8a28c613b9b591509194236"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db6dcd505b687fcc30fe6d0762e5f8d", "guid": "bfdfe7dc352907fc980b868725387e986f97cb319678592673eeba272820e6a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848e8df996160301dad33252e0f4ee9c6", "guid": "bfdfe7dc352907fc980b868725387e98771cd400281068da982a0c9b0f4bdad9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f307a7ca78c9e2bf038864d19e1da06b", "guid": "bfdfe7dc352907fc980b868725387e9822f881e2a925d0217a16d0b27fe47505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e03f105e0c1b4a5a72210df536e0dfde", "guid": "bfdfe7dc352907fc980b868725387e988d54bf7f9cbf72317600b1f1c270686f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984229261504a9919cab661996db0b8fa5", "guid": "bfdfe7dc352907fc980b868725387e986c4a2a55661efa38df8443994e7dbdf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da946584b91c55530a69b5a2127d0027", "guid": "bfdfe7dc352907fc980b868725387e98df944b460578ea2c8cea1f82f120dddb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861d2c2b5b8bd30589415540e838d7acb", "guid": "bfdfe7dc352907fc980b868725387e98a9dac38469eb18018561081938f0ad9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8b2f71f9e4c5fec7b00d0c5803fd4c8", "guid": "bfdfe7dc352907fc980b868725387e98e2408bc5733e22001094de63d9b515bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848c6c25a157dc21efe5f09e2bf076147", "guid": "bfdfe7dc352907fc980b868725387e98cdb39d60b0e0128c7dc00ab89b7fbfd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b78c1619c032e4d033e29b122e8a35ad", "guid": "bfdfe7dc352907fc980b868725387e98c06e2d23895f229c2f4b3b2c6e52a73a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a23732ab748d0267acec716b6ae3836a", "guid": "bfdfe7dc352907fc980b868725387e9841f4e8f09c4c9add0696486fb715ebf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a49e3394d085f3ac1c7bb00cf6e5aa0", "guid": "bfdfe7dc352907fc980b868725387e981938954696fca4f64d3ecd7e06da9bf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d3b6e69c1182c0024d830162e05f39e", "guid": "bfdfe7dc352907fc980b868725387e9824c5bf4954b06908b19370c6890840af"}], "guid": "bfdfe7dc352907fc980b868725387e980cd24d3f3b673897a5eca9db73a9a767", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dfef273ab335cd0bdfe6cf173ce43a68", "guid": "bfdfe7dc352907fc980b868725387e9886169ce2dd55adf30ec215d392f178c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f2832c5fe4cff50ff9db3364e8a17c", "guid": "bfdfe7dc352907fc980b868725387e98eadbfe454f34cb25574621f98e5a68a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b671de89febc5ebeb017b35feaca0176", "guid": "bfdfe7dc352907fc980b868725387e9887a10e4bd2e25e9d8e409ea14be48ff7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aac60a8e67511113b31906373332fd4e", "guid": "bfdfe7dc352907fc980b868725387e98d899e4c1b2b8bedaba65e595f87ca338"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988672b7f88fe14d7e23ea4e6f8769ea94", "guid": "bfdfe7dc352907fc980b868725387e98cf27f206cdba64f8e01adba901f57e4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e597845fab9f40a182d52bb6af879326", "guid": "bfdfe7dc352907fc980b868725387e985c86a74e36509b598efc8886a09a70ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800f47897de78fd3c63e444b01fe41648", "guid": "bfdfe7dc352907fc980b868725387e98dd637e6a07e40eff0dfbca7beef7e600"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f0517a8d1bba4a138cc28b43ca6eecf", "guid": "bfdfe7dc352907fc980b868725387e98274aaf20e76f865d4cdc36eb458a016f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bae65f1148f87937b46e646452100a77", "guid": "bfdfe7dc352907fc980b868725387e98d8e0248a580817a641eeb4dfb920ec31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b45e49d4981923a48a3148f1cc151e0", "guid": "bfdfe7dc352907fc980b868725387e982c75f632eaa059a79079c0cbc2bc887c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0b07fa7d2aed68d1724eb004626aa3b", "guid": "bfdfe7dc352907fc980b868725387e98b291a6449e76227fe299523599f92e88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980346db41369c2571e514e9fc75cfda6a", "guid": "bfdfe7dc352907fc980b868725387e9834697b2eef29dfe40f1c46845c26ce33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c78cde35bed41622e3a4760f888093a8", "guid": "bfdfe7dc352907fc980b868725387e98a5f043a26d268664555c29bb1c9e4830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbdb8218f4a881b0f8d4921f8e8fbdf5", "guid": "bfdfe7dc352907fc980b868725387e9830497f224a1f62c3aa193b9ba52d57e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981429bf1383f5accca031ae83b4a59554", "guid": "bfdfe7dc352907fc980b868725387e98976e18ab41475c1da1cbbb6183f26eb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb7a0d6db3d016818d0c1f1ae6dc6db5", "guid": "bfdfe7dc352907fc980b868725387e98ef132fd8339760493c719d9b88ef03cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b54e0a18908489e26f76c85aed6ea62", "guid": "bfdfe7dc352907fc980b868725387e98d6bb2a8f84298daaaaa936f0f596c80c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a810f847d82e8405385a0ebee1da2b19", "guid": "bfdfe7dc352907fc980b868725387e983c954510acb15b9e3c53a8534b062df7"}], "guid": "bfdfe7dc352907fc980b868725387e9867aca479d60e0d4665310ae93c34c5a0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98598543d9b483dc71db8a761c4971c702", "guid": "bfdfe7dc352907fc980b868725387e98036c8b33163f6cb1a0cd02845ad3e2ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988588d4b41f9a658daee30dc7219482d3", "guid": "bfdfe7dc352907fc980b868725387e98bf2905e1ad09b21706695c359ed4ff27"}], "guid": "bfdfe7dc352907fc980b868725387e98de652428b0881a5cecbc003f6fac829f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9806d2af2c721fbc3f916c2d473edca088", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9886a2b9bce9ac7a2c489ab63b7d507111", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}