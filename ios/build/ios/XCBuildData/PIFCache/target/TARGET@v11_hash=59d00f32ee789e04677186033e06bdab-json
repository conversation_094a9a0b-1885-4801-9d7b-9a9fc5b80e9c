{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983cb432fce7e126ca8ffb5df8f9dd38b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989297272400ab97bc14f97bf15ba7cf27", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988beb425d2bac6d97c83c58c8155653be", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c633daee61d5cfe6352b26d046da4f11", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988beb425d2bac6d97c83c58c8155653be", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983c279ffc34b270f58072508e8eab1548", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98756885c188ab328191db2c75ca19447e", "guid": "bfdfe7dc352907fc980b868725387e98bc649dd34b189c47d0ac514bb833ed35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98466c8b80d9539526c690af03d84ed564", "guid": "bfdfe7dc352907fc980b868725387e983f4ce16fea065b34e9fa2098fb197869"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801c290f350335954527c2ecbcf739319", "guid": "bfdfe7dc352907fc980b868725387e984aa9d4422b72d755d3f0a64821040ba1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98530a63db8871a00fa23277f07c9d01c3", "guid": "bfdfe7dc352907fc980b868725387e9812fc9a5763ad04111199b54b34789c79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c79f135b0aa9a0d3924998afbe453c4b", "guid": "bfdfe7dc352907fc980b868725387e9801d44cc3c068695a64626291ed4d5f01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868070e60806e4bb04eb49d72fd821419", "guid": "bfdfe7dc352907fc980b868725387e986318d77c1e372e9ceb8cc74296ea14e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5b9cf72accff11e1a89fa9222d41d73", "guid": "bfdfe7dc352907fc980b868725387e98e6cd49455200fc2cbf2f7c547440eb65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98759616a5e595c85be3ab3eeb6909afee", "guid": "bfdfe7dc352907fc980b868725387e9808ce72ec5cfdece3165d1d755f12c26f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b493559695981b929ac9a8f6fc536b6c", "guid": "bfdfe7dc352907fc980b868725387e9846df1406417cc8e5acfa1f684e8278b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897ca196d0d2ffad255db14ce5b529ee4", "guid": "bfdfe7dc352907fc980b868725387e9844015bb442c53acd94be4e024ff70353", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2a2bd703a61a0997d271f690563249", "guid": "bfdfe7dc352907fc980b868725387e98b82549536e023570dc937c2015d275bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f4702bd9ab03aa488ac4b9ccff6c875", "guid": "bfdfe7dc352907fc980b868725387e98835b500a23e32563061f2cc55dcfddd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daacd225e7faf19732f1db49455afd7a", "guid": "bfdfe7dc352907fc980b868725387e988cf59fb4d0596e41807c5d3f590f22d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9692540754d0feba5816db99c991a1c", "guid": "bfdfe7dc352907fc980b868725387e98a26044bc8a739762361069961c4dc204"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc5ebb482cdb8b745818a2c16e1fc05", "guid": "bfdfe7dc352907fc980b868725387e984c355b2ebc6dbad6b8ead1c237f5e80f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889365bdf51ac1f5337574a2547d1d2f3", "guid": "bfdfe7dc352907fc980b868725387e98cc75a62a4e2a0b3e5449e352f9e5538c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f618908ac9a16a6f52ef8e7834a55342", "guid": "bfdfe7dc352907fc980b868725387e98dc0c4da505f2f48173b2811c9ee9372b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0a5e20266e4cf4678e3f10552f3a17e", "guid": "bfdfe7dc352907fc980b868725387e98f3d319eb3938d543ddca12d6cf298806", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a47c599237a87228101ba01ec51ada7", "guid": "bfdfe7dc352907fc980b868725387e988d58ccdbd92ec5e10065dbf6674608e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b31038280437a57674173f7a073a24e4", "guid": "bfdfe7dc352907fc980b868725387e9864c8afa8bec5d14e865ef67ddabfb438"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e443232424cb516c9e50a3ee42713c00", "guid": "bfdfe7dc352907fc980b868725387e98eb82897b7e83dcbd9530fbc47b1566b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864c76d96040ca291878fcf13205d6040", "guid": "bfdfe7dc352907fc980b868725387e98f89d647d242bc9efc6a937f5d74c48e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a76e0fee7dd01732bd4cb049f23bd58", "guid": "bfdfe7dc352907fc980b868725387e9855eaf78d40b7e7927a9d5df7ca96becd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d06ccc53348871989ae3a674429f7c8", "guid": "bfdfe7dc352907fc980b868725387e98056bc09d3b7968431326a492f0d8b9ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e88d71fd47486d6f38c57aeee3f731f9", "guid": "bfdfe7dc352907fc980b868725387e98c34c119e55d5844c401c88ed907719ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98127394ee7ad792e89c1a37513561364d", "guid": "bfdfe7dc352907fc980b868725387e98a9b29ebec9ec2e96c17e767460460589"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aada64a39bf65838a68008149ae51bc7", "guid": "bfdfe7dc352907fc980b868725387e98de46431f1bf71a6e928294677ea9d548"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be0a914d3199067a3c478fcf8a12480f", "guid": "bfdfe7dc352907fc980b868725387e98c1013e8db9276fd2feb089264284b45a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989924f61749df537a4e8a1533d497824c", "guid": "bfdfe7dc352907fc980b868725387e989b812c835fb185df74723735b5d0e210"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830acac6bcb6614b5447663698ed3dd6b", "guid": "bfdfe7dc352907fc980b868725387e98ab9f0e158080c19fcd08bd39e9308816"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b45b72e28e22036d92034b61bce9f87d", "guid": "bfdfe7dc352907fc980b868725387e98250b9fb1d72929452db466fd24049871"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb90fa59d6887e8e41a5954036948359", "guid": "bfdfe7dc352907fc980b868725387e98ba8a51751e0d6a39e62b0be31db90354", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa14788a91333288b5576f68c3daf00b", "guid": "bfdfe7dc352907fc980b868725387e98f9a554e72d7de90513c2df69b453e77a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a24d62dccc3d999017eef06f21aa8a7", "guid": "bfdfe7dc352907fc980b868725387e98864c3f0662a81886824b0fbee4b0e85f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98991cccbbb61f59c4662967a7f948c0b3", "guid": "bfdfe7dc352907fc980b868725387e983793cf7feb2e53b0c7e8a05241faedd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a771cdc3ad4075262edcee0f06c79126", "guid": "bfdfe7dc352907fc980b868725387e98ab9a762b03486af4770d59bc6c45581e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c560bafd03fa732ddcf16d0c8932d56", "guid": "bfdfe7dc352907fc980b868725387e982243b19712908ef958fbacb366db35ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a245dcb06c5d0082944fe5cbbc6a2a5", "guid": "bfdfe7dc352907fc980b868725387e980d2b857dc930cf71af76a7e2ce8e66da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caad27d3a517175c3e602a355985075b", "guid": "bfdfe7dc352907fc980b868725387e98e7896ecc6d322e075c11b6cf66c5de45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98360c5502d66f755caa9984f2b9c6bce7", "guid": "bfdfe7dc352907fc980b868725387e984dc61f217147970b445457c7649f0914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d1b001b357759ac6301d0d2d4a2e082", "guid": "bfdfe7dc352907fc980b868725387e9811254ec8b3523006b2c1f31601256a68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831926ec937e03a9ae9d7aa9ac79e8497", "guid": "bfdfe7dc352907fc980b868725387e9813abc141b129d0ed60f1d1f03f00379f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873843d5e791ef9db9b50366fd7d82085", "guid": "bfdfe7dc352907fc980b868725387e98ae8482ae7e66d4880ed3d18803c76343"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98777bdb5cd172590bd6bd85744e982595", "guid": "bfdfe7dc352907fc980b868725387e98fac4085e3e6e507a1092d349ce5a153b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8120f1801aa05db2e55b2e454ebaf1b", "guid": "bfdfe7dc352907fc980b868725387e9855d6692aa2a47a0766866d8f89027e6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98263b3459f2d2c89eb15622ba62d62817", "guid": "bfdfe7dc352907fc980b868725387e98c5d01d6c7465cce20d553c80be782b69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c22e84c8c77be90afa5ef48cfe20a4af", "guid": "bfdfe7dc352907fc980b868725387e98048b14c60ee5b586aacee1c079371a57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b432e8c437a4e935db177d046b1b30aa", "guid": "bfdfe7dc352907fc980b868725387e98e091bb58c4842029b0c8dfca658e253b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98562900f6466b4e1eeef849e6fa106091", "guid": "bfdfe7dc352907fc980b868725387e983f469a73faa4683e9c6ac9b889fb8f1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983879584b7c9550c2dd2056aa1fd35942", "guid": "bfdfe7dc352907fc980b868725387e98cfffd718aec68814ac70f4109c01902a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f81196809e65e4d562756a1a76f6f61", "guid": "bfdfe7dc352907fc980b868725387e989a39c25de297768e054f92c3369ef193"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d46dda176fe4ce08325f1a1506e5a7be", "guid": "bfdfe7dc352907fc980b868725387e98557aec303d2ddf535bedcd5d21693e16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d26f285bdb624d4bf9cd57a67811b040", "guid": "bfdfe7dc352907fc980b868725387e98971c4d4431bc0b3dd2c0e93c1e5a1a35"}], "guid": "bfdfe7dc352907fc980b868725387e983afa6deb8891b51d9a1e7b31ed51ea99", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988dd226490ee71dca3cd564ecb4ce90ec", "guid": "bfdfe7dc352907fc980b868725387e983ab546922cf8773fcb42b5acc609f62d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98611e707c80392e634754b94404588fc3", "guid": "bfdfe7dc352907fc980b868725387e98105b07928ab8ae113268c04b33a1aa06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d039bcd6df73683d1021eb19d33557cd", "guid": "bfdfe7dc352907fc980b868725387e98deb9ce7530f09b7539ae2a519a3ff8ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cea0e814e7918efe21c377610e0fb752", "guid": "bfdfe7dc352907fc980b868725387e98a276c819be6004def2caed39dfe2b55e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4cf185d89f5cb89670e4494a5b61700", "guid": "bfdfe7dc352907fc980b868725387e986695e54d6a3aab35d2b201d23f1c03e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868b2278ebb630397e22fe91a069ff6c8", "guid": "bfdfe7dc352907fc980b868725387e9883be8a2684c638bb9417db56cc26cdf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e1a52f40153fa422b3722243f882395", "guid": "bfdfe7dc352907fc980b868725387e98d54b9ffce5409e53cde46db2502d9342"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9815625757be58f1126686efc5c38ad", "guid": "bfdfe7dc352907fc980b868725387e985c6db3e11afa4ed72efef46f6800360b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b66d11f4ba3b7dd52502b886572308a0", "guid": "bfdfe7dc352907fc980b868725387e980e955643579f1211637b780ab51ffc51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847338caf2896a7c2077380e592ecce39", "guid": "bfdfe7dc352907fc980b868725387e98a64fa818aed319908d1ca9c1e901ca80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd56d8c86b8ee5d42d8b322421c08a78", "guid": "bfdfe7dc352907fc980b868725387e988711fc650d593655b52a80bd474c5681"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc01700945397e751c366ff32a1069a0", "guid": "bfdfe7dc352907fc980b868725387e98b65c4e908f864ca8078476fdc70d5f6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9b287a8bfcf835226c80ff8f9262e50", "guid": "bfdfe7dc352907fc980b868725387e98b58a6a7374f1b73401f0b66bbd5ed5d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e7dd439400c0e898e646e0f168b76c3", "guid": "bfdfe7dc352907fc980b868725387e98a2485127178eb02c40a54d8c6e3e89f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98220eeefff322412673259aaeb8e63225", "guid": "bfdfe7dc352907fc980b868725387e98398bd202e264a20153c436d201b7828a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae2e50a1de48c56fb0d586e3c6f61573", "guid": "bfdfe7dc352907fc980b868725387e983343609e5c39faa3d59a88909cdea469"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98292709a98c1a45e329ecb65cd0181d4e", "guid": "bfdfe7dc352907fc980b868725387e98bccbc01ac93293e39d14eb29a1f8ac60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3376271b5d99c089cd685a11aeb7013", "guid": "bfdfe7dc352907fc980b868725387e982a841cf628e4eaaf27a2ea9f43da6595"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825c74d3c04986d2889760bdfad1b7da4", "guid": "bfdfe7dc352907fc980b868725387e9853b39e7fb63b55b42a88b8ab5d9b513c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ddbca38f4bbdeeee868aab96482da59", "guid": "bfdfe7dc352907fc980b868725387e982940fe19bd06fffb266feebcb19f60e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882bc2d5e00308be06ac3c65c880d4f96", "guid": "bfdfe7dc352907fc980b868725387e98d4de4cb53715c97ec041f3f2949b604f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e85d5acd15a29fda4588bfecfe71dd5", "guid": "bfdfe7dc352907fc980b868725387e980e4b5bce8fb743f147cc817330dd9aee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986da60b5e283d901e25523ccb70a2fe9c", "guid": "bfdfe7dc352907fc980b868725387e98743247aeef373816a8ddf16d88a28d2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ef5e2f0b85e8478876edfd38c98e09", "guid": "bfdfe7dc352907fc980b868725387e98c315baa747cb5105f171316bbe6e78eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f039db931b0cd4ce352ee9acc8b278c", "guid": "bfdfe7dc352907fc980b868725387e984963108228b0884ec7c7413bc9920419"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98841d53d90c4a6929755acce2f8ed707b", "guid": "bfdfe7dc352907fc980b868725387e98312d8124a4a4e06d7892bb306afe2d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981348726ddbede2dadfc87dafaaae0538", "guid": "bfdfe7dc352907fc980b868725387e986a39c46e0e5b1c2e86e8a985872a1ac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1b5207bf570dfb175f7b06131169f0b", "guid": "bfdfe7dc352907fc980b868725387e9882c21be92244b944f1eb0d3a01038a1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f7931c200b5dd8835aeaced2b09cbbe", "guid": "bfdfe7dc352907fc980b868725387e9825bb9f231602082733c3df84beecc81f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b09cc8c468b340a4676c80e4354e952", "guid": "bfdfe7dc352907fc980b868725387e98ac6cc783113bc6cde9ef73fa78ae0561"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f686a9fc11aed56258e88b32057987c", "guid": "bfdfe7dc352907fc980b868725387e988f156e4c587f317af3448cf24a83cfe9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859e3fab73d3a42c9e3bb5fdfe10caad5", "guid": "bfdfe7dc352907fc980b868725387e98c906b5cca65700c6e80951e4211319b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984034e235207e82f53eaa03976cd96306", "guid": "bfdfe7dc352907fc980b868725387e9878639ee6a803c344312b9e09810a5fc7"}], "guid": "bfdfe7dc352907fc980b868725387e98b5fbe86506fa7babcb9f33557e6ba724", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98598543d9b483dc71db8a761c4971c702", "guid": "bfdfe7dc352907fc980b868725387e983b36f5b88aa6e8321ad9119c9e05bbad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f79fa56db675f51b170ffe3b6d8b02a4", "guid": "bfdfe7dc352907fc980b868725387e9895efde027e9e99da08e3fbf773e71f0b"}], "guid": "bfdfe7dc352907fc980b868725387e98862b64e3549ce46208b218fe44b63597", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98716b89212231d5b7118c1334b6f26044", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e984d6226424af7f4b20ecf8a8ce0f597e2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}