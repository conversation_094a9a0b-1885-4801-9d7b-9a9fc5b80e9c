{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c05aa5d0656b73349669ad14c8e4b9b6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98607babafe4f5a94bceba348a672faad9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d8772e65e06e2ddbac211df3f7d8341", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9871735d08af236ca76c262d47f18274e3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d8772e65e06e2ddbac211df3f7d8341", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983d4437cc3e8f67bccb89defb1610e2d1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f1635fec34d4f9ab8a2fef711c05f6ea", "guid": "bfdfe7dc352907fc980b868725387e9800e08f56a9469c140ebe5b9a0bc12f42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0bba17406b7357815bef8e8e6aa557f", "guid": "bfdfe7dc352907fc980b868725387e980389b9f3d8f9a75ac574e5bf04295c84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ef3f2c60ce191a78aa000b0a5cbf5e8", "guid": "bfdfe7dc352907fc980b868725387e98467e0faab15dca2b25f963a036257fce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb1e5e79703ff1539b90ba9d79c0147e", "guid": "bfdfe7dc352907fc980b868725387e982bcfef2a19bc86310dbe1d50d4e7df05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898c6af0d48e09e858528a405e2a822d7", "guid": "bfdfe7dc352907fc980b868725387e98e5ea972ac83d9b03e1416fddbbdc9665"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9c9ac4dc6a12d2db34d7bbe5eb3150c", "guid": "bfdfe7dc352907fc980b868725387e981a35c807079c30dc7e88bcbcb57302e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896aad944f2e64b907f8c71285a5ce236", "guid": "bfdfe7dc352907fc980b868725387e98695b0c54baaa90da53ab10b0aceac0ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d8123a7fe84708047c34205f252434e", "guid": "bfdfe7dc352907fc980b868725387e9858c640b0dc10e0f9911dbc64a2c97d30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858e5fe8e0338fe4782360160444bb5d1", "guid": "bfdfe7dc352907fc980b868725387e986fc7c75ea50b73c612c9a2967eca43fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807c020e11d169f51135f6c7452753b3b", "guid": "bfdfe7dc352907fc980b868725387e98d6f5587c0c645416b7e9e6682d7c2ba4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8a8c42e5de9c0f0b479f0534244cae6", "guid": "bfdfe7dc352907fc980b868725387e98a793d2ab4f7036a5e953e53a420a5469", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889cdf3647f92e05c4fd5ee8460320672", "guid": "bfdfe7dc352907fc980b868725387e980d3e6926bd7bf552ce1504ee64e01bce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b1873d32fe47978ad61d488b2399bd8", "guid": "bfdfe7dc352907fc980b868725387e989969974fd1ed1116078ee6b049f4866a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb7b08c8089c722f2191079c25f8f679", "guid": "bfdfe7dc352907fc980b868725387e9835a254fed0e6bd2c64a8c9029ce4c157"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98615f9e18a8d4ebc23a9afbbe39e1edb5", "guid": "bfdfe7dc352907fc980b868725387e987a8703c620820b76ea2dbe28a83cac2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d6e4d860f79800cce5722ae57d714ce", "guid": "bfdfe7dc352907fc980b868725387e9803d2074d3433c0cf81ec49bd2ed3c5ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cfc3e56a500b48d70395cf6b2eee918", "guid": "bfdfe7dc352907fc980b868725387e981078f7e420480b1e882dd4cb1987189e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a52eb75f48e72e9004f8df1b46a1d6e8", "guid": "bfdfe7dc352907fc980b868725387e987c2455d8a73ad759badb21d0318b27c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98508e4ae3aa68d1cab1bf12dcdb7543ab", "guid": "bfdfe7dc352907fc980b868725387e987f23d7c1bc63f854746cdc4968bad98c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806deb0251eb71909fd2e56a4bc3049cf", "guid": "bfdfe7dc352907fc980b868725387e9850c7fbf421bed04146c719ff0669e224"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876f242fd7ad14e9c6c4aa68bdedb57cd", "guid": "bfdfe7dc352907fc980b868725387e98294cac9638725ed7c1156bc9a311e709", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98579b5a0adfcb55e329cec7f04a3adbe4", "guid": "bfdfe7dc352907fc980b868725387e98c3306ebd894c217966996a4667ecffe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986397425ae212ec312a37f089b43670d1", "guid": "bfdfe7dc352907fc980b868725387e98bd66c4b6dbd88f6d95e606e77eeca6f8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98284d9e3ec7edf1e6e59b47e3a804b222", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9888d1f4d9b84cd145699b68b98a054f0e", "guid": "bfdfe7dc352907fc980b868725387e985ecfb8fb569035f208047ce9b7390367"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d347d9fff5b97d90f0d20851205fe874", "guid": "bfdfe7dc352907fc980b868725387e98bdda32384192932062b82c50e40b5e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa7f484868033d4b341f49bae0815b1a", "guid": "bfdfe7dc352907fc980b868725387e9823da92de650f87274176799fc4cf3115"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ebbb4f808690905c4908ef617087b0c", "guid": "bfdfe7dc352907fc980b868725387e98161d9e2d582090d480ee9af671d51f1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980517fe2abe43c3de9979f41ade01093e", "guid": "bfdfe7dc352907fc980b868725387e981e8027f79b71ed562cc0e7c6a9b4a5be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bad68cd58b0e3048fd1cab8608ede563", "guid": "bfdfe7dc352907fc980b868725387e982c08dff4be3ae5c0415ba690177df331"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836061eda1cfe94de716a7a5ae8faffe6", "guid": "bfdfe7dc352907fc980b868725387e98a659bf645de173fb9393cf45efe71cc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852f80a4c5a5caaf185636cb1bf57490f", "guid": "bfdfe7dc352907fc980b868725387e984fb361e5dd15c9a24414cc9393b1fcf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880891bec682612326a5d4d212efd2819", "guid": "bfdfe7dc352907fc980b868725387e983e1768c7afc19ba2e5254c76a50429e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d85c9d31238afeccd1700fe2e0cde415", "guid": "bfdfe7dc352907fc980b868725387e9887264df72643ccf7ee83fe200ea4e8e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c7a07907705552f285377932c360849", "guid": "bfdfe7dc352907fc980b868725387e9845948d734f51832977fe00d40f183b0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830c0e8a8e3337d3931e46693b66f9e16", "guid": "bfdfe7dc352907fc980b868725387e980ce7acc20ca2fcb69105fcaa85a609b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc7109f0fcbec29149dccfcf5f8a34e5", "guid": "bfdfe7dc352907fc980b868725387e987acb6abf4b700a9349b234336cb125fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f85ce387c69c7245c35064a233f88c4", "guid": "bfdfe7dc352907fc980b868725387e98a95594fc19c4fd0ed08628e0b6dca944"}], "guid": "bfdfe7dc352907fc980b868725387e98d847df178e48b1f43d4fb3e396b1ebec", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98598543d9b483dc71db8a761c4971c702", "guid": "bfdfe7dc352907fc980b868725387e98c377e67378c2b8220793b84ef68618a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854aedc33db3ee991374b0e616283cc9f", "guid": "bfdfe7dc352907fc980b868725387e98a5d0417d4c8ed262887d5e29e9738e51"}], "guid": "bfdfe7dc352907fc980b868725387e98168522076146b3d95d19a57a93635528", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9881617f9e9ead168b4a2ca13b3d1e5b0c", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e981406d4acab17f4c93e38fb35c4d8d480", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}