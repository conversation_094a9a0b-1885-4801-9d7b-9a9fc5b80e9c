{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987ba89f9b5cec961f2f8c5732f2cf76f4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a060b3d29e01244fb4b673fd2e66e29", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f3349831d60956a3f1b5e11a142f7e51", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f81b37ce89cf686122e2125357ee8fb0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f3349831d60956a3f1b5e11a142f7e51", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fad185e0785ada158cd750c2b39ac83d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9827172ca1482b397291e12384e53a2369", "guid": "bfdfe7dc352907fc980b868725387e986fecf2931645643990d554bf86bfbfa4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98137057a908c125a233cf454e145dbece", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982eada01b92a714a693122249bd86c0d8", "guid": "bfdfe7dc352907fc980b868725387e984d81297bac29b985b22403eafaa6cf57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844328d628f1b7bfe85c8f8191ea4ac11", "guid": "bfdfe7dc352907fc980b868725387e9815474c812192dc606ee575ce54dd9809"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f98abef373ddbb7470e0f77edd6d524", "guid": "bfdfe7dc352907fc980b868725387e984d51ba2f1e0812a6bf948dd6ff96198a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af16daeee096fcf87d435ca6c571f2a1", "guid": "bfdfe7dc352907fc980b868725387e9872a37548bc40c7a33b662e38f84c2465"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1e4ea8e81428321b9aa09dc89b07afe", "guid": "bfdfe7dc352907fc980b868725387e98c00f97c1d40e8a774c52cfc19960c314"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c7e6e2acc040f313c09b61fec5c64ba", "guid": "bfdfe7dc352907fc980b868725387e9814472e2215f04d7d1b75aa8371434b74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893ad302499a292bb4a4888f255f00c89", "guid": "bfdfe7dc352907fc980b868725387e98700cdf74925ae2c266b087d6db37a4af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fee029b37a9e69d498520a1500acd03", "guid": "bfdfe7dc352907fc980b868725387e9898bdb005e495fbf404644c42207e7efd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d54ab4a46a36aa8503c8442a9546a6c7", "guid": "bfdfe7dc352907fc980b868725387e98e2fcd39771feb2a851d782476d58eb4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820c6118e74ade7d68bb0350d160e5ef5", "guid": "bfdfe7dc352907fc980b868725387e9819631cf29bf8a31d2442dc40df3d7e7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895d9d011c25810cb79c8eab862efa3d6", "guid": "bfdfe7dc352907fc980b868725387e987b8c8f5ae8fa44c9f8c1df9fdad6db28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c0f9e338158c3be11cc42538a12cd0a", "guid": "bfdfe7dc352907fc980b868725387e988491ccc2f47b26407c116f9b93f5e132"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98348dce2a7e37f7db839ca85dde3fb81b", "guid": "bfdfe7dc352907fc980b868725387e98b6d603cee1366b07c9247b64c4277632"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dffc596e54e2c8ce049764317304835", "guid": "bfdfe7dc352907fc980b868725387e98b8cd11346e55370ed2e953ca6b8922d7"}], "guid": "bfdfe7dc352907fc980b868725387e9831f3e26abd6f64d986dfde20fb100118", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98598543d9b483dc71db8a761c4971c702", "guid": "bfdfe7dc352907fc980b868725387e98855e18db97ca4294d6bf072a9b323889"}], "guid": "bfdfe7dc352907fc980b868725387e98e1356a7dc10130af37455746eab3d51b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98654516459f41aa7dbd645570be539e76", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98f318057ff38f847fff124b24c1f07e98", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}