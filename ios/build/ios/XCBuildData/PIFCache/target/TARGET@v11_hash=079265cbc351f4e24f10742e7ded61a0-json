{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862ec806b929348543816eca6197dec56", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleUtilities", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "GoogleUtilities", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "GoogleUtilities_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98ebf12a9287fccd6908075a86478dc6e0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0c2dc3274fe55fe5b5c7896db18f2f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleUtilities", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "GoogleUtilities", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "GoogleUtilities_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9868ffe225a1655aa6f69f39b992818b8b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0c2dc3274fe55fe5b5c7896db18f2f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/GoogleUtilities", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "GoogleUtilities", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "GoogleUtilities_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98bd5cfba2cda6919c32e92a357ba35932", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98575852a41151d2153145cbb4a5e459c5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983d2f4b7953439ad70a3a03bd40b85373", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9843c7501ca79981f8df9a1b5406a65c4d", "guid": "bfdfe7dc352907fc980b868725387e984b0690a7bbab0bc847a70b49cc9aac80"}], "guid": "bfdfe7dc352907fc980b868725387e989c7c0d788a24d38e9a3b15a80ab67dfe", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981f1852a7971aaa5e479d216071487d3a", "name": "GoogleUtilities_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}