{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f6dc754677f18c0929d74e5b0d0eef56", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982aa49fd59f69c380fabe89894237c7f9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f8d086829fb5cf9e09df48d248c6d796", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9889007656ab1dd365dd3acf664ca91140", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f8d086829fb5cf9e09df48d248c6d796", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b799affb4e4b3cec3b74d892a9cf89b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dcdc123955cb79f5ad04027ea3a65957", "guid": "bfdfe7dc352907fc980b868725387e985b291fcdf2ddfad1da9878010fc7aaad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801f9b104fec350a794e021baaec0a578", "guid": "bfdfe7dc352907fc980b868725387e98f6d499d11a867f4019e66b398915deb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eaf59cf6c428f1014e79ca6044b1a45", "guid": "bfdfe7dc352907fc980b868725387e9881d84caf2314b8fe166237ddeaace18c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d45b46e7410cbf89690f8c5da7392fbf", "guid": "bfdfe7dc352907fc980b868725387e98d70619d0ba15ff3de441c082e717be8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4bf3d6f14253500a3dcf02fe71748d1", "guid": "bfdfe7dc352907fc980b868725387e98314cd67e9fa262f0aeae2a940f8dd1ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5f767bbc1bf423377c9652703c5ae36", "guid": "bfdfe7dc352907fc980b868725387e98d01ef0ba52601ff0b463212478a4c144", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834691cd8f248bd6642bd9276205e6d62", "guid": "bfdfe7dc352907fc980b868725387e98e1b3d353427c244b2b43b2a57c582468", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b1749747ec6c0138c156ca2da517acc", "guid": "bfdfe7dc352907fc980b868725387e98780568f824541b50631c2876ffbf50d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf984701842cb05454b0966c74b66dd9", "guid": "bfdfe7dc352907fc980b868725387e9858a8125b5a2971b7d5e6433a6d06a06f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e84ad5cefacb8b44e3c110695c9ed1f", "guid": "bfdfe7dc352907fc980b868725387e987e703ece9270f6e94095e69ab4f55872", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986159f869ed76edf285af3ac407e1cd4a", "guid": "bfdfe7dc352907fc980b868725387e98a69af9162207302bcaf656ada1de9c17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c2f82a0f6ebe7e382aa5fb99af95fc2", "guid": "bfdfe7dc352907fc980b868725387e987543c75c1a8441129e556f9e30dea99c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98584e77e1a30671b30d69dd550aacdec9", "guid": "bfdfe7dc352907fc980b868725387e981231ccc7cf5c89fbbf3f690cbdcbe4f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d6adc7f673a8da29b82159a0e736f0c", "guid": "bfdfe7dc352907fc980b868725387e988d2c93e17c5a3af4c7bb4eaeea2307e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1d1c92fc1116177aa1cb5a37d4b4b7f", "guid": "bfdfe7dc352907fc980b868725387e98429bf0d44e79dd5fe2a05fed6a0d8e8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d619087f50a6c18954746edaa38c012", "guid": "bfdfe7dc352907fc980b868725387e98ff90e8fd160f71a140067ed21325ee8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880eaefb4373fc3d90a55ad047767a8db", "guid": "bfdfe7dc352907fc980b868725387e98ea2c35b133c66d2207d4a2865a1e300f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b5925ad4c48994e93d453285e218aba", "guid": "bfdfe7dc352907fc980b868725387e9832e3019fdc3834ae130021030abedf16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c75bf12ddf45721a1fbb68159e0f22d7", "guid": "bfdfe7dc352907fc980b868725387e989b9cafa007c1df4bc2ef1e09c4f6a5b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98933a152eb1fba5e9aa85678b459e9410", "guid": "bfdfe7dc352907fc980b868725387e983dc0ad440621d5cb6ab8cf87c70a8f94", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98262a81189ceee122c623e539e8560956", "guid": "bfdfe7dc352907fc980b868725387e98358d35f65857c0dcae293796b2b83be1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890de164f151d7f01f00d28a7318ed454", "guid": "bfdfe7dc352907fc980b868725387e983ba21d46842157074c4279e11506ced2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989b82971dc7c080767bcd7a33466d218b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846b59e3f582c31c992d8e4f35e58082e", "guid": "bfdfe7dc352907fc980b868725387e980cbb5f0d496645d86aa461b73cc86895"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc1709c17b377dc880f7a0f0cc57d493", "guid": "bfdfe7dc352907fc980b868725387e983f870dec78f496f90a96dafce779b053"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98620c0b213d940ce4430b1f8757c2e511", "guid": "bfdfe7dc352907fc980b868725387e98f1ed0489833f9cbe9535ac953cc365b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9545cbd38961cdbe87a6999d78dc630", "guid": "bfdfe7dc352907fc980b868725387e983f518041effc4782e677e7140f5de376"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c80b24ccd4893331ec3007b13761ae85", "guid": "bfdfe7dc352907fc980b868725387e9880e7e54e54fa55cc42e565c8d28b2b33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bbd4c93f2f8f892aaefa2a88e36c71f", "guid": "bfdfe7dc352907fc980b868725387e98443e79d760239982a0721fe752bd7099"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d83f1e479cf08a67caef050557f9f6d7", "guid": "bfdfe7dc352907fc980b868725387e987bb1b00fad4aa0f32a351df6f66dd267"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d08958fc8e10edf1962fbe64b68b8383", "guid": "bfdfe7dc352907fc980b868725387e9864ca8f65bb39658e6b7008dad5856652"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edc26add36701dbc611cabd4ff67239e", "guid": "bfdfe7dc352907fc980b868725387e982789e281ae5baf5c38e449c6680f9bc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ba95d12d807df82db26a87663544483", "guid": "bfdfe7dc352907fc980b868725387e9809ae212b56a1d842167a9d556a69ce30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877a54786d457e8e3fde2e63de143db1a", "guid": "bfdfe7dc352907fc980b868725387e980c4b0fb14c5f57825c323401799537e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893dceeff5197ea06cca7e68877432175", "guid": "bfdfe7dc352907fc980b868725387e980ec806e1f07566d8b489ff6650dcf242"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b14985114f3b40590c0de55ecee5e619", "guid": "bfdfe7dc352907fc980b868725387e9811ba17b7c672f7ea647346bae1bb72e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8c36c0497cd3a5b9b495ba4a7e35a4f", "guid": "bfdfe7dc352907fc980b868725387e98f794f192df45685374e55d6f35b58aa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f7e993a8b4dd70de42058a18497a704", "guid": "bfdfe7dc352907fc980b868725387e9803a05d55bda1032370bd69151676bfcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ede58b8028156a08cfea39077da84e63", "guid": "bfdfe7dc352907fc980b868725387e983bff6aa963e54090050e9f2b99deb112"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d1229815c1ccf0a9075285b541cbd5", "guid": "bfdfe7dc352907fc980b868725387e980840ec6f761495944a667428f9d3884a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0fd87e6cd12a130568108c852d185cb", "guid": "bfdfe7dc352907fc980b868725387e9862bb05b3cb63a566ba47c9e895088bec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887c532e13920ae37dd6f81773fda5c0c", "guid": "bfdfe7dc352907fc980b868725387e9804f170144b1cecb3fa063777e96443d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98472ad86faf86a1e3beb12da4b336d35e", "guid": "bfdfe7dc352907fc980b868725387e9846c0fe9f0d9d9b102402b2942aaff9a3"}], "guid": "bfdfe7dc352907fc980b868725387e98648bada9ea83502e177baf2c493bbce1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98598543d9b483dc71db8a761c4971c702", "guid": "bfdfe7dc352907fc980b868725387e982d465706ee84e8054f21b43b932ba928"}], "guid": "bfdfe7dc352907fc980b868725387e98b48ea94e1255357879c6b15d6f04df4b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b255934a7a5f1cf0737ff205fae4d114", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9807c71e87ad11fd1c1c949233268e953d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}