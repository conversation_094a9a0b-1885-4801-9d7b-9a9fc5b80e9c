{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9862ec806b929348543816eca6197dec56", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b1e4d93a0981c071ffcf424b2e9218d4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0c2dc3274fe55fe5b5c7896db18f2f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ddbb94eba11ba36690efb713e4f8e03f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0c2dc3274fe55fe5b5c7896db18f2f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98567d9ee1cb20223164a85ea86b5f2e7e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9801ed3d62218dfcfa0859f8b8f84c03fd", "guid": "bfdfe7dc352907fc980b868725387e982d0259aa49f99bccab9ab53e328bb45c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853f4c39f231cf6de239619151aeedfec", "guid": "bfdfe7dc352907fc980b868725387e98895238e52bbc98f5dedc6bd2fdeb5d32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6d02e74bb520dc808dea9b87f581a3d", "guid": "bfdfe7dc352907fc980b868725387e987f054ff6e90106f65e2a79e8e4395ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab68e28c5c4af52806f50fc8a92fe9d0", "guid": "bfdfe7dc352907fc980b868725387e98ba659fccae9f3ea9fa51c4203304c858", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851c20a4afaced08bce4e47e86a243fee", "guid": "bfdfe7dc352907fc980b868725387e98c7346c9d9a8de6b3419556f85d6cca8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cb0b6a5c833d6b656ce4cd06e6d864c", "guid": "bfdfe7dc352907fc980b868725387e9836b4b05af60a4e6b498483f71fcc5a41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974f44e148dfac908936b46ff39e71b1", "guid": "bfdfe7dc352907fc980b868725387e98c676ed31982e0ab9238351ea57c08ef4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982925a847024d0be046a5524c05361809", "guid": "bfdfe7dc352907fc980b868725387e98d736268bf4e456e5d2a01ee22044a091", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4c15d2ea1d5d79029ab1452d8108f51", "guid": "bfdfe7dc352907fc980b868725387e98490b2bc4e03c9d0b2681ae9a45b0d846"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c799183ca9e0dff495e35756c315b08d", "guid": "bfdfe7dc352907fc980b868725387e98e8fa743dc82906aeb6ad01c8920e33ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852bf3fd0fb8f065dd95f9ad4ae03cf9a", "guid": "bfdfe7dc352907fc980b868725387e9828d55e8e84e076e5924cb7ddb7c3279b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b44716e4f05acd3ce03ba2481bbb5107", "guid": "bfdfe7dc352907fc980b868725387e989d3b02e2d338c3009ec8c53fefd39089", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e86e91c8724f6b9b56cd5e845ab5786", "guid": "bfdfe7dc352907fc980b868725387e98c18c07504599f70a77c7f62698190cbf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f757b39d9b8b76ab9baa6306ac059802", "guid": "bfdfe7dc352907fc980b868725387e98afb6470750173b9e9b2c9c53aa07350c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98173407b0edab95ecc2d8742f271b2034", "guid": "bfdfe7dc352907fc980b868725387e985807d62944c5d801bd2fcc00e89e2b75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d00244101354c670f641849520be7d9", "guid": "bfdfe7dc352907fc980b868725387e98de4b729afa1e2c51e2ab0a005cc58a8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98918a55734162bfaf19823772ba8d82e2", "guid": "bfdfe7dc352907fc980b868725387e98b5e3106146b5c3abab42aa3ca7c129be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3c1fba6901fed82c2aa258420390947", "guid": "bfdfe7dc352907fc980b868725387e98bcbd514dc9013bc11d8d90b1e9658b16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a4906abbf800372b07ab7f2d3754c9", "guid": "bfdfe7dc352907fc980b868725387e98f3e2a2ec9a807b8cbef00b4141db22d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0afa310c82b233653b39680d7fecaff", "guid": "bfdfe7dc352907fc980b868725387e9817a007b25260ebba0c3ad062940127c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fe2ecd311d6ae49a03c94be1c4b7feb", "guid": "bfdfe7dc352907fc980b868725387e980dfa07f2d8bae0ebc2ab011c444e381b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832e7a7c398675f6e4d99b6ee51fcb60e", "guid": "bfdfe7dc352907fc980b868725387e9827700689b0299996c57ac32dcef490cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fefdb014c7d0d27fc9778a2c8d17f2f5", "guid": "bfdfe7dc352907fc980b868725387e9870391c59f2637880c7aeaca77cafdb05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98533ae7cb732439a8025c93e6d7a5b09c", "guid": "bfdfe7dc352907fc980b868725387e980321ca9d9949a8d1e91a55a3ed118c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a84430a2c8565bb72adf7a86ae9e8a3", "guid": "bfdfe7dc352907fc980b868725387e98c220617685a4f399f90a479a767df779", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819eebe9c1112780dec89d3866a66eeb9", "guid": "bfdfe7dc352907fc980b868725387e98e22f5e700e9d69dc13cde8289f2b14c1"}], "guid": "bfdfe7dc352907fc980b868725387e983a36d5888e546f104f2569d288715a03", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aaa26d08192d03b06b553da8b3693627", "guid": "bfdfe7dc352907fc980b868725387e98605ea08515c044e3fba6cb2e017067c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865a245cc3e6db3ce5589c1d19251f0ff", "guid": "bfdfe7dc352907fc980b868725387e9817d598da43313257657b7e13756c0cd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98372910a706203a29af9eca3ad8d82e52", "guid": "bfdfe7dc352907fc980b868725387e9867c5c3859ab09788ff9bb59420320fbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c286a19d9ad3aefa1ae773af12fd2b43", "guid": "bfdfe7dc352907fc980b868725387e981f741a6ea14be5d406a386975e6746bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1380914f493a81c7bc754461cd50757", "guid": "bfdfe7dc352907fc980b868725387e983d46beac11bc2362fb288fbc03fcd610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831927b6a3facbc292b93ec992c1a5e65", "guid": "bfdfe7dc352907fc980b868725387e985747048f366234f5c560f47a82358784"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871147f9110eb1d4bff83ed087c6980d6", "guid": "bfdfe7dc352907fc980b868725387e9828f1110b321a90a0d4dfe875fae680ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852863c18fd5a6084ee534afc64b9a2db", "guid": "bfdfe7dc352907fc980b868725387e98a41171e1931099832355d73fac0eed27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818d0248f6aeb9eeaae6752f822f957c4", "guid": "bfdfe7dc352907fc980b868725387e98cb75a002cfdc44bf373a0357e22635d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c20fd3682faeade7bbdbb0604519fef", "guid": "bfdfe7dc352907fc980b868725387e982313264aa12e9098d822bc5cce6b51bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869fe63b38a427e856761cba8b225e61b", "guid": "bfdfe7dc352907fc980b868725387e98b94a3a3218c69d8ecd8a7b5648f48c30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c66d1dc0ffe65bef520991129650ab0", "guid": "bfdfe7dc352907fc980b868725387e98d7980d8fb562d149a0715d979aa27595"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98893d2545c9069b2100d0296d0e2b5ea6", "guid": "bfdfe7dc352907fc980b868725387e984266ad9dc889163997af544e615fbfa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876efd37f26336303cc23a767ca20c769", "guid": "bfdfe7dc352907fc980b868725387e98e1081c10eb73dfcabb3ea9ee9a9a23c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c6d7e44d6bd00dd8dcca1e66ce377de", "guid": "bfdfe7dc352907fc980b868725387e981259c51f74ca83128bc037c93cb4277d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989968749d80f197fd43eabbabc5a419f0", "guid": "bfdfe7dc352907fc980b868725387e98e470cf69cdde380316f65996f822235e"}], "guid": "bfdfe7dc352907fc980b868725387e987ddc9331c802856f65db5d387f667178", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98598543d9b483dc71db8a761c4971c702", "guid": "bfdfe7dc352907fc980b868725387e9806de1c08e6cfca28366a4fed19b44943"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988588d4b41f9a658daee30dc7219482d3", "guid": "bfdfe7dc352907fc980b868725387e98ad6840d658f65cb299823aef63fc7ce7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f79fa56db675f51b170ffe3b6d8b02a4", "guid": "bfdfe7dc352907fc980b868725387e98765742c7f4c5a1d617f6af8f490366c8"}], "guid": "bfdfe7dc352907fc980b868725387e98c211e8beb09e19d1e6ddd83859d50d7e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fbe7a91458d8bf539b1d2d0d663addd2", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e982387e2df3559e05272a273a7e3fd29e1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}