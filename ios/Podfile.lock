PODS:
  - Firebase/CoreOnly (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - Firebase/Messaging (11.13.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.13.0)
  - firebase_core (3.14.0):
    - Firebase/CoreOnly (= 11.13.0)
    - Flutter
  - firebase_messaging (15.2.7):
    - Firebase/Messaging (= 11.13.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.13.0):
    - FirebaseCoreInternal (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (11.13.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_picker_ios (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - Toast (4.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - Toast

EXTERNAL SOURCES:
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  Firebase: 3435bc66b4d494c2f22c79fd3aae4c1db6662327
  firebase_core: 700bac7ed92bb754fd70fbf01d72b36ecdd6d450
  firebase_messaging: 860c017fcfbb5e27c163062d1d3135388f3ef954
  FirebaseCore: c692c7f1c75305ab6aff2b367f25e11d73aa8bd0
  FirebaseCoreInternal: 29d7b3af4aaf0b8f3ed20b568c13df399b06f68c
  FirebaseInstallations: 0ee9074f2c1e86561ace168ee1470dc67aabaf02
  FirebaseMessaging: 195bbdb73e6ca1dbc76cd46e73f3552c084ef6e4
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: a5a732f069baa862e728d839dd2ebb904737effb
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  fluttertoast: 76fea30fcf04176325f6864c87306927bd7d2038
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: 251cb053df7158f337c0712f2ab29f4e0fa474ce

COCOAPODS: 1.16.2
