{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter_windows_3.27.2-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\TGRA-Ecommerce\\android\\app\\.cxx\\Debug\\14s3g5d6\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\TGRA-Ecommerce\\android\\app\\.cxx\\Debug\\14s3g5d6\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}