{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\TGRA-Ecommerce\\android\\app\\.cxx\\Debug\\3w5s482s\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\TGRA-Ecommerce\\android\\app\\.cxx\\Debug\\3w5s482s\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}