{"auth": {"password": "Password", "welcomeBack": "Welcome back!", "loginToAccount": "Login to your account", "phoneNumber": "Phone number", "forgotPassword": "Forgot password?", "login": "<PERSON><PERSON>", "dontHaveAccount": "Don't have an account?", "createAccount": "Create account", "registerWelcome": "Happy to have you join us!", "registerDescription": "Enter your phone number to create your account", "fullName": "Full name", "confirmPassword": "Confirm password", "confirmationCodeMessage": "You will receive a verification code on your phone. Make sure you have your phone with you.", "confirm": "Confirm", "alreadyHaveAccount": "Already have an account?", "signIn": "Sign in", "email": "Email"}, "splash": {"splash": "Everything You Want and More"}, "start": {"electronicPlatform": "Electronic Platform", "saudi": "Saudi ", "digitalMarketing": "For Digital Marketing", "collectBrands": "Bringing Together the Best Saudi Brands", "begin": "Let's Start", "previous": "Previous"}, "onboarding": {"next": "Next", "previous": "Previous", "shopEase": "Shop With Ease, ", "supportLocal": "Support Local !", "enjoyTheQuality": "And Enjoy Exceptional Quality!", "allBrandsNeed": "Everything You Need From Brands ", "saudi": "In Saudi Arabia!", "inOnePlace": "All In One Place!", "shopBestBrands": "Shop From The Best Brands ", "fashionToFurniture": "From Fashion To Furniture"}, "validation": {"required": "{field} is required.", "username": {"required": "Username is required.", "invalid": "Invalid username."}, "email": {"required": "Email is required.", "invalid": "Invalid email address."}, "password": {"required": "Password is required.", "minLength": "Password must be at least 6 characters.", "uppercase": "Password must contain at least one uppercase letter.", "number": "Password must contain at least one number.", "specialChar": "Password must contain at least one special character."}, "confirmPassword": {"required": "Password confirmation is required.", "mismatch": "Password and confirmation do not match."}, "phone": {"required": "Phone number is required.", "invalid": "Invalid phone number. It should be in the format 966+XXXXXXXXX."}, "addresss": {"required": "Address is required.", "invalid": "Invalid address."}, "city": {"required": "City name is required.", "invalid": "Invalid city name."}, "apartmentNumber": {"required": "Apartment number is required.", "invalid": "Invalid apartment number."}, "buildingNumber": {"required": "Building number is required.", "invalid": "Invalid building number."}, "address": {"address": "Addresses", "addNewAddress": "Add New Address", "addAddress": "Add Address", "saveAddress": "Save Address", "fullName": "Full Name", "phoneNumber": "Phone Number", "city": "City", "appartmentNumber": "Apartment Number", "buildingNumber": "Building Number", "notes": "Notes", "update": "Update Address"}, "youhavenofavorites": "Your favorites list is empty."}, "otp": {"accountVerification": "Account verification code!", "enterCodeSentTo": "Enter the verification code sent to", "validOtp": "Enter a valid OTP", "confirm": "Confirm", "resendCode": "Resend code", "didNotReceiveCode": "If you did not receive a message", "resend": "Resend code", "seconds": "seconds"}, "search": {"search": "Search", "searchResults": "Search results", "searchHistory": "Search history", "resultsAreOut": "No more results", "didnotFindWhatWeWereLooking": "Sorry, We Didn't Find What We Were Looking For!", "tryUsingOtherWords": "Try using other words or check your spelling", "allCategories": "All Categories"}, "profile": {"myOrders": "My Orders", "addresses": "Addresses", "editPassword": "Edit password", "newPassword": "New password", "oldPassword": "Old password", "confirmNewPassword": "Confirm New password", "forgetYourPassword": "Forgot your password ?", "editProfile": "Edit Profile", "changePassword": "Change Password", "favorite": "Favorites", "language": "Language", "support": "Support", "aboutUs": "About Us", "vision": "Vision", "termsAndCondation": "Terms and Conditions", "usagePolicy": "Usage Policy", "logout": "Logout", "deleteAccount": "Delete Account", "fullName": "Full Name", "phoneNumber": "Phone Number", "email": "Email", "city": "City", "update": "Update", "changeimage": "Change Image", "pickfromdevice": "Pick From Device", "pickfromCamera": "Pick From Camera", "theNext": "The Next", "logouterror": "Logout Error", "logout?": "Log out?!", "wanttologout": "Are you sure you want to log out?", "cancellation": "Cancellation", "deleteaccount?": "Delete account!?", "wanttodelete": "Are you sure you want to delete your account (you will lose all your data)?", "deleteaccount": "Delete acc", "noChange": "No change"}, "filter": {"filterAndSelect": "Filter and select", "price": "Price", "allCategories": "All categories", "who": "From", "theCategory": "The Category", "to": "To", "evaluation": "Rating", "sortByMostToLeastRated": "Sort by highest to lowest rating", "theBrand": "Brand", "allBrands": "All brands", "filtering": "Filtering", "theOffer": "Offer", "riyal": "Riyal"}, "home": {"whatAreYouThinkingAbout": "What are you thinking about?", "bestOffers": "Best offers", "bestSeller": "Best seller", "haveANiceDay": "Have a nice day!", "categories": "Categories", "cart": "<PERSON><PERSON>", "clothes": "<PERSON><PERSON><PERSON>", "market": "Market", "homeFurniture": "Home furniture", "healthAndBeauty": "Health and beauty", "brands": "Brands", "brandsDesc": "PUMA, a global sports brand since 1948, designs the best quality and performance footwear and apparel for you. Choose me to combine speed, strength, and style in every step!", "showProducts": "Show products", "orderNow": "Order now", "tShit": "T-shirt", "showMore": "Show more"}, "categories": {"categories": "Categories", "thereAreNoProductsInThisSectionYet": "There are no products in this section yet", "browseOurOtherSections": "Browse our other sections for the best deals."}, "payment": {"payment": "Payment", "totalPrice": "Total Price", "address": "Address", "addNewAddress": "A New Address Must Be Added", "newDeliveryAddress": "A New Delivery Address Must Be Added", "paymentMethod": "Payment Method", "noAddressSelected": "the Address not saved", "addAddress": "Add Address", "selectAddressBeforeCheckout": "Please select an address before completing the order", "notes": "Notes", "fullName": "Full Name", "cardNumber": "Card Number", "expireDate": "Card Expiration Date", "cash": "Cash", "credit": "Credit", "chooseYourPayment": "Please choose your payment method first"}, "cart": {"payment": "Payment", "emptyBasket": "Your Basket is Currently Empty!", "cartDesc": "Add your Favorite products and enjoy a unique shopping experience with us"}, "brands": {"brands": "Brands", "foryou": "For you", "filtering": "Filtering", "price": "Price", "evaluation": "Eveluation", "products": "Products", "brandevaluation": "Brand Evaluation", "evaluationDesc": "The brand has ", "toBeSure": "To be sure", "viewProducts": "View Products", "noProductInBrand": "There Are No Products In This Brand Yet", "browseOurOTherSections": "Browse Our Other Sections For The Best Deals"}, "orders": {"orders": "Orders", "details": "Details", "totalPrice": "Total Price", "discount": "Discount", "payment": "Payment", "discountCode": "Discount Code", "activation": "Activation", "cancellation": "Cancellation", "productevaluation": "Product Evaluation ", "evaluationDesc": "The product has ", "description": "Description", "yourreview": "Write your review here...", "toBeSure": "To be sure", "orderno": "Order No: ", "implementationstages": "Implementation Stages", "productinformation": "Product Information", "name": "Name : ", "phone": "Phone number : ", "date": "Date : ", "address": "Address : ", "paymentmethod": "Payment Method : ", "paymentreceipt": "Payment Receipt", "copy": "Copy", "download": "Download", "yourorder": "Your Order", "evaluationproduct": "Eveluation", "myorders": "My orders", "Pending": "Pending", "delivered": "Delivered", "canceled": "Canceled", "noPendingTitle": "No Pending Orders", "noPendingDescription": "There are currently no pending orders. Orders will appear here once available.", "noDeliveredTitle": "No Delivered Orders", "noDeliveredDescription": "There are currently no delivered orders. Orders will be shown here once they are completed.", "noCancelledTitle": "No Cancelled Orders", "noCancelledDescription": "There are currently no cancelled orders. Orders will be displayed here if any get cancelled."}, "mainLayout": {"home": "Home", "brands": "Brands", "more": "More"}, "notification": {"notification": "Notifications", "buyNow": "Buy Now", "day": "Today", "aWeekAgo": "A week ago", "noNewNotifications": "No New Notifications", "noNotifications": "No Notifications", "notificationDesc": "We will notify you when there are important offers or updates"}, "favorite": {"favorite": "Favorites", "favoritesEmpty": "Favorites List is Empty", "favoritesDesc": "Add The Products You Like to Find Them Easily At Any Time"}, "address": {"address": "Address", "addNewAddress": "Add New Address", "addAddress": "Add Address", "saveAddress": "Save Address", "fullName": "Full Name", "phoneNumber": "Phone Number", "city": "City", "appartmentNumber": "Apartment Number", "buildingNumber": "Building Number", "notes": "Notes", "update": "Update Address", "editAddress": "Edit Address", "theAddress": "The Address", "defaultAddress": "Set as default address", "noSavedAddresses": "No Saved Addresses", "addANewAddressToDeliver": "Add a new address to deliver your purchases easily and quickly"}, "productDetails": {"productDetails": "Product Name", "ratings": "Ratings", "loading": "Loading...", "noRatings": "No Ratings", "similarproducts": "Similar products", "NoSimilarProducts": "No Similar products", "AddToCart": "Add To Cart", "Somethingwentwrong": "Something went wrong", "Evaluation": "Evaluation", "Showless": "Show less", "Showmore": "Show more", "PumaTshirtscombine": "Puma T-shirts combine style and athletic performance, making them an ideal choice for sports activities and everyday wear. Puma T-shirts combine", "Pumatshirtsareshort": "Puma T-shirts are short between no sport, and choose the perfect option for sports activities and everyday wear Puma T-shirts are multi between sporty phoenix, and enable the perfect option for sports activities and everyday wear Puma T-shirts are many between sporty vintage, and choose the perfect option for sports activities...", "selectVaration": "please select varation", "addToCartSuccess": "add to cart success"}, "youhavenofavorites": "Your Favorites is Emtpy", "subTotal": "Sub Total", "subTotalAfterApplyCoupon": "Subtotal After Coupon", "couponDiscountAmount": "Coupon Discount Amount", "couponCode": "Coupon Code", "tax": "Tax", "shipping": "Shipping", "currency": "<PERSON><PERSON><PERSON><PERSON>", "total": "Total", "support": {"noTickets": "No Tickets", "noTicketsDescription": "You don't have any tickets", "supportText": "Support", "ticketNumber": "Ticket Number", "contactSupport": "Contact Support", "messageStatus": "Message Status", "messageTitle": "Message Title", "messageDetails": "Message Details", "send": "send", "urgent": "<PERSON><PERSON>", "medium": "Medium", "normal": "Normal", "SentSuccessfully": "Your request has been sent successfully.", "willReply": "We will reply to you within 24 hours ", "next": "Next"}, "termsOfUse": {"termsofuse": "Terms of Use", "termsText": "Text"}, "privacyPolicy": {"privacyPolicy": "Privacy Policy", "privacyText": "Text"}, "details": "Details", "message": "Message", "please_enter_valid_message": "Please enter a valid message"}