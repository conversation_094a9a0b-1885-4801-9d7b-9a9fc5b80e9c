import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tegra_ecommerce_app/core/routing/app_router.dart';
import 'package:tegra_ecommerce_app/tegra_app.dart';

void main() {
  testWidgets('Counter increments smoke test', (WidgetTester tester) async {
    // Create an instance of AppRouter to pass to TegraApp.
    final appRouter = AppRouter();

    // Build our app and trigger a frame.
    await tester.pumpWidget(TegraApp(
      appRouter: appRouter,
      token: null, // You can set a token if needed for the test.
    ));

    // Verify that our counter starts at 0.
    expect(find.text('0'), findsOneWidget);
    expect(find.text('1'), findsNothing);

    // Tap the '+' icon and trigger a frame.
    await tester.tap(find.byIcon(Icons.add));
    await tester.pump();

    // Verify that our counter has incremented.
    expect(find.text('0'), findsNothing);
    expect(find.text('1'), findsOneWidget);
  });
}
