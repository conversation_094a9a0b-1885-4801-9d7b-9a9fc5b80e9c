import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_helper.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_keys.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
import 'package:tegra_ecommerce_app/core/fcm.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/routing/app_router.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/utils/bloc_observer.dart';
import 'package:tegra_ecommerce_app/firebase_options.dart';
import 'package:tegra_ecommerce_app/tegra_app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await setupDependencyInjection();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  await DioHelper.init();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  await EasyLocalization.ensureInitialized();
  await CacheHelper.init();
  // await CacheHelper.clearAllSecuredData();

  Bloc.observer = MyBlocObserver();

  AppConstants.userToken =
      await CacheHelper.getSecuredString(key: CacheKeys.userToken);

  try {
    await Firebase.initializeApp();
    await PushNotificationService().initialize();
  } on Exception catch (e) {
    print(e);
  }

  logSuccess(
    "userToken: ${AppConstants.userToken}",
  );
  logSuccess(
    "Fcm: ${CacheHelper.getData(key: CacheKeys.deviceToken)}",
  );

  runApp(
    EasyLocalization(
      saveLocale: true,
      useFallbackTranslations: true,
      fallbackLocale: const Locale('ar', 'EG'),
      // fallbackLocale: const Locale('ar', 'EG'),
      supportedLocales: const [
        Locale('ar', 'EG'),
        Locale('en', 'UK'),
      ],
      path: 'assets/languages',
      child: Phoenix(
        child: TegraApp(
          appRouter: AppRouter(),
          token: AppConstants.userToken,
        ),
      ),
    ),
  );
}
