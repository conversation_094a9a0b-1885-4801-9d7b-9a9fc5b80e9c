import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/banner/banner_data_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/repos/home_repo.dart';

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit(this.homeRepository) : super(HomeInitial());

  final HomeRepository homeRepository;
  BannerDataModel? bannerDataModel;
  ProductCardPaginatedModel? bestSellerProductCardModel;
  ProductCardPaginatedModel? bestOfferProductCardModel;
  bool isLoadingMore = false;
  int currentPage = 1;
  final ScrollController scrollController = ScrollController();
  int currentBannerIndex = 0;
  bool isListSearch = true;

  void changeIndex(int index) {
    currentBannerIndex = index;
    emit(HomeIndexChanged());
  }

  void toggleSearchMode() {
    isListSearch = !isListSearch;
    emit(SearchModeToggleState());
  }

  Future showAllBanners() async {
    emit(ShowAllBannersLoading());
    final result = await homeRepository.showAllBanners();

    result.when(
      success: (data) {
        bannerDataModel = data;
        emit(ShowAllBannersSuccess());
      },
      failure: (errorHandler) {
        emit(ShowAllBannersError());
      },
    );
  }

  Future getBestSeller() async {
    emit(GetBestSellerLoading());
    final result = await homeRepository.getBestSeller();

    result.when(
      success: (data) {
        bestSellerProductCardModel = data;
        emit(GetBestSellerSuccess());
      },
      failure: (errorHandler) {
        emit(GetBestSellerError());
      },
    );
  }

  Future getBestSellerPagination() async {
    if (isLoadingMore ||
        (bestSellerProductCardModel?.data?.meta?.currentPage ?? 1) >=
            (bestSellerProductCardModel?.data?.meta?.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(GetBestSellerLoadingMore());

    final nextPage = currentPage + 1;
    final result = await homeRepository.getBestSeller();

    result.when(success: (data) {
      bestSellerProductCardModel?.data?.product
          ?.addAll(data.data?.product ?? []);
      currentPage = data.data?.meta?.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(GetBestSellerSuccess());
    }, failure: (error) {
      isLoadingMore = false;
      emit(GetBestSellerError());
    });
  }

  Future getBestOffers() async {
    emit(GetBestOffersLoading());
    final result = await homeRepository.getBestOffers();

    result.when(
      success: (data) {
        bestOfferProductCardModel = data;
        emit(GetBestOffersSuccess());
      },
      failure: (errorHandler) {
        emit(GetBestOffersError());
      },
    );
  }

  Future getBestOffersPagination() async {
    if (isLoadingMore ||
        (bestOfferProductCardModel?.data?.meta?.currentPage ?? 1) >=
            (bestOfferProductCardModel?.data?.meta?.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(GetBestOffersLoadingMore());

    final nextPage = currentPage + 1;
    final result = await homeRepository.getBestOffers();

    result.when(success: (data) {
      bestOfferProductCardModel?.data?.product
          ?.addAll(data.data?.product ?? []);
      currentPage = data.data?.meta?.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(GetBestOffersSuccess());
    }, failure: (error) {
      isLoadingMore = false;
      emit(GetBestOffersError());
    });
  }
}
