part of 'home_cubit.dart';

abstract class HomeState {}

final class HomeInitial extends HomeState {}

/// Home Index
final class HomeIndexChanged extends HomeState {}

/// Search Mode Toggle States
final class SearchModeToggleState extends HomeState {}

/// Check Is Search States
final class CheckIsSearchState extends HomeState {}

/// Filter States
final class FilterCategorySelected extends HomeState {}

final class FilterOfferSelected extends HomeState {}

final class FilterRatingSelected extends HomeState {}

/// Show All Banners States
final class ShowAllBannersLoading extends HomeState {}

final class ShowAllBannersSuccess extends HomeState {}

final class ShowAllBannersError extends HomeState {}

/// Get Children Categories
final class GetChildrenCategoriesLoading extends HomeState {}

final class GetChildrenCategoriesSuccess extends HomeState {}

final class GetChildrenCategoriesError extends HomeState {}

/// Get All Best Seller
final class GetBestSellerLoading extends HomeState {}

final class GetBestSellerLoadingMore extends HomeState {}

final class GetBestSellerSuc<PERSON> extends HomeState {}

final class GetBestSellerError extends HomeState {}

/// Get All Best Offers
final class GetBestOffersLoading extends HomeState {}

final class GetBestOffersLoadingMore extends HomeState {}

final class GetBestOffersSuccess extends HomeState {}

final class GetBestOffersError extends HomeState {}

/// Get Products By Category
final class GetProductsByCategoryLoading extends HomeState {}

final class GetProductsByCategorySuccess extends HomeState {}

final class GetProductsByCategoryError extends HomeState {}

/// Load Products
final class LoadProductsLoadingState extends HomeState {}

final class LoadProductsSuccessState extends HomeState {}

final class LoadProductsErrorState extends HomeState {}
