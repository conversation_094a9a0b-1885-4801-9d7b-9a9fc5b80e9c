import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:page_transition/page_transition.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/main_app_bar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/show_more/show_more_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/show_more/show_more_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/home_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/screens/show_all_best_offer_screen.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/screens/show_all_best_seller_screen.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/banner/banner_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/brands_row_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/categories/categories_row_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/best_offer/best_offers_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/best_seller/best_seller_widget.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/screens/search_screen.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // logSuccess(context.read<CartCubit>().productModel!.data.first.name);
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MainAppBarWidget(),
          SizedBox(height: 20.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: CustomTextFormFieldWidget(
              readOnly: true,
              // onTap: () => context.pushNamed(Routes.searchScreen),
              onTap: () {
                PersistentNavBarNavigator.pushNewScreen(
                  context,
                  screen: BlocProvider(
                    create: (context) => SearchCubit(getIt()),
                    child: SearchScreen(),
                  ),
                  withNavBar: true,
                  pageTransitionAnimation: PageTransitionAnimation.cupertino,
                );
              },
              // onTap: () => Navigator.of(context).push(
              //   PageTransition(
              //     type: PageTransitionType.fade,
              //     child: SearchScreen(
                    // products: context
                    //     .read<HomeCubit>()
                    //     .bestSellerProductCardModel!
                    //     .data!
                    //     .product!,
              //     ),
              //   ),
              // ),
              backgroundColor: AppColors.neutralColor100,
              borderRadius: AppConstants.borderRadius + 2,
              borderColor: AppColors.neutralColor100,
              borderWidth: 1.w,
              prefixIcon: SvgPicture.asset(
                'assets/images/svgs/search_icon.svg',
                fit: BoxFit.scaleDown,
              ),
              hintText: 'home.whatAreYouThinkingAbout'.tr(),
              hintStyle: Styles.contentRegular.copyWith(
                color: AppColors.neutralColor1200,
              ),
            ),
          ),
          SizedBox(height: 8.h),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 32.h),
                  const BannerWidget(
                    isTop: true,
                  ),
                  SizedBox(height: 50.h),
                  CategoriesRowWidget(),
                  SizedBox(height: 50.h),
                  BrandsRowWidget(),
                  SizedBox(height: 50.h),
                  const BannerWidget(
                    isTop: false,
                  ),
                  SizedBox(height: 50.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 16.h,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "home.bestOffers".tr(),
                              style: Styles.heading5.copyWith(
                                color: Colors.black,
                              ),
                            ),
                            BlocBuilder<HomeCubit, HomeState>(
                              buildWhen: (previous, current) =>
                                  current is GetBestOffersLoading ||
                                  current is GetBestOffersSuccess ||
                                  current is GetBestOffersLoadingMore ||
                                  current is GetBestOffersError,
                              builder: (context, state) {
                                return context
                                            .read<HomeCubit>()
                                            .bestOfferProductCardModel ==
                                        null
                                    ? ShowMoreSkeletonizerWidget()
                                    : ShowMoreWidget(
                                        onTapShowMore: () {
                                          Navigator.of(context).push(
                                            PageTransition(
                                              type: PageTransitionType.fade,
                                              child: BlocProvider(
                                                create: (context) =>
                                                    HomeCubit(getIt())
                                                      ..getBestOffers(),
                                                child: ShowAllBestOfferScreen(
                                                  title: "home.bestOffers".tr(),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      );
                              },
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 230.h,
                          child: BestOffersWidget(),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 32.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 16.h,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "home.bestSeller".tr(),
                              style: Styles.heading5.copyWith(
                                color: Colors.black,
                              ),
                            ),
                            BlocBuilder<HomeCubit, HomeState>(
                              buildWhen: (previous, current) =>
                                  current is GetBestSellerLoading ||
                                  current is GetBestSellerLoadingMore ||
                                  current is GetBestSellerSuccess ||
                                  current is GetBestSellerError,
                              builder: (context, state) {
                                return context
                                            .read<HomeCubit>()
                                            .bestSellerProductCardModel ==
                                        null
                                    ? ShowMoreSkeletonizerWidget()
                                    : ShowMoreWidget(
                                        onTapShowMore: () {
                                          Navigator.of(context).push(
                                            PageTransition(
                                              type: PageTransitionType.fade,
                                              child: BlocProvider(
                                                create: (context) =>
                                                    HomeCubit(getIt())
                                                      ..getBestSeller(),
                                                child: ShowAllBestSellerScreen(
                                                  title: "home.bestSeller".tr(),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      );
                              },
                            )
                          ],
                        ),
                        SizedBox(
                          height: 230.h,
                          child: BestSellerWidget(),
                        ),
                      ],
                    ),
                  ),
                  50.h.verticalSpace,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
