import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/home_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/search/search_grid_view_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/search/search_grid_view_widget_skeletonizer.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/search/search_list_view_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/search/search_list_view_widget_skeletonizer.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/screens/filter_screen.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/screens/search_screen.dart';

class ShowAllBestSellerScreen extends StatelessWidget {
  const ShowAllBestSellerScreen({
    super.key,
    required this.title,
  });

  final String title;

  @override
  Widget build(BuildContext context) {
    final homeCubit = context.read<HomeCubit>();

    /// Add listener for pagination
    homeCubit.scrollController.addListener(
      () {
        if (homeCubit.scrollController.position.pixels >=
            homeCubit.scrollController.position.maxScrollExtent - 100) {
          homeCubit.getBestSellerPagination();
        }
      },
    );

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 16.w,
              children: [
                BackButtonWidget(onTap: () => context.pop()),
                Text(
                  title,
                  style: Styles.heading2.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
              ],
            ),
          ),

          /// Search & Switch & Filter
          // Padding(
          //   padding: EdgeInsets.symmetric(horizontal: 16.w),
          //   child: Row(
          //     children: [
          //       Expanded(
          //         child: Padding(
          //           padding: EdgeInsets.only(
          //             top: 10.h,
          //             bottom: 10.h,
          //           ),
          //           child: CustomTextFormFieldWidget(
          //             backgroundColor: AppColors.neutralColor100,
          //             borderRadius: AppConstants.borderRadius + 2,
          //             borderColor: AppColors.neutralColor100,
          //             readOnly: true,
          //             onTap: () {
          //               // context.pushNamed(Routes.searchScreen);
          //               PersistentNavBarNavigator.pushNewScreen(
          //                 context,
          //                 screen: BlocProvider(
          //                   create: (context) => SearchCubit(getIt()),
          //                   child: SearchScreen(),
          //                 ),
          //                 withNavBar: true,
          //                 pageTransitionAnimation:
          //                     PageTransitionAnimation.cupertino,
          //               );
          //             },
          //             borderWidth: 1.w,
          //             prefixIcon: SvgPicture.asset(
          //               'assets/images/svgs/search_icon.svg',
          //               fit: BoxFit.scaleDown,
          //             ),
          //             hintText: 'home.whatAreYouThinkingAbout'.tr(),
          //             hintStyle: Styles.contentRegular.copyWith(
          //               color: AppColors.neutralColor1200,
          //             ),
          //           ),
          //         ),
          //       ),
          //       SizedBox(width: 12.w),
          //       BlocBuilder<HomeCubit, HomeState>(
          //         buildWhen: (previous, current) =>
          //             current is SearchModeToggleState,
          //         builder: (context, state) {
          //           if (!homeCubit.isListSearch) {
          //             return InkWell(
          //               onTap: () {
          //                 homeCubit.toggleSearchMode();
          //               },
          //               child: SvgPicture.asset(
          //                 'assets/images/svgs/four_dots_icon.svg',
          //                 fit: BoxFit.scaleDown,
          //               ),
          //             );
          //           } else {
          //             return InkWell(
          //               onTap: () {
          //                 homeCubit.toggleSearchMode();
          //               },
          //               child: SvgPicture.asset(
          //                 'assets/images/svgs/drawer_icon.svg',
          //                 fit: BoxFit.scaleDown,
          //                 // Assets.assetsImagesPngsIonList,
          //               ),
          //             );
          //           }
          //         },
          //       ),
          //       SizedBox(width: 16.w),
          //       InkWell(
          //         onTap: () {
          //           PersistentNavBarNavigator.pushNewScreen(
          //             context,
          //             screen: BlocProvider(
          //               create: (context) => SearchCubit(getIt()),
          //               child: SearchScreen(
          //                 isShowMore: true,
          //               ),
          //             ),
          //             withNavBar: true,
          //             pageTransitionAnimation: PageTransitionAnimation.fade,
          //           );
          //         },
          //         //context.pushNamed(Routes.filterScreen),
          //         child: Image.asset(
          //           Assets.assetsImagesPngsSortIcon,
          //           fit: BoxFit.scaleDown,
          //         ),
          //       ),
          //     ],
          //   ),
          // ),

          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: 10.h,
                      bottom: 10.h,
                    ),
                    child: CustomTextFormFieldWidget(
                      readOnly: true,
                      onTap: () {
                        PersistentNavBarNavigator.pushNewScreen(
                          context,
                          screen: BlocProvider(
                            create: (context) => SearchCubit(getIt()),
                            child: SearchScreen(),
                          ),
                          withNavBar: true,
                          pageTransitionAnimation:
                              PageTransitionAnimation.cupertino,
                        );
                      },
                      backgroundColor: AppColors.neutralColor100,
                      borderRadius: AppConstants.borderRadius + 2,
                      borderColor: AppColors.neutralColor100,
                      borderWidth: 1.w,
                      prefixIcon: SvgPicture.asset(
                        'assets/images/svgs/search_icon.svg',
                        fit: BoxFit.scaleDown,
                      ),
                      hintText: 'home.whatAreYouThinkingAbout'.tr(),
                      hintStyle: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                BlocBuilder<HomeCubit, HomeState>(
                  buildWhen: (previous, current) =>
                      current is SearchModeToggleState,
                  builder: (context, state) {
                    if (!homeCubit.isListSearch) {
                      return InkWell(
                        onTap: () {
                          homeCubit.toggleSearchMode();
                        },
                        child: SvgPicture.asset(
                          'assets/images/svgs/four_dots_icon.svg',
                          fit: BoxFit.scaleDown,
                        ),
                      );
                    } else {
                      return InkWell(
                        onTap: () {
                          homeCubit.toggleSearchMode();
                        },
                        child: SvgPicture.asset(
                          'assets/images/svgs/drawer_icon.svg',
                          fit: BoxFit.scaleDown,
                        ),
                      );
                    }
                  },
                ),
                SizedBox(width: 16.w),
                InkWell(
                  onTap: () {
                    PersistentNavBarNavigator.pushNewScreen(
                      context,
                      screen: BlocProvider(
                        create: (context) => SearchCubit(getIt()),
                        child: FilterScreen(),
                      ),
                      withNavBar: true,
                      pageTransitionAnimation: PageTransitionAnimation.fade,
                    );
                  },
                  //context.pushNamed(Routes.filterScreen),
                  child: Image.asset(
                    Assets.assetsImagesPngsSortIcon,
                    fit: BoxFit.scaleDown,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              controller: homeCubit.scrollController,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: BlocBuilder<HomeCubit, HomeState>(
                  buildWhen: (previous, current) =>
                      current is SearchModeToggleState ||
                      current is GetBestSellerLoading ||
                      current is GetBestSellerSuccess ||
                      current is GetBestSellerError ||
                      current is GetBestSellerLoadingMore,
                  builder: (context, state) {
                    if (homeCubit.isListSearch) {
                      return homeCubit.bestSellerProductCardModel == null ||
                              state is GetBestSellerLoading
                          ? SearchListViewWidgetSkeletonizer()
                          : SearchListViewWidget(
                              products: homeCubit
                                  .bestSellerProductCardModel!.data!.product!,
                            );
                    } else {
                      return homeCubit.bestSellerProductCardModel == null ||
                              state is GetBestSellerLoading
                          ? SearchGridViewWidgetSkeletonizer()
                          : SearchGridViewWidget(
                              products: homeCubit
                                  .bestSellerProductCardModel!.data!.product!,
                            );
                    }
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
