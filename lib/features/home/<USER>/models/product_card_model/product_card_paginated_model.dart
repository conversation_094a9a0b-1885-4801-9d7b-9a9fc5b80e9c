import 'package:json_annotation/json_annotation.dart';

part 'product_card_paginated_model.g.dart';

@JsonSerializable()
class ProductCardPaginatedModel {
  @JsonKey(name: 'data')
  ProductData? data;

  String? status;
  String? error;
  int? code;

  ProductCardPaginatedModel({this.data, this.status, this.error, this.code});

  factory ProductCardPaginatedModel.fromJson(Map<String, dynamic> json) =>
      _$ProductCardPaginatedModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProductCardPaginatedModelToJson(this);
}

@JsonSerializable()
class ProductData {
  @JsonKey(name: 'data')
  List<Product>? product;
  Links? links;
  Meta? meta;

  ProductData({this.product, this.links, this.meta});

  factory ProductData.fromJson(Map<String, dynamic> json) =>
      _$ProductDataFromJson(json);

  Map<String, dynamic> toJson() => _$ProductDataToJson(this);
}

@JsonSerializable()
class Product {
  int? id;
  String? name;
  String? thumbnail;
  num? price;
  num? discountRounded;
  num? priceAfterDiscount;
  Brand? brand;
  bool? isFavourite;
  int? rate;
  String? label;

  Product({
    this.id,
    this.name,
    this.thumbnail,
    this.price,
    this.discountRounded,
    this.priceAfterDiscount,
    this.brand,
    this.isFavourite,
    this.rate,
    this.label,
  });

  factory Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);

  Map<String, dynamic> toJson() => _$ProductToJson(this);
}

@JsonSerializable()
class Brand {
  String? title;
  String? thumbnail;

  Brand({this.title, this.thumbnail});

  factory Brand.fromJson(Map<String, dynamic> json) => _$BrandFromJson(json);

  Map<String, dynamic> toJson() => _$BrandToJson(this);
}

@JsonSerializable()
class Links {
  String? first;
  String? last;
  String? prev;
  String? next;

  Links({this.first, this.last, this.prev, this.next});

  factory Links.fromJson(Map<String, dynamic> json) => _$LinksFromJson(json);

  Map<String, dynamic> toJson() => _$LinksToJson(this);
}

@JsonSerializable()
class Meta {
  @JsonKey(name: 'current_page')
  int? currentPage;
  int? from;
  @JsonKey(name: 'last_page')
  int? lastPage;
  List<PageLink>? links;
  String? path;
  @JsonKey(name: 'per_page')
  int? perPage;
  int? to;
  int? total;

  Meta({
    this.currentPage,
    this.from,
    this.lastPage,
    this.links,
    this.path,
    this.perPage,
    this.to,
    this.total,
  });

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);

  Map<String, dynamic> toJson() => _$MetaToJson(this);
}

@JsonSerializable()
class PageLink {
  String? url;
  String? label;
  bool? active;

  PageLink({this.url, this.label, this.active});

  factory PageLink.fromJson(Map<String, dynamic> json) => _$PageLinkFromJson(json);

  Map<String, dynamic> toJson() => _$PageLinkToJson(this);
}