import 'package:json_annotation/json_annotation.dart';

part 'banner_data_model.g.dart';

@JsonSerializable()
class BannerDataModel {
  @JsonKey(name: 'data')
  List<BannerData>? banners;

  String? status;
  String? error;
  int? code;

  BannerDataModel({this.banners, this.status, this.error, this.code});

  factory BannerDataModel.fromJson(Map<String, dynamic> json) =>
      _$BannerDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$BannerDataModelToJson(this);
}

@JsonSerializable()
class BannerData {
  int? id;
  String? background;
  String? position;
  String? placement;
  int? modelId;
  String? linkType;
  String? linkTarget;

  BannerData({
    this.id,
    this.background,
    this.position,
    this.placement,
    this.modelId,
    this.linkType,
    this.linkTarget,
  });

  factory BannerData.fromJson(Map<String, dynamic> json) =>
      _$BannerDataFromJson(json);

  Map<String, dynamic> toJson() => _$BannerDataTo<PERSON>son(this);
}
