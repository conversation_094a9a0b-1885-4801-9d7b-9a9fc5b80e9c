// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'banner_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BannerDataModel _$BannerDataModelFromJson(Map<String, dynamic> json) =>
    BannerDataModel(
      banners: (json['data'] as List<dynamic>?)
          ?.map((e) => BannerData.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] as String?,
      error: json['error'] as String?,
      code: (json['code'] as num?)?.toInt(),
    );

Map<String, dynamic> _$Banner<PERSON>o<PERSON>son(BannerDataModel instance) =>
    <String, dynamic>{
      'data': instance.banners,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

BannerData _$BannerDataFromJson(Map<String, dynamic> json) => BannerData(
      id: (json['id'] as num?)?.toInt(),
      background: json['background'] as String?,
      position: json['position'] as String?,
      placement: json['placement'] as String?,
      modelId: (json['modelId'] as num?)?.toInt(),
      linkType: json['linkType'] as String?,
      linkTarget: json['linkTarget'] as String?,
    );

Map<String, dynamic> _$BannerDataToJson(BannerData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'background': instance.background,
      'position': instance.position,
      'placement': instance.placement,
      'modelId': instance.modelId,
      'linkType': instance.linkType,
      'linkTarget': instance.linkTarget,
    };
