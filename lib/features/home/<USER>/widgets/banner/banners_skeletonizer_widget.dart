import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/widgets/cache_network_image/imag_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/home_cubit.dart';

class BannersSkeletonizerWidget extends StatelessWidget {
  const BannersSkeletonizerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: CarouselSlider.builder(
        itemCount: 3,
        options: CarouselOptions(
          autoPlay: true,
          enlargeCenterPage: true,
          viewportFraction: 0.9,
          aspectRatio: 16.h / 8.h,
          enableInfiniteScroll: true,
          autoPlayAnimationDuration: const Duration(milliseconds: 800),
          autoPlayInterval: const Duration(seconds: 3),
          onPageChanged: (index, reason) {
            context.read<HomeCubit>().changeIndex(index);
          },
        ),
        itemBuilder: (context, index, realIndex) {
          return CacheNetworkImagesWidget(
            width: double.infinity,
            image:
                'https://developers.elementor.com/docs/assets/img/elementor-placeholder-image.png',
            boxFit: BoxFit.fill,
          );
        },
      ),
    );
  }
}
