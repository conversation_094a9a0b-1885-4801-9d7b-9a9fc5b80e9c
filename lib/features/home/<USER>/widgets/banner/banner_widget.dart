import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/cache_network_image/imag_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/home_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/banner/banner_data_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/banner/banners_skeletonizer_widget.dart';
import 'package:url_launcher/url_launcher.dart';

class BannerWidget extends StatelessWidget {
  const BannerWidget({super.key, required this.isTop});

  final bool isTop;

  @override
  Widget build(BuildContext context) {
    final HomeCubit homeCubit = context.read<HomeCubit>();

    void launchURL(BuildContext context, String url) async {
      final uri = Uri.tryParse(url);
      // if (uri != null && await canLaunchUrl(uri)) {
      await launchUrl(uri!, mode: LaunchMode.externalApplication);
      // }
      // else {
      // ScaffoldMessenger.of(context).showSnackBar(
      // SnackBar(content: Text('تعذر فتح الرابط')),
      // );
      // }
    }

    return BlocBuilder<HomeCubit, HomeState>(
      buildWhen: (previous, current) =>
          current is HomeIndexChanged ||
          current is ShowAllBannersLoading ||
          current is ShowAllBannersSuccess ||
          current is ShowAllBannersError,
      builder: (context, state) {
        if (homeCubit.bannerDataModel == null ||
            state is ShowAllBannersLoading) {
          return BannersSkeletonizerWidget();
        }

        final List<BannerData> banners = homeCubit.bannerDataModel!.banners!
            .where((banner) => banner.placement == (isTop ? 'top' : 'bottom'))
            .toList();

        return banners.isNotEmpty
            ? Column(
                children: [
                  CarouselSlider.builder(
                    itemCount: banners.length,
                    options: CarouselOptions(
                      autoPlay: true,
                      enlargeCenterPage: true,
                      viewportFraction: 0.9,
                      aspectRatio: 16.h / 8.h,
                      enableInfiniteScroll: true,
                      autoPlayAnimationDuration:
                          const Duration(milliseconds: 800),
                      autoPlayInterval: const Duration(seconds: 3),
                      onPageChanged: (index, reason) {
                        context.read<HomeCubit>().changeIndex(index);
                      },
                    ),
                    itemBuilder: (context, index, realIndex) {
                      final banner = banners[index];
                      return GestureDetector(
                        onTap: () {
                          final url = banner.linkTarget;
                          if (url != null && url.isNotEmpty) {
                            launchURL(context, url);
                          }
                        },
                        child: CacheNetworkImagesWidget(
                          image: banner.background!,
                          boxFit: BoxFit.fill,
                        ),
                      );
                    },
                  ),
                  SizedBox(height: 12.h),
                  AnimatedSmoothIndicator(
                    activeIndex: context.read<HomeCubit>().currentBannerIndex,
                    count: banners.length,
                    effect: WormEffect(
                      dotHeight: 6.h,
                      dotWidth: 6.w,
                      activeDotColor: AppColors.primaryColor900,
                      dotColor: AppColors.neutralColor200,
                    ),
                  ),
                ],
              )
            : SizedBox();
      },
    );
  }
}
