import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/brand/business_logic/brand_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand/presentation/widgets/brand_item_widget.dart';
import 'package:tegra_ecommerce_app/features/brand_details/business_logic/brand_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/brands/brand_skeletonizer_widget.dart';

class BrandsRowWidget extends StatelessWidget {
  const BrandsRowWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(
        horizontal: 15.w,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 16.h,
      ),
      decoration: BoxDecoration(
        color: AppColors.neutralColor100,
        borderRadius: BorderRadius.circular(
          AppConstants.borderRadius + 2,
        ),
      ),
      child: BlocProvider(
        create: (context) => BrandCubit(getIt())..getAllBrands(),
        child: BlocBuilder<BrandCubit, BrandState>(
          buildWhen: (previous, current) =>
              current is GetAllBrandsLoading ||
              current is GetAllBrandsSuccess ||
              current is GetAllBrandsError,
          builder: (context, state) {
            final brandCubit = context.read<BrandCubit>();

            return brandCubit.allBrandsByCategoryModel == null ||
                    state is GetAllBrandsLoading
                ? BrandSkeletonizerWidget()
                : brandCubit.allBrandsByCategoryModel!.brandsData == null ||
                        brandCubit
                            .allBrandsByCategoryModel!.brandsData!.data!.isEmpty
                    ? SizedBox()
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'home.brands'.tr(),
                            style: Styles.heading5.copyWith(
                              color: Color(0xff000000),
                            ),
                          ),
                          SizedBox(height: 16),
                          SizedBox(
                            height: 280.h,
                            child:
                            SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: List.generate(
                                  brandCubit.allBrandsByCategoryModel!
                                      .brandsData!.data!.length,
                                  (index) {
                                    final brand = brandCubit
                                        .allBrandsByCategoryModel!
                                        .brandsData!
                                        .data![index];

                                    return BlocProvider(
                                      create: (context) =>
                                          BrandDetailsCubit(getIt())
                                            ..getProductsByBrands(brand.id!),
                                      child: BrandItemWidget(
                                        isHome: true,
                                        brandData: brand,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
          },
        ),
      ),
    );
  }
}
