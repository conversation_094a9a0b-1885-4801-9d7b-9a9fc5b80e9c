import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/categories/category_scroll_widget.dart';

class CategoriesRowWidget extends StatelessWidget {
  const CategoriesRowWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: AppColors.neutralColor100,
        borderRadius: BorderRadius.circular(
          AppConstants.borderRadius + 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text( 
            'home.categories'.tr(),
            style: Styles.heading5.copyWith(
              color: Color(0xff000000),
            ),
          ),
          SizedBox(height: 16),
          CategoryScrollWidget(),
        ],
      ),
    );
  }
}
