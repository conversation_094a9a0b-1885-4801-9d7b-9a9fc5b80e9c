import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CategorySkeletonizerWidget extends StatelessWidget {
  const CategorySkeletonizerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: List.generate(
            5,
            (index) {
              return Column(
                spacing: 8.h,
                children: [
                  Container(
                    padding: EdgeInsets.all(5.63.sp),
                    child: Image.asset(
                      'assets/images/pngs/clothes_icon.png',
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
