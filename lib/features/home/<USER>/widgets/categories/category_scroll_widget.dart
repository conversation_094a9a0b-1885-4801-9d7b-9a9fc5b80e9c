import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/features/category/business_logic/category_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/categories/category_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/category_item_in_home_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/business_logic/product_by_categrory_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/presentation/screen/products_by_category_screen.dart';

class CategoryScrollWidget extends StatelessWidget {
  const CategoryScrollWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CategoryCubit(getIt())..getAllCategories(),
      child: BlocBuilder<CategoryCubit, CategoryState>(
        buildWhen: (previous, current) =>
            current is GetAllCategoriesLoading ||
            current is GetAllCategoriesSuccess ||
            current is GetAllCategoriesError,
        builder: (context, state) {
          final homeCubit = context.read<CategoryCubit>();

          return homeCubit.categoriesModel == null ||
                  state is GetAllCategoriesLoading
              ? CategorySkeletonizerWidget()
              : SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: homeCubit.categoriesModel!.categories == null ||
                          homeCubit.categoriesModel!.categories!.isEmpty
                      ? SizedBox()
                      : Row(
                          children: List.generate(
                            homeCubit.categoriesModel!.categories!.length,
                            (index) {
                              final category =
                                  homeCubit.categoriesModel!.categories![index];

                              return CategoryItemInHomeWidget(
                                imageUrl: category.image,
                                categoryName: category.name!,
                                // onTap: () => context.pushNamed(
                                //   Routes.productsByCategoryScreen,
                                //   arguments: category.id!,
                                // ),
                                onTap: () {
                                  // context.pushNamed(
                                  //   Routes.productsByCategoryScreen,
                                  //   arguments: ProductsByCategoryArgumets(
                                  //     categoryId: category.id!,
                                  //     name: category.name!,
                                  //   ),
                                  // );
                                  PersistentNavBarNavigator.pushNewScreen(
                                    context,
                                    screen: BlocProvider(
                                      create: (context) =>
                                          ProcutsByCategoryCubit(getIt())
                                            ..getChildrenCategories(
                                                category.id!)
                                            ..intController()
                                            ..getProcutsByCategory(
                                                categoryId: category.id!,
                                                page: 1),
                                      child: ProductsByCategoryScreen(
                                        categroyName: category.name!,
                                      ),
                                    ),
                                    withNavBar: true,
                                    pageTransitionAnimation:
                                        PageTransitionAnimation.fade,
                                  );
                                },
                              );
                            },
                          ),
                        ),
                );
        },
      ),
    );
  }
}
