import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/category_name_rotation_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/container/clip_container_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/gradient_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/total_rate_widget.dart';

class BestSellerSkeletonizerWidget extends StatelessWidget {
  const BestSellerSkeletonizerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: AppConstants.productGridViewImages.length,
        scrollDirection: Axis.horizontal,
        separatorBuilder: (context, index) {
          return SizedBox(width: 16.w);
        },
        itemBuilder: (context, index) {
          return Container(
            width: 150.w,
            height: 230.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              color: Colors.white,
              border: Border.all(color: AppColors.neutralColor200, width: 1.w),
            ),
            child: Column(
              spacing: 10.sp,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Container(
                    alignment: Alignment.topCenter,
                    height: 116.h,
                    child: Stack(
                      children: [
                        Stack(
                          children: [
                            /// Product Image
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(
                                  AppConstants.borderRadius,
                                ),
                              ),
                              child: Image.asset(
                                'assets/images/pngs/clothes_icon.png',
                                height: 69.h,
                                width: 69.w,
                                fit: BoxFit.cover,
                              ),
                            ),

                            /// Category Name
                            CategoryNameRotationWidget(
                              categoryName: 'تيشيرت',
                            ),
                          ],
                        ),

                        /// Clip Container
                        ClipContainerInSpecificProductWidget(),

                        /// Gradient
                        GradientInSpecificProductWidget(),

                        /// Favorite Icon
                        Positioned(
                          top: 8.h,
                          right: 8.w,
                          child: InkWell(
                            onTap: () {},
                            child: SvgPicture.asset(
                              'assets/images/svgs/favorite_icon.svg',
                              fit: BoxFit.scaleDown,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 8.w, right: 8.w, bottom: 16.h),
                  child: Column(
                    spacing: 5.sp,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TotalRateWidget(
                        rate: '4.5',
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            // homeCubit.productCardModel!
                            //     .products![index].name!,
                            'Sar 250',
                            style: Styles.captionRegular.copyWith(
                                color: Colors.black,
                                fontWeight: FontWeight.bold,
                                fontSize: 16.sp),
                          ),
                        ],
                      ),
                      Text(
                        'Sar 250',
                        style: Styles.contentRegular.copyWith(
                          color: AppColors.neutralColor600,
                        ),
                      ),
                      Row(
                        spacing: 5.sp,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(
                                AppConstants.borderRadius * 100),
                            child: Image.asset(
                              Assets.assetsImagesPngsPumaIcon,
                              fit: BoxFit.scaleDown,
                            ),
                          ),
                          Text(
                            'PUMA',
                            style: Styles.contentRegular,
                          )
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
