import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
// <<<<<<< edit_main_layout
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
// =======
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/routing/app_router.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
// >>>>>>> development
import 'package:tegra_ecommerce_app/features/home/<USER>/home_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/best_seller/best_seller_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_card_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_details_screen.dart';

class BestSellerWidget extends StatelessWidget {
  const BestSellerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      buildWhen: (previous, current) =>
          current is GetBestSellerSuccess ||
          current is GetBestSellerLoading ||
          current is GetBestSellerLoadingMore ||
          current is GetBestSellerError,
      builder: (context, state) {
        final homeCubit = context.read<HomeCubit>();

        return homeCubit.bestSellerProductCardModel == null ||
                state is GetBestSellerLoading
            ? BestSellerSkeletonizerWidget()
            : homeCubit.bestSellerProductCardModel!.data!.product == null ||
                    homeCubit.bestSellerProductCardModel!.data!.product!.isEmpty
                ? SizedBox()
                : ListView.separated(
                    shrinkWrap: true,
                    itemCount: homeCubit.bestSellerProductCardModel!.data!
                                .product!.length >
                            5
                        ? 5
                        : homeCubit
                            .bestSellerProductCardModel!.data!.product!.length,
                    scrollDirection: Axis.horizontal,
                    separatorBuilder: (context, index) {
                      return SizedBox(width: 16.w);
                    },
                    itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () {
                          logSuccess(
                              'product id: ${homeCubit.bestSellerProductCardModel!.data!.product![index].id}');
// <<<<<<< edit_main_layout

                          PersistentNavBarNavigator.pushNewScreen(
                            context,
                            screen: BlocProvider(
                              create: (context) => ProductDetailsCubit(getIt())
                                ..showProductDetails(homeCubit
                                    .bestSellerProductCardModel!
                                    .data!
                                    .product![index]
                                    .id!)
                                ..loadSimilarProducts(homeCubit
                                    .bestSellerProductCardModel!
                                    .data!
                                    .product![index]
                                    .id!)
                                ..getProductReviews(homeCubit
                                    .bestSellerProductCardModel!
                                    .data!
                                    .product![index]
                                    .id!),
                              child: ProductDetailsScreen(),
                            ),
                            withNavBar: true,
                            pageTransitionAnimation:
                                PageTransitionAnimation.cupertino,
// =======
                          // context.pushNamed(
                          //   Routes.productsDetailsScreen,
                          //   arguments: homeCubit
                          //       .bestSellerProductCardModel!.data!.product![index].id,
                          // );
//                           context.pushNamed(
//                             Routes.productsDetailsScreen,
//                             arguments: ProductDetailsArguments(
//                                 productId: homeCubit.bestSellerProductCardModel!
//                                     .data!.product![index].id!),
// >>>>>>> development
                          );
                        },
                        child: ProductCardWidget(
                          productItem: homeCubit.bestSellerProductCardModel!
                              .data!.product![index],
// <<<<<<< edit_main_layout
// =======
                          // onTapFavorite: () {},
// >>>>>>> development
                        ),
                      );
                    },
                  );
      },
    );
  }
}
