import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/category_name_rotation_widget.dart';

class ProductListItemWidget extends StatelessWidget {
  const ProductListItemWidget({
    super.key,
    required this.productCard,
  });

  final Product productCard;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        color: Colors.white,
        border: Border.all(color: AppColors.neutralColor200, width: 1.w),
      ),
      padding: EdgeInsets.all(16.sp),
      child: SizedBox(
        width: 100.sp,
        height: 100.sp,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 98.w,
              height: 100.h,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppConstants.borderRadius),
                      topRight: Radius.circular(AppConstants.borderRadius),
                    ),
                    child: CachedNetworkImage(
                      imageUrl: productCard.thumbnail ?? '',
                      fit: BoxFit.scaleDown,
                      errorWidget: (context, url, error) => Center(
                        child: Icon(Icons.error),
                      ),
                    ),
                  ),
                  CategoryNameRotationWidget(
                    inListViewInSearch: true,
                    categoryName: productCard.label ?? '',
                  ),
                  Positioned(
                    bottom: 0.sp,
                    left: 0,
                    right: 0,
                    child: Image.asset(
                      Assets.assetsImagesPngsProductStackImage,
                      fit: BoxFit.fill,
                    ),
                  ),
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(AppConstants.borderRadius),
                          topRight: Radius.circular(AppConstants.borderRadius),
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withAlpha((0.16 * 255).toInt()),
                            Colors.black.withAlpha((0.0 * 255).toInt()),
                          ],
                          stops: [0.0, 1.0],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            12.horizontalSpace,
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'SAR ${productCard.price}',
                        style: Styles.captionRegular.copyWith(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 16.sp,
                        ),
                      ),
                      Spacer(),
                      Icon(
                        Icons.star,
                        color: AppColors.yellowColor100,
                        size: 20.sp,
                      ),
                      Text(
                        productCard.rate!.toString(),
                        style: Styles.contentEmphasis
                            .copyWith(color: Colors.black),
                      ),
                    ],
                  ),
                  Text(
                    productCard.name!,
                    style: Styles.contentRegular.copyWith(
                      color: AppColors.neutralColor600,
                    ),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(100),
                        child: CachedNetworkImage(
                          imageUrl: productCard.brand!.thumbnail!,
                          height: 20.h,
                          width: 20.w,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) => Center(
                            child: Icon(Icons.error),
                          ),
                        ),
                      ),
                      5.horizontalSpace,
                      Text(productCard.brand!.title!,
                          style: Styles.contentRegular),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
