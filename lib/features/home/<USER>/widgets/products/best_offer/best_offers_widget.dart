import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
// <<<<<<< edit_main_layout
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
// >>>>>>/> development
import 'package:tegra_ecommerce_app/features/home/<USER>/home_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/best_seller/best_seller_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_card_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_details_screen.dart';

class BestOffersWidget extends StatelessWidget {
  const BestOffersWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(
      buildWhen: (previous, current) =>
          current is GetBestOffersSuccess ||
          current is GetBestOffersLoading ||
          current is GetBestOffersLoadingMore ||
          current is GetBestOffersError,
      builder: (context, state) {
        final homeCubit = context.read<HomeCubit>();

        return homeCubit.bestOfferProductCardModel == null ||
                state is GetBestOffersLoading
            ? BestSellerSkeletonizerWidget()
            : homeCubit.bestOfferProductCardModel!.data!.product == null ||
                    homeCubit.bestOfferProductCardModel!.data!.product!.isEmpty
                ? SizedBox()
                : ListView.separated(
                    shrinkWrap: true,
                    itemCount: homeCubit.bestOfferProductCardModel!.data!
                                .product!.length >
                            5
                        ? 5
                        : homeCubit
                            .bestOfferProductCardModel!.data!.product!.length,
                    scrollDirection: Axis.horizontal,
                    separatorBuilder: (context, index) {
                      return 16.horizontalSpace;
                    },
                    itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () {
                          logSuccess(
                            'product id: ${homeCubit.bestOfferProductCardModel!.data!.product![index].id}',
                          );
// <<<<<<< edit_main_layout
                          // context.pushNamed(
                          //   Routes.productsDetailsScreen,
                          //   arguments: homeCubit
                          //       .bestOfferProductCardModel!.data!.product![index].id,
                          // );
                          PersistentNavBarNavigator.pushNewScreen(
                            context,
                            screen: BlocProvider(
                              create: (context) => ProductDetailsCubit(getIt())
                                ..showProductDetails(homeCubit
                                    .bestOfferProductCardModel!
                                    .data!
                                    .product![index]
                                    .id!)
                                ..loadSimilarProducts(homeCubit
                                    .bestOfferProductCardModel!
                                    .data!
                                    .product![index]
                                    .id!)
                                ..getProductReviews(homeCubit
                                    .bestOfferProductCardModel!
                                    .data!
                                    .product![index]
                                    .id!),
                              child: ProductDetailsScreen(),
                            ),
                            withNavBar: true,
                            pageTransitionAnimation:
                                PageTransitionAnimation.cupertino,
// =======
//                           context.pushNamed(
//                             Routes.productsDetailsScreen,
//                             arguments: ProductDetailsArguments(
//                                 productId: homeCubit.bestOfferProductCardModel!
//                                     .data!.product![index].id!),
// >>>>>>> development
                          );
                          // context.pushNamed(
                          //   Routes.productsDetailsScreen,
                          //   arguments: homeCubit.bestOfferProductCardModel!
                          //       .data!.product![index].id,
                          // );
                        },
                        child: ProductCardWidget(
                          productItem: homeCubit
                              .bestOfferProductCardModel!.data!.product![index],
                        ),
                      );
                    },
                  );
      },
    );
  }
}
