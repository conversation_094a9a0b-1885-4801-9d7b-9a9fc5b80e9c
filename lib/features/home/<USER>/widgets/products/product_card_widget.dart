import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/container/clip_container_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/gradient_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/total_rate_widget.dart';
import 'package:tegra_ecommerce_app/features/favorite/bloc/cubit/favorite_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/category_name_rotation_widget.dart';

class ProductCardWidget extends StatelessWidget {
  const ProductCardWidget({
    super.key,
    required this.productItem,
  });

  final Product productItem;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 150.w,
      height: 230.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          AppConstants.borderRadius,
        ),
        color: Colors.white,
        border: Border.all(
          color: AppColors.neutralColor200,
          width: 1.w,
        ),
      ),
      child: Column(
        spacing: 8.sp,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Container(
              alignment: Alignment.topCenter,
              height: 180.h,
              child: Stack(
                children: [
                  Stack(
                    children: [
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                            AppConstants.borderRadius,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(
                              AppConstants.borderRadius,
                            ),
                            topRight: Radius.circular(
                              AppConstants.borderRadius,
                            ),
                          ),
                          child:
                           CachedNetworkImage(
                            imageUrl: productItem.thumbnail??'',
                            width: 69.w,
                            fit: BoxFit.cover,
                             placeholder: (context, url) =>
                                const CircularProgressIndicator(),
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.error),
                          ),
                        ),
                      ),
                      CategoryNameRotationWidget(
                        categoryName: productItem.label??'',
                      ),
                    ],
                  ),
                  ClipContainerInSpecificProductWidget(),
                  GradientInSpecificProductWidget(),
                  Positioned(
                    top: 8.h,
                    right: 8.w,
                    child: BlocProvider(
                      create: (context) => FavoriteCubit(getIt()),
                      child: BlocBuilder<FavoriteCubit, FavoriteState>(
                        builder: (context, state) {
                          return InkWell(
                            onTap: () {
                              BlocProvider.of<FavoriteCubit>(context)
                                  .addToFavorites(productId: productItem.id!);
                              productItem.isFavourite =
                                  !productItem.isFavourite!;
                            },
                            child: SvgPicture.asset(
                              productItem.isFavourite!
                                  ? Assets.assetsImagesSvgsIsFavoriteIcon
                                  : 'assets/images/svgs/favorite_icon.svg',
                              fit: BoxFit.scaleDown,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 8.w, right: 8.w, bottom: 16.h),
            child: Column(
              spacing: 4.sp,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  spacing: 10.w,
                  children: [
                    Text(
                      'Sar ${productItem.priceAfterDiscount!.toString()}',
                      style: Styles.captionRegular.copyWith(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 16.sp,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    TotalRateWidget(
                      rate: productItem.rate!.toString(),
                    ),
                  ],
                ),
                productItem.priceAfterDiscount == null
                    ? const SizedBox()
                    : Text(
                        'Sar ${productItem.price!.toString()}',
                        style: Styles.captionEmphasis.copyWith(
                          color: AppColors.redColor200,
                          decoration: TextDecoration.lineThrough,
                          decorationColor: AppColors.redColor200,
                          decorationThickness: .8.h,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                Text(
                  productItem.name!,
                  style: Styles.contentRegular.copyWith(
                    color: AppColors.neutralColor600,
                  ),
                ),
                Row(
                  spacing: 5.sp,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(50.r),
                      child: CachedNetworkImage(
                        imageUrl: productItem.brand!.thumbnail??'',
                        height: 20.h,
                        width: 20.w,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Text(
                      productItem.brand!.title??'',
                      style: Styles.contentRegular,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
