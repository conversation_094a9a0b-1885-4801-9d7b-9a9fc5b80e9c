import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/category_name_rotation_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_details_screen.dart';

class ProductGridViewItemWidget extends StatelessWidget {
  const ProductGridViewItemWidget(
      {super.key, required this.productCard, this.favoriteTap});

  final Product productCard;
  final VoidCallback? favoriteTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // context.pushNamed(
        //   Routes.productsDetailsScreen,
        //   arguments: ProductDetailsArguments(productId: productCard.id!),
        // );
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: BlocProvider(
            create: (context) => ProductDetailsCubit(getIt())
              ..showProductDetails(productCard.id!)
              ..loadSimilarProducts(productCard.id!)
              ..getProductReviews(productCard.id!),
            child: ProductDetailsScreen(),
          ),
          withNavBar: true,
          pageTransitionAnimation: PageTransitionAnimation.cupertino,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          color: Colors.white,
          border: Border.all(color: AppColors.neutralColor200, width: 1.w),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 10.sp,
          children: [
            Expanded(
              child: Container(
                alignment: Alignment.topCenter,
                // color: Colors.blue,
                height: 150.h,
                child: Stack(
                  children: [
                    Stack(
                      children: [
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(
                                AppConstants.borderRadius,
                              ),
                              topRight: Radius.circular(
                                AppConstants.borderRadius,
                              ),
                            ),
                            child: CachedNetworkImage(
                              imageUrl: productCard.thumbnail!,
                              fit: BoxFit.fill,
                              errorWidget: (context, url, error) => Center(
                                child: Icon(Icons.error),
                              ),
                            ),
                          ),
                        ),

                        /// Category Name
                        CategoryNameRotationWidget(
                          categoryName: 'home.tShit'.tr(),
                        ),
                      ],
                    ),
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0.sp,
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child: Image.asset(
                          fit: BoxFit.fill,
                          Assets.assetsImagesPngsProductStackImage,
                        ),
                      ),
                    ),

                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(AppConstants.borderRadius),
                            topRight:
                                Radius.circular(AppConstants.borderRadius),
                          ),
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withAlpha((0.16 * 255).toInt()),
                              Colors.black.withAlpha((0.0 * 255).toInt()),
                            ],
                            stops: [0.0, 1.0],
                          ),
                        ),
                      ),
                    ),

                    /// Favorite Icon
                    Positioned(
                      top: 8.h,
                      right: 8.w,
                      child: InkWell(
                        onTap: favoriteTap,
                        child: SvgPicture.asset(
                          Assets.assetsImagesSvgsIsFavoriteIcon,
                          fit: BoxFit.scaleDown,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 8.w, right: 8.w, bottom: 16.h),
              child: Column(
                spacing: 5.sp,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'SAR ${productCard.priceAfterDiscount!}',
                        style: Styles.captionRegular.copyWith(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 16.sp,
                        ),
                      ),
                      Spacer(),
                      Icon(
                        Icons.star,
                        color: AppColors.yellowColor100,
                        size: 20.sp,
                      ),
                      Text(
                        productCard.rate!.toString(),
                        style: Styles.contentEmphasis
                            .copyWith(color: Colors.black),
                      ),
                    ],
                  ),
                  Text(
                    productCard.name!,
                    style: Styles.contentRegular.copyWith(
                      color: AppColors.neutralColor600,
                    ),
                  ),
                  Row(
                    spacing: 5.sp,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(50.r),
                        child: CachedNetworkImage(
                          imageUrl: productCard.brand!.thumbnail!,
                          height: 20.h,
                          width: 20.w,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) => Center(
                            child: Icon(Icons.error),
                          ),
                        ),
                      ),
                      Text(
                        productCard.brand!.title!,
                        style: Styles.contentRegular,
                      ),
                    ],
                  ),
                  // 5.verticalSpace,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
