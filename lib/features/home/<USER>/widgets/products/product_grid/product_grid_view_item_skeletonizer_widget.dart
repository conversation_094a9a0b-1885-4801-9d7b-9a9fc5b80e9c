import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/category_name_rotation_widget.dart';

class ProductGridViewItemSkeletonizerWidget extends StatelessWidget {
  const ProductGridViewItemSkeletonizerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          color: Colors.white,
          border: Border.all(color: AppColors.neutralColor200, width: 1.w),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 10.sp,
          children: [
            Expanded(
              child: Container(
                alignment: Alignment.topCenter,
                height: 150.h,
                child: Stack(
                  children: [
                    Stack(
                      children: [
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Image.asset(
                            Assets.assetsImagesPngsProduct1Image,
                            width: double.infinity,
                            fit: BoxFit.fill,
                          ),
                        ),

                        /// Category Name
                        CategoryNameRotationWidget(
                          categoryName: 'home.tShit'.tr(),
                        ),
                      ],
                    ),
                    Positioned(
                      bottom: 0.sp,
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child: Image.asset(
                          fit: BoxFit.fill,
                          Assets.assetsImagesPngsProductStackImage,
                        ),
                      ),
                    ),

                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(AppConstants.borderRadius),
                            topRight:
                                Radius.circular(AppConstants.borderRadius),
                          ),
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withAlpha((0.16 * 255).toInt()),
                              Colors.black.withAlpha((0.0 * 255).toInt()),
                            ],
                            stops: [0.0, 1.0],
                          ),
                        ),
                      ),
                    ),

                    /// Favorite Icon
                    Positioned(
                      top: 8.h,
                      right: 8.w,
                      child: InkWell(
                        onTap: () {},
                        child: SvgPicture.asset(
                          'assets/images/svgs/favorite_icon.svg',
                          fit: BoxFit.scaleDown,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 8.w, right: 8.w, bottom: 16.h),
              child: Column(
                spacing: 5.sp,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'SAR 544',
                        style: Styles.captionRegular.copyWith(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 16.sp,
                        ),
                      ),
                      Spacer(),
                      Icon(
                        Icons.star,
                        color: AppColors.yellowColor100,
                        size: 20.sp,
                      ),
                      Text(
                        '4.2',
                        style: Styles.contentEmphasis
                            .copyWith(color: Colors.black),
                      ),
                    ],
                  ),
                  Text(
                    'asdgfasdg',
                    style: Styles.contentRegular.copyWith(
                      color: AppColors.neutralColor600,
                    ),
                  ),
                  Row(
                    spacing: 5.sp,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(
                          AppConstants.borderRadius * 100,
                        ),
                        child: Image.asset(
                          Assets.assetsImagesPngsBrand1Image,
                          fit: BoxFit.cover,
                        ),
                      ),
                      5.horizontalSpace,
                      Expanded(
                        child: Text(
                          'ssdgfsdgsdjioadgsd',
                          style: Styles.contentRegular,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  // 5.verticalSpace,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
