import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class CategoryNameRotationWidget extends StatelessWidget {
  const CategoryNameRotationWidget({
    super.key,
    required this.categoryName,
    this.inListViewInSearch = false,
  });

  final String categoryName;
  final bool inListViewInSearch;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 10.w,
      bottom: inListViewInSearch ? 16.h : 25.h,
      child: Container(
        transform: Matrix4.rotationZ(8.93 * pi / 180),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            AppConstants.borderRadius / 2,
          ),
          color: AppColors.secondaryColor600.withValues(alpha: .7),
        ),
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
        child: Text(
          categoryName,
          style: Styles.footnoteEmphasis.copyWith(
            color: AppColors.scaffoldBackground,
          ),
        ),
      ),
    );
  }
}