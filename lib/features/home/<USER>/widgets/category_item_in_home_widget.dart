import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class CategoryItemInHomeWidget extends StatelessWidget {
  final String? imageUrl;
  final String categoryName;
  final VoidCallback onTap;

  const CategoryItemInHomeWidget({
    super.key,
    required this.imageUrl,
    required this.categoryName,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(5.63.sp),
            child: imageUrl == null
                ? Image.asset(
                    'assets/images/pngs/clothes_icon.png',
                    height: 69.h,
                    width: 69.w,
                  )
                : ClipRRect(
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius - 3,
                    ),
                    child: CachedNetworkImage(
                      imageUrl: imageUrl!,
                      height: 69.h,
                      width: 69.w,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) => Center(
                        child: Icon(Icons.error),
                      ),
                    ),
                  ),
          ),
          SizedBox(
            width: 68.w,
            child: Text(
              categoryName,
              textAlign: TextAlign.center,
              style: Styles.highlightEmphasis.copyWith(
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          )
        ],
      ),
    );
  }
}
