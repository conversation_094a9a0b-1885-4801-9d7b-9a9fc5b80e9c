import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';

class SubFilterInProductByCategoryWidget extends StatelessWidget {
  const SubFilterInProductByCategoryWidget({
    super.key,
    required this.title,
    this.isLastSubFilter = false,
  });

  final String title;
  final bool isLastSubFilter;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          title,
          style: Styles.highlightStandard.copyWith(
            color: Colors.black,
          ),
        ),
        SizedBox(width: 4.w),
        SvgPicture.asset(
          'assets/images/svgs/down_icon2.svg',
          fit: BoxFit.scaleDown,
        ),
        SizedBox(width: 10.w),
        if (!isLastSubFilter)
          SvgPicture.asset(
            'assets/images/svgs/vertical_divider.svg',
            fit: BoxFit.scaleDown,
          ),
        SizedBox(width: 10.w),
      ],
    );
  }
}
