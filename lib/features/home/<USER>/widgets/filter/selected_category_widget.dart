import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class SelectedCategoryWidget extends StatelessWidget {
  const SelectedCategoryWidget(
      {super.key,
      required this.title,
      this.isSelected = false,
      this.starImage,
      required this.onTap,

      required this.index, required this.selectedIndex});

  final String title;
  final bool isSelected;
  final String? starImage;
  final VoidCallback onTap;
  final int index;

  final  int selectedIndex;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius + 2.r),
          border: Border.all(
            color: isSelected
                ? AppColors.primaryColor900
                : AppColors.neutralColor200,
          ),
          color: AppColors.scaffoldBackground,
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 14.w,
              height: 14.h,
              child: Radio(
                value: index,
                groupValue: selectedIndex,

                onChanged: (newValue) {
                  onTap();
                },
                activeColor: Color(0xFF006638),
              ),
            ),
            12.horizontalSpace,
            Text(
              title,
              style: Styles.contentEmphasis.copyWith(
                color: AppColors.neutralColor1200,
              ),
            ),
            if (starImage != null)
              Padding(
                padding: EdgeInsets.only(right: 10.h),
                child: SvgPicture.asset(
                  starImage ?? "",
                ),
              )
          ],
        ),
      ),
    );
  }
}
