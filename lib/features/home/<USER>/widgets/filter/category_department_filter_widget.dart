import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/category/business_logic/category_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/filter/selected_category_widget.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';

class CategoryDepartmentFilterWidget extends StatelessWidget {
  const CategoryDepartmentFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final homeCubit = context.read<SearchCubit>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "home.categories".tr(),
          style: Styles.heading5.copyWith(
            color: Colors.black,
          ),
        ),
        12.verticalSpace,
        BlocProvider(
          create: (context) => CategoryCubit(getIt())..getAllCategories(),
          child: BlocBuilder<CategoryCubit, CategoryState>(
            builder: (context, state) {
              if (state is GetAllCategoriesLoading) {
                return CategorySkeltonWidget();
              } else {
                return Wrap(
                  spacing: 12.w,
                  runSpacing: 12.h,
                  children: List.generate(
                    BlocProvider.of<CategoryCubit>(context)
                            .categoriesModel!
                            .categories!
                            .length +
                        1,
                    (index) {
                      // الحالة الخاصة بالـ "كل الفئات"
                      if (index == 0) {
                        return BlocBuilder<SearchCubit, SearchState>(
                          buildWhen: (previous, current) =>
                              current is FilterCategorySelected,
                          builder: (context, state) {
                            return SelectedCategoryWidget(
                              title: 'search.allCategories'.tr(),
                              onTap: () {
                                homeCubit.selectCategory(
                                    0, -1); // null = no filter
                              },
                              isSelected: homeCubit.categoryIndex == 0,
                              index: 0,
                              selectedIndex: homeCubit.categoryIndex,
                            );
                          },
                        );
                      }

                      final category = BlocProvider.of<CategoryCubit>(context)
                              .categoriesModel!
                              .categories![
                          index - 1]; // index - 1 to offset for "All"

                      return BlocBuilder<SearchCubit, SearchState>(
                        buildWhen: (previous, current) =>
                            current is FilterCategorySelected,
                        builder: (context, state) {
                          return SelectedCategoryWidget(
                            title: category.name!,
                            onTap: () {
                              homeCubit.selectCategory(index, category.id!);
                            },
                            isSelected: homeCubit.categoryIndex == index,
                            index: index,
                            selectedIndex: homeCubit.categoryIndex,
                          );
                        },
                      );
                    },
                  ),
                  //  List.generate(
                  //   BlocProvider.of<CategoryCubit>(context)
                  //       .categoriesModel!
                  //       .categories!
                  //       .length,
                  //   (index) {
                  //     return BlocBuilder<SearchCubit, SearchState>(
                  //       buildWhen: (previous, current) =>
                  //           current is FilterCategorySelected,
                  //       builder: (context, state) {
                  //         return SelectedCategoryWidget(
                  //           title: BlocProvider.of<CategoryCubit>(context)
                  //               .categoriesModel!
                  //               .categories![index]
                  //               .name!,
                  //           onTap: () {
                  //             homeCubit.selectCategory(
                  //                 index,
                  //                 BlocProvider.of<CategoryCubit>(context)
                  //                     .categoriesModel!
                  //                     .categories![index]
                  //                     .id!);
                  //           },
                  //           isSelected: homeCubit.categoryIndex == index,
                  //           index: index,
                  //           selectedIndex: homeCubit.categoryIndex,
                  //         );
                  //       },
                  //     );
                  //   },
                  // ),
                );
              }
            },
          ),
        ),
      ],
    );
  }
}

class CategorySkeltonWidget extends StatelessWidget {
  const CategorySkeltonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Wrap(
        spacing: 12.w,
        runSpacing: 12.h,
        children: List.generate(
          AppConstants.categories.length,
          (index) {
            return SelectedCategoryWidget(
              title: AppConstants.categories[index],
              onTap: () {},
              isSelected: false,
              index: index,
              selectedIndex: 0,
            );
          },
        ),
      ),
    );
  }
}
