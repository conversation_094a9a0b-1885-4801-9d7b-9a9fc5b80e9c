import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';

class PriceFromANdToWidget extends StatelessWidget {
  const PriceFromANdToWidget(
      {super.key, required this.controller, required this.text});
  final TextEditingController controller;
  final String text;
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 5.sp),
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
        ),
        decoration: BoxDecoration(
          color: AppColors.neutralColor100,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius + 2),
        ),
        child: Row(
          children: [
            Text(
              '$text :',
              style: Styles.contentEmphasis.copyWith(
                color: AppColors.neutralColor1200,
              ),
            ),
            Expanded(
              child: CustomTextFormFieldWidget(
                keyboardType: TextInputType.number,
                contentPadding: EdgeInsets.zero,
                width: 5.w,
                borderColor: Colors.transparent,
                backgroundColor: Colors.transparent,
                controller: controller,
                hintText: ' 00',
                hintStyle: Styles.contentEmphasis.copyWith(
                  color: AppColors.neutralColor500,
                ),
              ),
            ),
            Text(
              'filter.riyal'.tr(),
              style: Styles.captionRegular.copyWith(
                color: AppColors.neutralColor600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
