import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/filter/selected_category_widget.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';

class CategoryOfferFilterWidget extends StatelessWidget {
  const CategoryOfferFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final homeCubit = context.read<SearchCubit>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "filter.theOffer".tr(),
          style: Styles.heading5.copyWith(
            color: Colors.black,
          ),
        ),
        12.verticalSpace,
        Wrap(
          spacing: 12.w,
          runSpacing: 12.h,
          children: List.generate(
            AppConstants.categoriesgender.length,
            (index) {
              return BlocBuilder<SearchCubit, SearchState>(
                buildWhen: (previous, current) =>
                    current is FilterOfferSelected,
                builder: (context, state) {
                  return SelectedCategoryWidget(
                    title: AppConstants.categoriesgender[index],
                    onTap: () {
                      homeCubit.selectOffer(index);
                    },
                    isSelected: homeCubit.offerSelectedIndex == index,
                    index: index,
                    selectedIndex: homeCubit.offerSelectedIndex,
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
