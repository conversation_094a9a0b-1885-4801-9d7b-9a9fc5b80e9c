import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/container/clip_container_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/gradient_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/total_rate_widget.dart';

class BrandSkeletonizerWidget extends StatelessWidget {
  const BrandSkeletonizerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'home.brands'.tr(),
            style: Styles.heading5.copyWith(
              color: Color(0xff000000),
            ),
          ),
          SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(
                AppConstants.brandsCoversImage.length,
                (index) {
                  return Container(
                    width: 218.w,
                    height: 286.h,
                    padding: EdgeInsets.only(left: 10.w),
                    child: Column(
                      spacing: 8.h,
                      children: [
                        Expanded(
                          child: Container(
                            alignment: Alignment.topCenter,
                            height: 116.h,
                            child: Stack(
                              clipBehavior: Clip.none,
                              // Ensure CircleAvatar is not clipped
                              children: [
                                /// Product Image
                                Container(
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                        AppConstants.borderRadius),
                                  ),
                                  child: Image.asset(
                                    'assets/images/pngs/clothes_icon.png',
                                    height: 69.h,
                                    width: 69.w,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                  
                                /// Clip Container Overlay (Moves Behind the Logo)
                                ClipContainerInSpecificProductWidget(),
                  
                                /// Gradient Overlay (Moves Behind the Logo)
                                GradientInSpecificProductWidget(),
                  
                                /// Puma Icon (Circle Avatar) - Ensuring it's above overlays
                                Positioned(
                                  bottom: -20.r,
                                  // Moves avatar outside the bottom of the image
                                  left: 0,
                                  right: 0,
                                  child: CircleAvatar(
                                    radius: 40.r,
                                    backgroundColor: Colors.transparent,
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(
                                        40.r,
                                      ),
                                      child: Image.asset(
                                        'assets/images/pngs/clothes_icon.png',
                                        height: 69.h,
                                        width: 69.w,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(horizontal: 8.w),
                          child: Column(
                            spacing: 4.h,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    "testtesttest",
                                    style: Styles.captionEmphasis.copyWith(
                                      color: Colors.black,
                                    ),
                                  ),
                                  TotalRateWidget(
                                    rate: "2",
                                  ),
                                ],
                              ),
                              Text(
                                " testtesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttest",
                                style: Styles.captionRegular.copyWith(
                                  color: AppColors.neutralColor600,
                                ),
                                maxLines: 5,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                spacing: 4.w,
                                children: [
                                  Text(
                                    'home.showProducts'.tr(),
                                    style: Styles.captionRegular.copyWith(
                                      color: Colors.black,
                                    ),
                                  ),
                                  Icon(
                                    Icons.arrow_forward_ios_rounded,
                                    color: AppColors.neutralColor1200,
                                    size: 14.sp,
                                  )
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
