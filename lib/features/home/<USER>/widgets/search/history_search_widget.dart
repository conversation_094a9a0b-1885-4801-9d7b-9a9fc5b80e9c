import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';

class HistorySearchWidget extends StatelessWidget {
  const HistorySearchWidget({
    super.key,
    required this.productSearchedName,
    required this.onClearItemInSearchFunction,
  });

  final String productSearchedName;
  final VoidCallback onClearItemInSearchFunction;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        InkWell(
          onTap: onClearItemInSearchFunction,
          child: SvgPicture.asset(
            'assets/images/svgs/close_icon.svg',
            fit: BoxFit.scaleDown,
          ),
        ),
        SizedBox(width: 10.w),
        SizedBox(
          width: MediaQuery.sizeOf(context).width * 0.8,
          child: Text(
            productSearchedName,
            style: Styles.highlightEmphasis.copyWith(
              color: AppColors.neutralColor700,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
