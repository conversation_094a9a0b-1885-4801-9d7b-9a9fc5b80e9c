import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/search/history_search_widget.dart';

class SearchHistory extends StatelessWidget {
  const SearchHistory({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
        ),
        child: SizedBox(
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 30.h),
              Text(
                'search.searchHistory'.tr(),
                style: Styles.heading5.copyWith(
                  color: Colors.black,
                ),
              ),
              SizedBox(height: 20.h),
              Expanded(
                child: ListView.separated(
                  shrinkWrap: true,
                  itemCount: AppConstants.searchHistoryList.length,
                  padding: EdgeInsets.zero,
                  separatorBuilder: (context, index) {
                    return SizedBox(height: 12.h);
                  },
                  itemBuilder: (context, index) {
                    return HistorySearchWidget(
                      productSearchedName:
                          AppConstants.searchHistoryList[index],
                      onClearItemInSearchFunction: () {},
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
