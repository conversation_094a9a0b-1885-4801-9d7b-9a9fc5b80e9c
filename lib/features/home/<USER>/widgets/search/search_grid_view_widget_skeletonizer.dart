import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_grid/product_grid_view_item_skeletonizer_widget.dart';

class SearchGridViewWidgetSkeletonizer extends StatelessWidget {
  const SearchGridViewWidgetSkeletonizer({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: GridView.builder(
        physics: NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisExtent: 220.sp,
          crossAxisSpacing: 40.sp,
          mainAxisSpacing: 25.sp,
        ),
        itemCount: AppConstants.productGridViewImages.length,
        itemBuilder: (BuildContext context, int index) {
          return ProductGridViewItemSkeletonizerWidget();
        },
      ),
    );
  }
}
