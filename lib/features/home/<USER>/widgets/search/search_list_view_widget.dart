import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart' show BlocProvider;
import 'package:flutter_screenutil/flutter_screenutil.dart';
// <<<<<<< edit_main_layout
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart'
    show PersistentNavBarNavigator;
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart'
    show getIt;
// =======
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/routing/app_router.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
// >>>>>>> development
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_list/product_list_item_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart'
    show ProductDetailsCubit;
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_details_screen.dart'
    show ProductDetailsScreen;

class SearchListViewWidget extends StatelessWidget {
  const SearchListViewWidget({
    super.key,
    required this.products,
  });

  final List<Product> products;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemCount: products.length,
      shrinkWrap: true,
      separatorBuilder: (BuildContext context, int index) {
        return 25.verticalSpace;
      },
      itemBuilder: (BuildContext context, int index) {
        return GestureDetector(
// <<<<<<< edit_main_layout
          // onTap: () => context.pushNamed(
          //   Routes.productsDetailsScreen,
          //   arguments: products[index].id,
          // ),
          onTap: () => PersistentNavBarNavigator.pushNewScreen(
            context,
            screen: BlocProvider(
              create: (context) => ProductDetailsCubit(getIt())
                ..showProductDetails(products[index].id!)
                ..loadSimilarProducts(products[index].id!)
                ..getProductReviews(products[index].id!),
              child: ProductDetailsScreen(),
            ),
// =======
//           onTap: () => context.pushNamed(
//             Routes.productsDetailsScreen,
//             arguments: ProductDetailsArguments(productId: products[index].id!),
// >>>>>>> development
          ),
          // context.pushNamed(
          //   Routes.productsDetailsScreen,
          //   arguments: products[index].id,
          // ),
          child: ProductListItemWidget(
            productCard: products[index],
          ),
        );
      },
    );
  }
}
