import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:page_transition/page_transition.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/search/search_grid_view_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/search/search_list_view_widget.dart';
import 'package:tegra_ecommerce_app/features/notification/presentation/widgets/day_history_widget.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/screens/filter_screen.dart';

class SearchResultsWidget extends StatelessWidget {
  const SearchResultsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final searchCubit = context.read<SearchCubit>();

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Column(
        children: [
          SizedBox(height: 30.h),
          Row(
            spacing: 10.sp,
            children: [
              Text(
                "search.searchResults".tr(),
                style: Styles.highlightEmphasis.copyWith(
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                ),
              ),
              Spacer(),
              BlocBuilder<SearchCubit, SearchState>(
                buildWhen: (previous, current) =>
                    current is SearchModeToggleStateSearch,
                builder: (context, state) {
                  if (!searchCubit.isListSearch) {
                    return InkWell(
                      onTap: () {
                        searchCubit.toggleSearchMode();
                      },
                      child: SvgPicture.asset(
                        'assets/images/svgs/four_dots_icon.svg',
                        fit: BoxFit.scaleDown,
                      ),
                    );
                  } else {
                    return InkWell(
                      onTap: () {
                        searchCubit.toggleSearchMode();
                      },
                      child: SvgPicture.asset(
                        'assets/images/svgs/drawer_icon.svg',
                        fit: BoxFit.scaleDown,
                      ),
                    );
                  }
                },
              ),
              InkWell(
                onTap: () {
                  Navigator.of(context).push(
                    PageTransition(
                      type: PageTransitionType.fade,
                      child: BlocProvider.value(
                        value: searchCubit,
                        child: FilterScreen(),
                      ),
                    ),
                  );
                },
                child: Image.asset(
                  Assets.assetsImagesPngsSortIcon,
                  fit: BoxFit.scaleDown,
                ),
              ),
            ],
          ),
          SizedBox(height: 26.h),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  BlocBuilder<SearchCubit, SearchState>(
                    buildWhen: (previous, current) =>
                        current is SearchModeToggleStateSearch,
                    builder: (context, state) {
                      if (searchCubit.isListSearch) {
                        return SearchListViewWidget(
                          products: searchCubit
                              .searchCardProductModel!.data!.product!,
                        );
                      } else {
                        return SearchGridViewWidget(
                          products: searchCubit
                              .searchCardProductModel!.data!.product!,
                        );
                      }
                    },
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 25.sp),
                    child: DayHistoryWidget(
                      dayHistoryText: 'search.resultsAreOut'.tr(),
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
