import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_list/product_list_item_skeletonizer_widget.dart';

class SearchListViewWidgetSkeletonizer extends StatelessWidget {
  const SearchListViewWidgetSkeletonizer({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: ListView.separated(
        physics: NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        itemCount: 10,
        shrinkWrap: true,
        separatorBuilder: (BuildContext context, int index) {
          return 25.verticalSpace;
        },
        itemBuilder: (BuildContext context, int index) {
          return ProductListItemSkeletonWidget();
        },
      ),
    );
  }
}
