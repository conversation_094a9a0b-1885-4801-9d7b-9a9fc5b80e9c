import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/banner/banner_data_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';

class HomeRepository {
  final HomeApiServices homeApiServices;

  HomeRepository(this.homeApiServices);

  /// Show All Banners
  Future<ApiResult<BannerDataModel>> showAllBanners() async {
    final response = await homeApiServices.showAllBanners();

    try {
      if (response!.statusCode == 200) {
        BannerDataModel bannerDataModel =
            BannerDataModel.fromJson(response.data);

        return ApiResult.success(bannerDataModel);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  /// Get Best Seller
  Future<ApiResult<ProductCardPaginatedModel>> getBestSeller() async {
    final response = await homeApiServices.getBestSeller();
    try {
      if (response!.statusCode == 200) {
        ProductCardPaginatedModel productCardModel =
            ProductCardPaginatedModel.fromJson(response.data);

        return ApiResult.success(productCardModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Get Best Offers
  Future<ApiResult<ProductCardPaginatedModel>> getBestOffers() async {
    final response = await homeApiServices.getBestOffers();
    try {
      if (response!.statusCode == 200) {
        ProductCardPaginatedModel productCardModel =
            ProductCardPaginatedModel.fromJson(response.data);
        return ApiResult.success(productCardModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Get Products By Category
  Future<ApiResult<ProductCardPaginatedModel>> getProductsByCategory({
    required int categoryId,
  }) async {
    final response = await homeApiServices.getProductsByCategory(
      categoryId: categoryId,
    );
    try {
      if (response!.statusCode == 200) {
        ProductCardPaginatedModel productCardModel =
            ProductCardPaginatedModel.fromJson(response.data);
        return ApiResult.success(productCardModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  }
