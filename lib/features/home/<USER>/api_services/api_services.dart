import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class HomeApiServices {
  HomeApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Show All Banners
  Future<Response?> showAllBanners() async {
    return _dioFactory.get(
      endPoint: EndPoints.showAllBanners,
    );
  }

  /// Get Best Seller
  Future<Response?> getBestSeller() async {
    return _dioFactory.get(
      endPoint: EndPoints.getBestSeller,
    );
  }

  /// Get Best offers
  Future<Response?> getBestOffers() async {
    return _dioFactory.get(
      endPoint: EndPoints.getBestOffers,
    );
  }

  Future<Response?> getProductsByCategory({
    required int categoryId,
  }) async {
    return _dioFactory.get(
      endPoint: EndPoints.getProductsByCategory(categoryId),
    );
  }


}
