import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/payment/data/api_services/api_services.dart';

class PaymentRepo {
  final PaymentApiServices paymentApiServices;

  PaymentRepo(this.paymentApiServices);

  /// Add Order
  Future<ApiResult<String>> addOrder({
    required int addressId,
    required String paymentMethodId,
    String? orderNotes,
  }) async {
    final response = await paymentApiServices.addOrder(
      addressId: addressId,
      paymentMethodId: paymentMethodId,
      orderNotes: orderNotes,
    );
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success('Order Added Successfully');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
      return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'),
      );
    }
  }
}