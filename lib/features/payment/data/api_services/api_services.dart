import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class PaymentApiServices {
  final DioHelper _dioFactory;

  PaymentApiServices(this._dioFactory);

  /// Add Order
  Future<Response?> addOrder({
    required int addressId,
    required String paymentMethodId,
    String? orderNotes,
  }) async {
    return _dioFactory.post(endPoint: EndPoints.addOrder, data: {
      'address_id': addressId,
      'payment_method': paymentMethodId,
      if (orderNotes != null) 'order_notes': orderNotes
    });
  }
}