import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_helper.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_keys.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/features/cart/data/model/cart_recipt_model.dart';
import 'package:tegra_ecommerce_app/features/main%20layout/business_logic/main_layout_cubit.dart';
import 'package:tegra_ecommerce_app/features/main%20layout/main_layout.dart';
import 'package:tegra_ecommerce_app/features/payment/business_logic/payment_cubit.dart';
import 'package:tegra_ecommerce_app/features/payment/business_logic/payment_state.dart';
import 'package:tegra_ecommerce_app/features/payment/presentation/widgets/add_address_widget.dart';
import 'package:tegra_ecommerce_app/features/payment/presentation/widgets/category_payment_widget.dart';

class PaymentScreen extends StatelessWidget {
  const PaymentScreen({super.key, required this.data});

  final Data data;

  @override
  Widget build(BuildContext context) {
    CacheHelper.removeData(key: CacheKeys.addressId);
    print(CacheHelper.getData(key: CacheKeys.addressId));
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: BlocProvider<PaymentCubit>(
        create: (context) => PaymentCubit(getIt()),
        child: Column(
          children: [
            AppBarWidget(
              rowWidget: Row(
                spacing: 16.w,
                children: [
                  BackButtonWidget(onTap: () => context.pop()),
                  Text(
                    'payment.payment'.tr(),
                    style: Styles.heading2.copyWith(
                      color: AppColors.scaffoldBackground,
                    ),
                  ),
                ],
              ),
            ),
            30.verticalSpace,
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.all(12.sp),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.neutralColor600),
                          borderRadius:
                              BorderRadius.circular(AppConstants.borderRadius),
                          color: AppColors.neutralColor100,
                        ),
                        child: Row(
                          children: [
                            Text(
                              'payment.totalPrice'.tr(),
                              style: Styles.highlightEmphasis.copyWith(
                                color: AppColors.neutralColor600,
                              ),
                            ),
                            Spacer(),
                            Text(
                              "SAR ${data.total}",
                              style: Styles.heading3.copyWith(
                                color: AppColors.neutralColor1200,
                                height: 2.h,
                              ),
                            ),
                          ],
                        ),
                      ),
                      20.verticalSpace,
                      AddAddressWidget(),
                      20.verticalSpace,
                      CategoryPaymentWidget(
                        paymentMethod: "cash",
                      ),
                      34.verticalSpace,
                      BlocConsumer<PaymentCubit, PaymentState>(
                        listener: (context, state) {
                          if (state is AddOrderSuccess) {
                            PersistentNavBarNavigator.pushDynamicScreen(
                              context,
                              screen: MaterialPageRoute(
                                builder: (context) => BlocProvider(
                                  create: (context) => MainLayoutCubit(),
                                  child: MainLayoutScreen(),
                                ),
                              ),
                              withNavBar: false,
                            );
                          }
                        },
                        builder: (context, state) {
                          return CustomButtonWidget(
                            text: "payment.payment".tr(),
                            onPressed: () {
                              print(CacheHelper.getData(
                                  key: CacheKeys.addressId));
                              if (context
                                      .read<PaymentCubit>()
                                      .paymentSelectedCategoryIndex ==
                                  -1) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content:
                                        Text("payment.chooseYourPayment".tr()),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              } else {
                                if (CacheHelper.getData(
                                        key: CacheKeys.addressId) ==
                                    null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                          "payment.selectAddressBeforeCheckout"
                                              .tr()),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                } else {
                                  context.read<PaymentCubit>().addOrder(
                                        addressId: CacheHelper.getData(
                                            key: CacheKeys.addressId),
                                        paymentMethod: 'cash',
                                      );
                                }
                              }
                            },
                          );
                        },
                      ),
                      34.verticalSpace,
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
