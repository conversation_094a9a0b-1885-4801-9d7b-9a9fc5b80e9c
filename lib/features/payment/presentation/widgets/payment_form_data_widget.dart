import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';

class PaymentFormDataWidget extends StatelessWidget {
  const PaymentFormDataWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomTextFormFieldWidget(
          borderRadius: AppConstants.borderRadius,
          backgroundColor: AppColors.neutralColor100,
          borderColor: AppColors.neutralColor200,
          labelText: "payment.fullName".tr(),
          labelStyle: Styles.contentEmphasis.copyWith(
            color: AppColors.neutralColor1200,
          ),
        ),
        16.verticalSpace,
        CustomTextFormFieldWidget(
          borderRadius: AppConstants.borderRadius,
          backgroundColor: AppColors.neutralColor100,
          borderColor: AppColors.neutralColor200,
          labelText: "payment.cardNumber".tr(),
          labelStyle: Styles.contentEmphasis.copyWith(
            color: AppColors.neutralColor1200,
          ),
        ),
        16.verticalSpace,
        Row(
          children: [
            Expanded(
              child: CustomTextFormFieldWidget(
                borderRadius: AppConstants.borderRadius,
                backgroundColor: AppColors.neutralColor100,
                borderColor: AppColors.neutralColor200,
                labelText: "CVV/CVC",
                labelStyle: Styles.contentEmphasis.copyWith(
                  color: AppColors.neutralColor1200,
                ),
              ),
            ),
            12.horizontalSpace,
            Expanded(
              child: CustomTextFormFieldWidget(
                borderRadius: AppConstants.borderRadius,
                backgroundColor: AppColors.neutralColor100,
                borderColor: AppColors.neutralColor200,
                labelText: "payment.expireDate".tr(),
                labelStyle: Styles.contentEmphasis.copyWith(
                  color: AppColors.neutralColor1200,
                ),
              ),
            ),
          ],
        ),
        16.verticalSpace,
        CustomTextFormFieldWidget(
          borderRadius: AppConstants.borderRadius,
          // height: 80.h,
          backgroundColor: AppColors.neutralColor100,
          borderColor: AppColors.neutralColor200,
          labelText: "payment.notes".tr(),
          maxLines: 7,
          labelStyle: Styles.contentEmphasis.copyWith(
            color: AppColors.neutralColor1200,
          ),
        ),
      ],
    );
  }
}
