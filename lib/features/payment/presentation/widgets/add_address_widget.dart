import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_helper.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_keys.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/address/business_logic/cubit/address_cubit.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/screens/add_new_address_screen.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/screens/all_address_screen.dart';

class AddAddressWidget extends StatelessWidget {
  AddAddressWidget({super.key});

  final ValueNotifier<String?> selectedAddressCityNotifier =
      ValueNotifier(CacheHelper.getData(key: CacheKeys.addressCity));

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AddressCubit(getIt())..getAllAddresses(),
      child: Column(
        children: [
          ValueListenableBuilder<String?>(
            valueListenable: selectedAddressCityNotifier,
            builder: (context, savedAddressCity, _) {
         
              return BlocBuilder<AddressCubit, AddressState>(
                builder: (context, state) {
                  final addressesCubit = context.read<AddressCubit>();

                  if (addressesCubit.addressesModel == null ||
                      state is GetAllAddressesLoading) {
                    return Skeletonizer(
                      enabled: true,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 12.sp, horizontal: 8.sp),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                              AppConstants.borderRadius + 2),
                          border: Border.all(color: AppColors.primaryColor900),
                        ),
                        child: Row(
                          children: [
                            Column(
                              spacing: 4.sp,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "العنوان",
                                  style: Styles.contentEmphasis.copyWith(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 16.sp,
                                  ),
                                ),
                                Text(
                                  'جار التحميل...',
                                  style: Styles.contentEmphasis.copyWith(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14.sp,
                                    color: AppColors.neutralColor600,
                                  ),
                                ),
                              ],
                            ),
                            Spacer(),
                            Icon(
                              size: 16.sp,
                              Icons.arrow_forward_ios_outlined,
                              color: AppColors.primaryColor900,
                            )
                          ],
                        ),
                      ),
                    );
                  }

                  if (addressesCubit.addressesModel == null ||
                      addressesCubit.addressesModel!.data.data.isEmpty) {
                    return Container(
                      padding: EdgeInsets.symmetric(
                          vertical: 12.sp, horizontal: 8.sp),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                            AppConstants.borderRadius + 2),
                        border: Border.all(color: AppColors.primaryColor900),
                      ),
                      child: Row(
                        children: [
                          Column(
                            spacing: 4.sp,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'payment.addNewAddress'.tr(),
                                style: Styles.contentEmphasis.copyWith(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 16.sp,
                                ),
                              ),
                              Text(
                                'payment.newDeliveryAddress'.tr(),
                                style: Styles.contentEmphasis.copyWith(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14.sp,
                                  color: AppColors.neutralColor600,
                                ),
                              ),
                            ],
                          ),
                          Spacer(),
                          Icon(
                            size: 16.sp,
                            Icons.arrow_forward_ios_outlined,
                            color: AppColors.primaryColor900,
                          )
                        ],
                      ),
                    );
                  }

                  return GestureDetector(
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(
                        context,
                        screen: BlocProvider(
                          create: (context) =>
                              AddressCubit(getIt())..getAllAddresses(),
                          child: const AllAddressScreen(),
                        ),
                        withNavBar: true,
                        pageTransitionAnimation:
                            PageTransitionAnimation.cupertino,
                      ).then((_) {
                        selectedAddressCityNotifier.value =
                            CacheHelper.getData(key: CacheKeys.addressCity);
                        context.read<AddressCubit>().getAllAddresses();
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          vertical: 12.sp, horizontal: 8.sp),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                            AppConstants.borderRadius + 2),
                        border: Border.all(color: AppColors.primaryColor900),
                      ),
                      child: Row(
                        children: [
                          Column(
                            spacing: 4.sp,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "payment.address".tr(),
                                style: Styles.contentEmphasis.copyWith(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 16.sp,
                                ),
                              ),
                              // Text(
                              //   savedAddressCity ??
                              //       "payment.noAddressSelected".tr(),
                              //   style: Styles.contentEmphasis.copyWith(
                              //     fontWeight: FontWeight.w400,
                              //     fontSize: 14.sp,
                              //     color: AppColors.neutralColor600,
                              //   ),
                              // ),
                              Text(
                                savedAddressCity ??
                                    "payment.noAddressSelected".tr(),
                                style: Styles.contentEmphasis.copyWith(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14.sp,
                                  color: AppColors.neutralColor600,
                                ),
                              ),
                            ],
                          ),
                          Spacer(),
                          Icon(
                            size: 16.sp,
                            Icons.arrow_forward_ios_outlined,
                            color: AppColors.primaryColor900,
                          )
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
          12.verticalSpace,
          BlocBuilder<AddressCubit, AddressState>(
            builder: (context, state) {
              return GestureDetector(
                onTap: () {
                  PersistentNavBarNavigator.pushNewScreen(
                    context,
                    screen: BlocProvider(
                      create: (context) => AddressCubit(getIt()),
                      child: AddNewAddressScreen(),
                    ),
                    withNavBar: true,
                    pageTransitionAnimation: PageTransitionAnimation.cupertino,
                  ).then((value) {
                    context.read<AddressCubit>().getAllAddresses();
                  });
                },
                child: Container(
                  width: double.infinity,
                  padding:
                      EdgeInsets.symmetric(vertical: 14.sp, horizontal: 16.sp),
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(AppConstants.borderRadius - 2),
                    border: Border.all(color: AppColors.primaryColor900),
                  ),
                  child: Text(
                    textAlign: TextAlign.center,
                    "payment.addAddress".tr(),
                    style: Styles.contentEmphasis
                        .copyWith(color: AppColors.primaryColor900),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
