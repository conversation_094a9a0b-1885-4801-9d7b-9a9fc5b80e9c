import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/filter/selected_category_widget.dart';
import 'package:tegra_ecommerce_app/features/payment/business_logic/payment_cubit.dart';
import 'package:tegra_ecommerce_app/features/payment/business_logic/payment_state.dart';
import 'package:tegra_ecommerce_app/features/payment/presentation/widgets/payment_form_data_widget.dart';

class CategoryPaymentWidget extends StatelessWidget {
  const CategoryPaymentWidget({
    super.key,
    required this.paymentMethod,
  });

  final String paymentMethod;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PaymentCubit, PaymentState>(
      buildWhen: (previous, current) => current is PaymentCategorySelected,
      builder: (context, state) {
        final paymentCubit = context.read<PaymentCubit>();
        final categoriesOfPayment = [
          'payment.cash'.tr(),
          'payment.credit'.tr(),
        ];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              // paymentMethod,
              'payment.paymentMethod'.tr(),
              style:
                  Styles.heading5.copyWith(color: AppColors.neutralColor1200),
            ),
            12.verticalSpace,
            Wrap(
              spacing: 12.w,
              runSpacing: 12.h,
              children: List.generate(
                categoriesOfPayment.length,
                (index) {
                  return SelectedCategoryWidget(
                    title: categoriesOfPayment[index],
                    onTap: () {
                      if (index != 1) {
                        context
                            .read<PaymentCubit>()
                            .selectCategorypayment(index);
                      }
                    },
                    isSelected:
                        paymentCubit.paymentSelectedCategoryIndex == index,
                    index: index,
                    selectedIndex: paymentCubit.paymentSelectedCategoryIndex,
                  );
                },
              ),
            ),

            if (paymentCubit.paymentSelectedCategoryIndex == 0) ...[
              20.verticalSpace,
              CustomTextFormFieldWidget(
                controller: paymentCubit.orderNotesController,
                borderRadius: AppConstants.borderRadius,
                // height: 80.h,
                backgroundColor: AppColors.neutralColor100,
                borderColor: AppColors.neutralColor200,
                labelText: "payment.notes".tr(),
                maxLines: 7,
                labelStyle: Styles.contentEmphasis.copyWith(
                  color: AppColors.neutralColor1200,
                ),
              ),
            ],

            /// Show PaymentFormDataWidget only when selected index is 1 or 2
            if (paymentCubit.paymentSelectedCategoryIndex == 1 ||
                paymentCubit.paymentSelectedCategoryIndex == 2) ...[
              20.verticalSpace,
              PaymentFormDataWidget(),
            ],
          ],
        );
      },
    );
  }
}
