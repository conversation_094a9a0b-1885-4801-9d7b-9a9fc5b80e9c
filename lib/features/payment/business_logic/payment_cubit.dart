import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/features/payment/business_logic/payment_state.dart';
import 'package:tegra_ecommerce_app/features/payment/data/repos/payment_repo.dart';

class PaymentCubit extends Cubit<PaymentState> {
  PaymentCubit(this.paymentRepository) : super(PaymentaInitial());

  final PaymentRepo paymentRepository;
  final TextEditingController orderNotesController = TextEditingController();

  int paymentSelectedCategoryIndex = -1;
  void selectCategorypayment(int index) {
    paymentSelectedCategoryIndex = index;
    emit(PaymentCategorySelected());
  }

  /// Add Order
  Future<void> addOrder({
    required int addressId,
    required String paymentMethod,
  }) async {
    showLoading();
    emit(AddOrderLoading());
    final result = await paymentRepository.addOrder(
      addressId: addressId,
      paymentMethodId: paymentMethod,
      orderNotes: orderNotesController.text,
    );
    result.when(success: (data) {
      hideLoading();
      emit(AddOrderSuccess());
    }, failure: (error) {
      hideLoading();
      emit(AddOrderError());
    });
  }

  @override
  Future<void> close() {
    orderNotesController.dispose();
    return super.close();
  }
}
