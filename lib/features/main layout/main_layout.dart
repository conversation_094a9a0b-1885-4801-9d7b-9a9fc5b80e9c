// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
// import 'package:tegra_ecommerce_app/core/routing/app_router.dart';
// import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
// import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
// import 'package:tegra_ecommerce_app/features/main%20layout/business_logic/main_layout_cubit.dart';
// import 'package:tegra_ecommerce_app/features/main%20layout/business_logic/main_layout_state.dart';

// class MainLayoutScreen extends StatelessWidget {
//   const MainLayoutScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final controller = PersistentTabController(initialIndex: 0);
//     List<PersistentBottomNavBarItem> navBarsItems() {
//       return [
//         PersistentBottomNavBarItem(
//           icon: SvgPicture.asset(
//             'assets/images/svgs/home_selected_icon.svg',
//             fit: BoxFit.scaleDown,
//             colorFilter: ColorFilter.mode(
//               AppConstants.mainLayoutInitialScreenIndex == 0
//                   ? AppColors.primaryColor900
//                   : AppColors.neutralColor1200,
//               BlendMode.srcIn,
//             ),
//           ),
//           title: 'mainLayout.home'.tr(),
//           activeColorPrimary: AppColors.primaryColor900,
//           inactiveColorPrimary: AppColors.neutralColor1200,
//           routeAndNavigatorSettings: RouteAndNavigatorSettings(
//             initialRoute: "/",
//           ),
//         ),
//         PersistentBottomNavBarItem(
//           icon: SvgPicture.asset(
//             'assets/images/svgs/category_icon.svg',
//             fit: BoxFit.scaleDown,
//             colorFilter: ColorFilter.mode(
//               AppConstants.mainLayoutInitialScreenIndex == 0
//                   ? AppColors.primaryColor900
//                   : AppColors.neutralColor1200,
//               BlendMode.srcIn,
//             ),
//           ),
//           title: 'home.categories'.tr(),
//           activeColorPrimary: AppColors.primaryColor900,
//           inactiveColorPrimary: AppColors.neutralColor1200,
//           routeAndNavigatorSettings: RouteAndNavigatorSettings(
//             initialRoute: "/",
//           ),
//         ),
//         PersistentBottomNavBarItem(
//           icon: SvgPicture.asset(
//             'assets/images/svgs/cart_icon.svg',
//             fit: BoxFit.scaleDown,
//             colorFilter: ColorFilter.mode(
//               AppConstants.mainLayoutInitialScreenIndex == 0
//                   ? AppColors.primaryColor900
//                   : AppColors.neutralColor1200,
//               BlendMode.srcIn,
//             ),
//           ),
//           title: "",
//           activeColorPrimary: AppColors.primaryColor900,
//           inactiveColorPrimary: AppColors.neutralColor1200,
//           routeAndNavigatorSettings: RouteAndNavigatorSettings(
//             initialRoute: "/",
//           ),
//         ),
//         PersistentBottomNavBarItem(
//           icon: SvgPicture.asset(
//             'assets/images/svgs/brands_icon.svg',
//             fit: BoxFit.scaleDown,
//             colorFilter: ColorFilter.mode(
//               AppConstants.mainLayoutInitialScreenIndex == 0
//                   ? AppColors.primaryColor900
//                   : AppColors.neutralColor1200,
//               BlendMode.srcIn,
//             ),
//           ),
//           title: 'mainLayout.brands'.tr(),
//           activeColorPrimary: AppColors.primaryColor900,
//           inactiveColorPrimary: AppColors.neutralColor1200,
//           routeAndNavigatorSettings: RouteAndNavigatorSettings(
//             initialRoute: "/",
//           ),
//         ),
//         PersistentBottomNavBarItem(
//           icon: SvgPicture.asset(
//             'assets/images/svgs/more_un_selected_icon.svg',
//             fit: BoxFit.scaleDown,
//             colorFilter: ColorFilter.mode(
//               AppConstants.mainLayoutInitialScreenIndex == 0
//                   ? AppColors.primaryColor900
//                   : AppColors.neutralColor1200,
//               BlendMode.srcIn,
//             ),
//           ),
//           title: 'mainLayout.more'.tr(),
//           activeColorPrimary: AppColors.primaryColor900,
//           inactiveColorPrimary: AppColors.neutralColor1200,
//           routeAndNavigatorSettings: RouteAndNavigatorSettings(
//             initialRoute: "/",
//             routes: {},
//           ),
//         ),
//       ];
//     }

//     return BlocBuilder<MainLayoutCubit, MainLayoutState>(
//         builder: (BuildContext context, state) {
//       return PersistentTabView(
//         context,
//         controller: controller,
//         screens: AppRouter().screen,
//         items: navBarsItems(),
//         handleAndroidBackButtonPress: true,
//         resizeToAvoidBottomInset: true,
//         stateManagement: true,
//         hideNavigationBarWhenKeyboardAppears: true,

//         // padding: const EdgeInsets.only(top: 8),
//         isVisible: true,
//         // animationSettings: const NavBarAnimationSettings(
//         //   navBarItemAnimation: ItemAnimationSettings(),
//         //   screenTransitionAnimation: ScreenTransitionAnimationSettings(),
//         // ),

//         confineToSafeArea: true,
//         navBarHeight: kBottomNavigationBarHeight,
//       );
//     });
//   }
// }

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/routing/app_router.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/main%20layout/business_logic/main_layout_cubit.dart';
import 'package:tegra_ecommerce_app/features/main%20layout/business_logic/main_layout_state.dart';

class MainLayoutScreen extends StatelessWidget {
  const MainLayoutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = PersistentTabController(
        initialIndex: AppConstants.mainLayoutInitialScreenIndex);

    // List<PersistentBottomNavBarItem> navBarsItems() {
    //   return [
    //     PersistentBottomNavBarItem(
    //       icon: const Icon(Icons.home),
    //       title: 'mainLayout.home'.tr(),
    //       activeColorPrimary: AppColors.primaryColor900,
    //       inactiveColorPrimary: AppColors.neutralColor1200,
    //     ),
    //     PersistentBottomNavBarItem(
    //       icon: const Icon(Icons.category),
    //       title: 'home.categories'.tr(),
    //       activeColorPrimary: AppColors.primaryColor900,
    //       inactiveColorPrimary: AppColors.neutralColor1200,
    //     ),
    //     PersistentBottomNavBarItem(
    //       icon: const Icon(Icons.shopping_cart),
    //       title: "",
    //       activeColorPrimary: AppColors.primaryColor900,
    //       inactiveColorPrimary: AppColors.neutralColor1200,
    //     ),
    //     PersistentBottomNavBarItem(
    //       icon: const Icon(Icons.business),
    //       title: 'mainLayout.brands'.tr(),
    //       activeColorPrimary: AppColors.primaryColor900,
    //       inactiveColorPrimary: AppColors.neutralColor1200,
    //     ),
    //     PersistentBottomNavBarItem(
    //       icon: const Icon(Icons.more_horiz),
    //       title: 'mainLayout.more'.tr(),
    //       activeColorPrimary: AppColors.primaryColor900,
    //       inactiveColorPrimary: AppColors.neutralColor1200,
    //     ),
    //   ];
    // }

    List<PersistentBottomNavBarItem> navBarsItems() {
      return [
        PersistentBottomNavBarItem(
          icon: Padding(
            padding: EdgeInsets.all(4.sp),
            child: SvgPicture.asset(
              'assets/images/svgs/home_selected_icon.svg',
              fit: BoxFit.scaleDown,
              colorFilter: ColorFilter.mode(
                AppConstants.mainLayoutInitialScreenIndex == 0
                    ? AppColors.primaryColor900
                    : AppColors.neutralColor1200,
                BlendMode.srcIn,
              ),
            ),
          ),
          title: 'mainLayout.home'.tr(),
          textStyle: Styles.contentRegular,
          activeColorPrimary: AppColors.primaryColor900,
          inactiveColorPrimary: AppColors.neutralColor1200,
          routeAndNavigatorSettings: RouteAndNavigatorSettings(
            initialRoute: "/",
          ),
        ),
        PersistentBottomNavBarItem(
          icon: Padding(
            padding: EdgeInsets.all(4.sp),
            child: SvgPicture.asset(
              'assets/images/svgs/category_icon.svg',
              fit: BoxFit.scaleDown,
              colorFilter: ColorFilter.mode(
                AppConstants.mainLayoutInitialScreenIndex == 0
                    ? AppColors.primaryColor900
                    : AppColors.neutralColor1200,
                BlendMode.srcIn,
              ),
            ),
          ),
          title: 'home.categories'.tr(),
          textStyle: Styles.contentRegular,
          activeColorPrimary: AppColors.primaryColor900,
          inactiveColorPrimary: AppColors.neutralColor1200,
          routeAndNavigatorSettings: RouteAndNavigatorSettings(
            initialRoute: "/",
          ),
        ),
        PersistentBottomNavBarItem(
          icon: Padding(
            padding: EdgeInsets.all(4.sp),
            child: SvgPicture.asset(
              'assets/images/svgs/cart_icon.svg',
              fit: BoxFit.scaleDown,
              colorFilter: ColorFilter.mode(
                AppConstants.mainLayoutInitialScreenIndex == 0
                    ? AppColors.primaryColor900
                    : AppColors.neutralColor1200,
                BlendMode.srcIn,
              ),
            ),
          ),
          title: 'home.cart'.tr(),
          textStyle: Styles.contentRegular,
          activeColorPrimary: AppColors.primaryColor900,
          inactiveColorPrimary: AppColors.neutralColor1200,
          routeAndNavigatorSettings: RouteAndNavigatorSettings(
            initialRoute: "/",
          ),
        ),
        PersistentBottomNavBarItem(
          icon: Padding(
            padding: EdgeInsets.all(4.sp),
            child: SvgPicture.asset(
              'assets/images/svgs/brands_icon.svg',
              fit: BoxFit.scaleDown,
              colorFilter: ColorFilter.mode(
                AppConstants.mainLayoutInitialScreenIndex == 0
                    ? AppColors.primaryColor900
                    : AppColors.neutralColor1200,
                BlendMode.srcIn,
              ),
            ),
          ),
          title: 'mainLayout.brands'.tr(),
          textStyle: Styles.contentRegular,
          activeColorPrimary: AppColors.primaryColor900,
          inactiveColorPrimary: AppColors.neutralColor1200,
          routeAndNavigatorSettings: RouteAndNavigatorSettings(
            initialRoute: "/",
          ),
        ),
        PersistentBottomNavBarItem(
          icon: Padding(
            padding: EdgeInsets.all(4.sp),
            child: SvgPicture.asset(
              'assets/images/svgs/more_un_selected_icon.svg',
              fit: BoxFit.scaleDown,
              colorFilter: ColorFilter.mode(
                AppConstants.mainLayoutInitialScreenIndex == 0
                    ? AppColors.primaryColor900
                    : AppColors.neutralColor1200,
                BlendMode.srcIn,
              ),
            ),
          ),
          title: 'mainLayout.more'.tr(),
          textStyle: Styles.contentRegular,
          activeColorPrimary: AppColors.primaryColor900,
          inactiveColorPrimary: AppColors.neutralColor1200,
          routeAndNavigatorSettings: RouteAndNavigatorSettings(
            initialRoute: "/",
            routes: {},
          ),
        ),
      ];
    }

    return BlocBuilder<MainLayoutCubit, MainLayoutState>(
      builder: (BuildContext context, state) {
        return PersistentTabView(
          context,
          controller: controller,
          screens: AppRouter().screen,
          items: navBarsItems(),
          stateManagement: true,
          padding: EdgeInsets.symmetric(vertical: 20.sp),
          navBarHeight: 94.sp,

          /// **🛠 Restoring the Default Design**
          decoration: NavBarDecoration(
            colorBehindNavBar: Colors.white,
            useBackdropFilter: false,
          ),
          navBarStyle: NavBarStyle.style1,
        );
      },
    );
  }
}