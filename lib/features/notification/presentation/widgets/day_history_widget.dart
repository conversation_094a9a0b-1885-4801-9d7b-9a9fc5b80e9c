import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';

class DayHistoryWidget extends StatelessWidget {
  const DayHistoryWidget({
    super.key, required this.dayHistoryText,
  });

  final String dayHistoryText;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Row(
        children: [
          Expanded(
            child: Divider(
              color: AppColors.neutralColor500,
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              dayHistoryText,
              style: Styles.captionEmphasis.copyWith(
                  color: AppColors.neutralColor500, height: -.5.h),
            ),
          ),
          Expanded(
            child: Divider(
              color: AppColors.neutralColor500,
            ),
          ),
        ],
      ),
    );
  }
}