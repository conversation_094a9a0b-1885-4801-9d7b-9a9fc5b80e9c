import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class NotificationWidget extends StatelessWidget {
  const NotificationWidget({
    super.key,
    required this.productImage,
    required this.productDescription,
    this.brandImage,
    this.brandName,
    this.discountedPrice,
    this.originalPrice,
    this.onMakeOrderFunction,
  });

  final String productImage;
  final String productDescription;
  final String? brandImage;
  final String? brandName;
  final String? discountedPrice;
  final String? originalPrice;
  final VoidCallback? onMakeOrderFunction;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.sp),
      decoration: BoxDecoration(
        color: AppColors.scaffoldBackground,
        border: Border.all(
          width: 1.w,
          color: AppColors.neutralColor200,
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadius + 2),
      ),
      child: Row(
        spacing: 12.w,
        crossAxisAlignment: brandImage != null &&
                brandName != null &&
                onMakeOrderFunction != null &&
                originalPrice != null
            ? CrossAxisAlignment.start
            : CrossAxisAlignment.center,
        children: [
          /// Product Image
          Column(
            children: [
              Image.asset(
                productImage,
                fit: BoxFit.scaleDown,
                 height: 50.h,
                width: 50.w,
              ),
            ],
          ),
          Expanded(
            child: Column(
              spacing: 8.h,
              children: [
                Text(
                  productDescription,
                  style: Styles.highlightEmphasis.copyWith(
                    color: Colors.black,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (brandImage != null && brandName != null) ...[
                  Row(
                    spacing: 4.w,
                    children: [
                      Image.asset(
                        brandImage!,
                        fit: BoxFit.scaleDown,
                        width: 20.w,
                        height: 20.h,
                      ),
                      Text(
                        brandName!,
                        style: Styles.captionBold.copyWith(
                          color: AppColors.neutralColor1200,
                        ),
                      ),
                    ],
                  ),
                ],
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (discountedPrice != null && originalPrice != null) ...[
                      Row(
                        spacing: 8.w,
                        children: [
                          Text(
                            discountedPrice!,
                            style: Styles.highlightEmphasis.copyWith(
                              color: Colors.black,
                            ),
                          ),
                          Text(
                            originalPrice!,
                            style: Styles.highlightEmphasis.copyWith(
                              color: AppColors.primaryColor800,
                              decoration: TextDecoration.lineThrough,
                              decorationColor: AppColors.primaryColor800,
                              decorationThickness: .8.h,
                            ),
                          ),
                        ],
                      ),
                    ],
                    if (onMakeOrderFunction != null) ...[
                      InkWell(
                        onTap: onMakeOrderFunction,
                        child: Text(
                          'notification.buyNow'.tr(),
                          style: Styles.highlightEmphasis.copyWith(
                            color: AppColors.primaryColor900,
                          ),
                        ),
                      ),
                    ]
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
