import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/empty_widget.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 16.w,
              children: [
                BackButtonWidget(onTap: () => context.pop()),
                Text(
                  'notification.notification'.tr(),
                  style: Styles.heading2.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 30.h),

          /// Empty Widget
          EmptyWidget(
            imagePath: 'assets/images/svgs/emptyNotification.svg',
            title: 'notification.noNewNotifications'.tr(),
            description: 'notification.notificationDesc'.tr(),
          )

          /// Notifications Widget
          // Padding(
          //   padding: EdgeInsets.symmetric(horizontal: 16.w),
          //   child: Column(
          //     children: [
          //       DayHistoryWidget(
          //         dayHistoryText: 'notification.day'.tr(),
          //       ),
          //       SizedBox(height: 20.h),
          //       NotificationWidget(
          //         productImage: 'assets/images/pngs/product1_image.png',
          //         productDescription:
          //             'عرض تيشيرت بوما الأكثر مبيعاً خلال خلال الأشهر الماضية',
          //         brandImage: 'assets/images/pngs/brand1_image.png',
          //         brandName: 'PUMA',
          //         discountedPrice: 'SAR 250',
          //         originalPrice: 'SAR 250',
          //         onMakeOrderFunction: () {},
          //       ),
          //       SizedBox(height: 20.h),
          //       NotificationWidget(
          //         productImage: 'assets/images/pngs/product3_image.png',
          //         productDescription: 'اطلب الآن اي قطعتين الثالثة مجاناً',
          //       ),
          //       SizedBox(height: 20.h),
          //       DayHistoryWidget(
          //         dayHistoryText: 'notification.aWeekAgo'.tr(),
          //       ),
          //       SizedBox(height: 20.h),
          //       NotificationWidget(
          //         productImage: 'assets/images/pngs/product2_image.png',
          //         productDescription:
          //             'عرض تيشيرت بوما الأكثر مبيعاً خلال خلال الأشهر الماضية',
          //         brandImage: 'assets/images/pngs/brand1_image.png',
          //         brandName: 'PUMA',
          //         discountedPrice: 'SAR 250',
          //         originalPrice: 'SAR 250',
          //         onMakeOrderFunction: () {},
          //       ),
          //     ],
          //   ),
          // ),
        ],
      ),
    );
  }
}
