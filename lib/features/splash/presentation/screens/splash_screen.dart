import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/splash/business_logic/splash_cubit.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<SplashCubit, SplashState>(
      listener: (context, state) {
        if (state is SplashNavigateToLogin) {
          context.pushNamedAndRemoveUntil(Routes.loginScreen);
        }
      },
      child: Scaffold(
        body: Stack(
          children: [
            // Background Image
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                    "assets/images/pngs/bg_image_in_splash_screen.png",
                  ),
                  fit: BoxFit.fill,
                ),
              ),
              child: Center(
                child: SvgPicture.asset(
                  'assets/images/svgs/logo_green_color.svg',
                  colorFilter: ColorFilter.mode(
                    AppColors.scaffoldBackground,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),

            // Animated Text
            Positioned(
              left: 0,
              right: 0,
              bottom: 168.h,
              child: TweenAnimationBuilder<Offset>(
                duration: const Duration(seconds: 2),
                tween: Tween<Offset>(
                  begin: const Offset(-1, 0),
                  end: Offset.zero,
                ),
                curve: Curves.easeInOut,
                builder: (context, offset, child) {
                  return Transform.translate(
                    offset: Offset(
                        offset.dx * MediaQuery.of(context).size.width, 0),
                    child: child,
                  );
                },
                child: Text(
                  'splash.splash'.tr(),
                  style: Styles.heading1.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
