import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/text/custom_text_rich_widget.dart';

class StartScreen extends StatelessWidget {
  const StartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    String currentLocale = context.locale.toString();

    // print(currentLocale);

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.25,
          ),

          // Logo
          Center(
            child: SvgPicture.asset(
              'assets/images/svgs/logo_green_color.svg',
            ),
          ),

          176.verticalSpace,

          // Rich Text Description
          Column(
            children: [
              CustomRichText(
                text1: 'start.electronicPlatform'.tr(),
                text2: 'start.saudi'.tr(),
                text3: 'start.digitalMarketing'.tr(),
                textStyle1: Styles.highlightBold.copyWith(color: Colors.black),
                textStyle2: Styles.highlightBold
                    .copyWith(color: AppColors.primaryColor900),
                textStyle3: Styles.highlightBold.copyWith(color: Colors.black),
              ),

              16.verticalSpace,

              // Subtext
              Text(
                'start.collectBrands'.tr(),
                style: Styles.highlightEmphasis.copyWith(
                  color: AppColors.neutralColor600,
                ),
              ),
            ],
          ),

          Spacer(),
          // Bottom Navigation Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              /// Circular Start Button
              GestureDetector(
                onTap: () => context.pushNamed(Routes.splashScreen),
                child: Align(
                  alignment: currentLocale == 'en_UK'
                      ? Alignment.bottomLeft
                      : Alignment.bottomRight,
                  child: Stack(
                    alignment: currentLocale == 'en_UK'
                        ? Alignment.bottomLeft
                        : Alignment.bottomRight,
                    children: [
                      currentLocale == 'en_UK'
                          ? Image.asset(
                              'assets/images/pngs/circle_image_right.png',
                              fit: BoxFit.scaleDown,
                              width: 110.w,
                              // height: 100.h,
                            )
                          : Image.asset(
                              'assets/images/pngs/circle_image.png',
                              fit: BoxFit.scaleDown,
                              width: 110.w,
                              // height: 100.h,
                            ),
                      Padding(
                        padding: currentLocale == 'en_UK'
                            ? EdgeInsets.only(
                                bottom: 33.h,
                                left: 5.w,
                              )
                            : EdgeInsets.only(
                                bottom: 33.h,
                                right: 16.w,
                              ),
                        child: Row(
                          spacing: 4.w,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.arrow_back,
                              color: AppColors.scaffoldBackground,
                              size: 16.sp,
                            ),
                            Text(
                              'start.begin'.tr(),
                              style: Styles.highlightStandard.copyWith(
                                color: AppColors.scaffoldBackground,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Previous Button (السابق)
              IconButton(
                onPressed: () => context.pop(),
                icon: Row(
                  children: [
                    Text(
                      'start.previous'.tr(),
                      style: Styles.highlightStandard.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Icon(
                      Icons.arrow_forward,
                      color: AppColors.neutralColor1200,
                      size: 16.sp,
                    ),
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
