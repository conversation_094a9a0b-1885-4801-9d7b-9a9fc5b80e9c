abstract class CartState {}

final class CartInitialState extends CartState {}

/// Get Cart States
final class GetCartLoadingState extends CartState {}

final class GetCartSuccessState extends CartState {}

final class GetCartErrorState extends CartState {}

/// Add To Cart States
final class AddToCartLoadingState extends CartState {}

final class AddToCartSuccessState extends CartState {}

final class AddToCartErrorState extends CartState {}

/// Remove From Cart
final class RemoveCartLoadingState extends CartState {}

final class RemoveCartSuccessState extends CartState {}

final class RemoveCartBottomState extends CartState {}

final class RemoveCartErrorState extends CartState {}

/// Update Product Quantity States
final class UpdateProductQuantityLoadingState extends CartState {}

final class UpdateProductQuantitySuccessState extends CartState {}

final class UpdateProductQuantityErrorState extends CartState {}

/// Calculate Total Price States
final class CalculateTotalPrice extends CartState {}

/// Plus Increment States
class PlusIncrementCartLoadingState extends CartState {}

class PlusIncrementCartSuccessState extends CartState {}

class PlusIncrementCartErrorState extends CartState {}

/// Minus Increment
class MinusIncrementCartLoadingState extends CartState {}

class MinusIncrementCartSuccessState extends CartState {}

class MinusIncrementCartErrorState extends CartState {}

/// Get Cart Details
class GetCartDetailsErrorState extends CartState {}

class GetCartDetailsSuccessState extends CartState {}

class GetCartDetailsLoadingState extends CartState {}

/// Apply Coupon States
final class ApplyCouponLoadingState extends CartState {}

final class ApplyCouponSuccessState extends CartState {}

final class ApplyCouponErrorState extends CartState {}