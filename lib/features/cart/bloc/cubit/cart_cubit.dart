import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
import 'package:tegra_ecommerce_app/core/helper_functions/flutter_toast.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_state.dart';
import 'package:tegra_ecommerce_app/features/cart/data/model/cart_model.dart';
import 'package:tegra_ecommerce_app/features/cart/data/model/cart_recipt_model.dart';
import 'package:tegra_ecommerce_app/features/cart/data/repo/cart_repo.dart';

class CartCubit extends Cubit<CartState> {
  CartCubit(this._cartRepository) : super(CartInitialState());
  final CartRepository _cartRepository;
  CartModel? productModel;
  CartReciptModel? cartReceiptModel;
  TextEditingController couponTextFormField = TextEditingController();
  bool isCancel = false;

  /// Get Cart
  Future<void> getCart() async {
    emit(GetCartLoadingState());
    final result = await _cartRepository.getCart();

    result.when(success: (products) async {
      productModel = products;
      // await getCartDetails();
      emit(GetCartSuccessState());
    }, failure: (error) {
      emit(GetCartErrorState());
    });
  }

  /// Get Cart Details
  Future<void> getCartDetails() async {
    emit(GetCartDetailsLoadingState());
    final result = await _cartRepository.getCartDetails();

    result.when(
      success: (products) {
        cartReceiptModel = products;
        isCancel = cartReceiptModel!.data.couponCode == null ? true : false;
        emit(GetCartDetailsSuccessState());
      },
      failure: (error) {
        emit(GetCartDetailsErrorState());
      },
    );
  }

  /// Add to cart

  Future<void> addToCart({required int productId, required int skuId}) async {
    emit(AddToCartLoadingState());
    showLoading();

    final result = await _cartRepository.addToCart(
      proudctId: productId,
      skuId: skuId,
    );

    result.when(
      success: (_) async {
        hideLoading();
        customToast(
            msg: 'productDetails.addToCartSuccess'.tr(),
            color: AppColors.greenColor200);

        await getCart();

        emit(AddToCartSuccessState());
      },
      failure: (error) {
        hideLoading();
        emit(AddToCartErrorState());
      },
    );
  }

  // Future<void> addToCart({required int productId, required int skuId}) async {
  //   emit(AddToCartLoadingState());
  //   showLoading();
  //   final result =
  //       await _cartRepository.addToCart(proudctId: productId, skuId: skuId);

  //   result.when(
  //     success: (_) {
  //       hideLoading();
  //       customToast(msg: "add to cart success", color: AppColors.greenColor200);
  //       emit(AddToCartSuccessState());
  //     },
  //     failure: (error) {
  //       hideLoading();
  //       emit(AddToCartErrorState());
  //     },
  //   );
  // }

  /// Plus Increment Cart
  Future<void> plusIncrementCart({
    required int cartId,
    required int index,
    required int quantity,
  }) async {
    showLoading();
    emit(PlusIncrementCartLoadingState());
    final result = await _cartRepository.plusIncrementcart(cartId: cartId);

    result.when(
      success: (_) async {
        await getCartDetails();
        // customToast(msg: "Quantity increased", color: AppColors.greenColor200);
        logSuccess(productModel!.data[index].quantity.toString());
        productModel!.data[index].quantity = quantity;
        logSuccess(productModel!.data[index].quantity.toString());
        hideLoading();
        emit(PlusIncrementCartSuccessState());
      },
      failure: (error) {
        hideLoading();
        emit(PlusIncrementCartErrorState());
      },
    );
  }

  /// Minus Increment Cart
  Future<void> minusIncrementCart({
    required int cartId,
    required int index,
    required int quantity,
  }) async {
    showLoading();
    emit(MinusIncrementCartLoadingState());
    final result = await _cartRepository.minusIncrementCart(cartId: cartId);

    result.when(
      success: (_) async {
        await getCartDetails();
        logSuccess(productModel!.data[index].quantity.toString());
        productModel!.data[index].quantity = quantity;

        logSuccess(productModel!.data[index].quantity.toString());
        hideLoading();
        emit(MinusIncrementCartSuccessState());
      },
      failure: (error) {
        hideLoading();
        emit(MinusIncrementCartErrorState());
      },
    );
  }

  /// Remove From Cart
  Future<void> removeFromCartApi(
      {required int cartId, required int index}) async {
    emit(RemoveCartLoadingState());
    showLoading();
    final result = await _cartRepository.removeFromCart(cartId: cartId);

    result.when(
      success: (_) async {
        productModel?.data.removeAt(index);
        await getCartDetails();
        if (productModel!.data.isEmpty) {
          emit(RemoveCartBottomState());
        }
        hideLoading();
        emit(RemoveCartSuccessState());
      },
      failure: (error) {
        hideLoading();
        emit(RemoveCartErrorState());
      },
    );
  }

  /// Apply Coupon
  Future<void> applyCoupon({
    required String coupon,
  }) async {
    showLoading();
    emit(ApplyCouponLoadingState());
    final result = await _cartRepository.applyCoupon(coupon: coupon);

    result.when(
      success: (_) async {
        isCancel = false;
        await getCartDetails();
        emit(ApplyCouponSuccessState());
        hideLoading();
      },
      failure: (error) {
        hideLoading();
        emit(ApplyCouponErrorState());
      },
    );
  }

  /// Remove Coupon
  Future<void> removeCoupon() async {
    showLoading();
    emit(ApplyCouponLoadingState());
    final result = await _cartRepository.removeCoupon();

    result.when(
      success: (_) async {
        isCancel = true;
        await getCartDetails();
        emit(ApplyCouponSuccessState());
        hideLoading();
      },
      failure: (error) {
        hideLoading();
        emit(ApplyCouponErrorState());
      },
    );
  }
}
