import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_cubit.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_state.dart';

class QuantityWidget extends StatefulWidget {
  QuantityWidget({
    super.key,
    required this.cardId,
    required this.quantity,
    required this.index,
  });
  int quantity;
  final int cardId;
  final int index;
  @override
  State<QuantityWidget> createState() => _QuantityWidgetState();
}

class _QuantityWidgetState extends State<QuantityWidget> {
  @override
  Widget build(BuildContext context) {
    CartCubit cartCubit = BlocProvider.of<CartCubit>(context);

    return BlocBuilder<CartCubit, CartState>(
      builder: (context, state) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              decoration: BoxDecoration(
                color: AppColors.scaffoldBackground,
                border: Border.all(
                  color: AppColors.neutralColor300,
                ),
                borderRadius: BorderRadius.all(
                  Radius.circular(
                    AppConstants.borderRadius - 4,
                  ),
                ),
              ),
              width: 104.w,
              height: 32,
              child: Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          widget.quantity++;
                        });
                        cartCubit.plusIncrementCart(
                          cartId: widget.cardId,
                          index: widget.index,
                          quantity: widget.quantity,
                        );
                      },
                      child: SvgPicture.asset(
                        height: 16,
                        width: 16,
                        Assets.assetsImagesSvgsAddIcon,
                      ),
                    ),
                  ),
                  VerticalDivider(
                    color: AppColors.neutralColor300,
                    width: 1.w,
                  ),
                  Expanded(
                    child: Text(
                      textAlign: TextAlign.center,
                      "${widget.quantity}",
                      style: Styles.contentRegular,
                    ),
                  ),
                  VerticalDivider(
                    color: AppColors.neutralColor300,
                    width: 1.w,
                  ),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        if (widget.quantity > 1) {
                          setState(() {
                            widget.quantity--;
                            cartCubit.minusIncrementCart(
                              cartId: widget.cardId,
                              index: widget.index,
                              quantity: widget.quantity,
                            );
                          });
                        } else {
                          cartCubit.removeFromCartApi(
                              cartId: widget.cardId, index: widget.index);
                        }
                      },
                      child: SvgPicture.asset(
                        height: 16,
                        width: 16,
                        Assets.assetsImagesSvgsDecrementIcon,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            InkWell(
              onTap: () {
                cartCubit.removeFromCartApi(
                    cartId: widget.cardId, index: widget.index);
              },
              child: SvgPicture.asset(
                Assets.assetsImagesSvgsDeleteRedIcon,
              ),
            )
          ],
        );
      },
    );
  }
}