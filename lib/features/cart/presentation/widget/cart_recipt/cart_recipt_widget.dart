import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_cubit.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_state.dart';

class CartDetails extends StatelessWidget {
  const CartDetails({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<CartCubit>();

    return BlocBuilder<CartCubit, CartState>(
      buildWhen: (previous, current) =>
          current is RemoveCartSuccessState ||
          current is GetCartDetailsSuccessState ||
          current is GetCartDetailsLoadingState ||
          current is GetCartDetailsErrorState ||
          current is MinusIncrementCartSuccessState ||
          current is PlusIncrementCartSuccessState,
      builder: (context, state) {
        if (state is GetCartDetailsLoadingState) {
          return const Center(child: CircularProgressIndicator());
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "details".tr(),
              style: Styles.heading5,
            ),
            12.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "subTotal".tr(),
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor600,
                  ),
                ),
                Text(
                  "SAR  ${cubit.cartReceiptModel!.data.total}",
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor1200,
                  ),
                ),
              ],
            ),
            16.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "tax".tr(),
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor600,
                  ),
                ),
                Text(
                  "SAR  ${cubit.cartReceiptModel!.data.tax}",
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.redColor100,
                  ),
                ),
              ],
            ),
            16.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "shipping".tr(),
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor600,
                  ),
                ),
                Text(
                  "SAR  ${cubit.cartReceiptModel!.data.shipping}",
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.redColor100,
                  ),
                ),
              ],
            ),
            16.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "couponDiscountAmount".tr(),
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor600,
                  ),
                ),
                Text(
                  "SAR  ${cubit.cartReceiptModel!.data.couponDiscountAmount}",
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.redColor100,
                  ),
                ),
              ],
            ),
            cubit.cartReceiptModel!.data.couponCode != null
                ? 16.verticalSpace
                : SizedBox.shrink(),
            cubit.cartReceiptModel!.data.couponCode != null
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "couponCode".tr(),
                        style: Styles.contentEmphasis.copyWith(
                          color: AppColors.neutralColor600,
                        ),
                      ),
                      Text(
                        "${cubit.cartReceiptModel!.data.couponCode}",
                        style: Styles.contentEmphasis.copyWith(
                          color: AppColors.redColor100,
                        ),
                      ),
                    ],
                  )
                : SizedBox.shrink(),
            16.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "total".tr(),
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor600,
                  ),
                ),
                Text(
                  "SAR  ${cubit.cartReceiptModel!.data.total}",
                  style: Styles.contentEmphasis.copyWith(),
                ),
              ],
            ),
            16.verticalSpace,
            Divider(
              color: AppColors.neutralColor10,
            ),
          ],
        );
      },
    );
  }
}
