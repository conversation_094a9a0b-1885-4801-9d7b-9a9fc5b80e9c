// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/cart/data/model/cart_model.dart';
import 'package:tegra_ecommerce_app/features/cart/presentation/widget/quantity_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/category_name_rotation_widget.dart';

class CartItemWidget extends StatelessWidget {
  const CartItemWidget({super.key, required this.item, required this.index});
  final ProductCartItem item;
  final int index;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        color: Colors.white,
        border: Border.all(color: AppColors.neutralColor200, width: 1.w),
      ),
      padding: EdgeInsets.all(16.sp),
      child: SizedBox(
        width: 100.w,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 98.w,
              height: 100.h,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppConstants.borderRadius),
                      topRight: Radius.circular(AppConstants.borderRadius),
                    ),
                    child: CachedNetworkImage(
                      imageUrl: item.thumbnail,
                      fit: BoxFit.scaleDown,
                    ),
                  ),
                  CategoryNameRotationWidget(
                    inListViewInSearch: true,
                    categoryName: item.label,
                  ),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Image.asset(
                      Assets.assetsImagesPngsProductStackImage,
                      fit: BoxFit.fill,
                    ),
                  ),
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(AppConstants.borderRadius),
                          topRight: Radius.circular(AppConstants.borderRadius),
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withAlpha((0.16 * 255).toInt()),
                            Colors.black.withAlpha((0.0 * 255).toInt()),
                          ],
                          stops: [0.0, 1.0],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            12.horizontalSpace,
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    spacing: 4.h,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'SAR ${item.priceAfterDiscount}',
                            style: Styles.captionRegular.copyWith(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                              fontSize: 16.sp,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        item.name,
                        style: Styles.contentRegular.copyWith(
                          color: AppColors.neutralColor600,
                        ),
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(50.r),
                            child: CachedNetworkImage(
                              imageUrl: item.brand.thumbnail,
                              height: 20.h,
                              width: 20.w,
                              fit: BoxFit.cover,
                            ),
                          ),
                          5.horizontalSpace,
                          Text(
                            item.brand.title,
                            style: Styles.contentEmphasis,
                          ),
                        ],
                      ),
                    ],
                  ),
                  12.verticalSpace,
                  QuantityWidget(
                    index: index,
                    quantity: item.quantity,
                    cardId: item.cartId,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
