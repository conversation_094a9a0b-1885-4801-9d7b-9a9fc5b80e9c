import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page_transition/page_transition.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/main_app_bar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/empty/empty_widget.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/widgets/custom_header_in_refresh_indicator_widget.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_cubit.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_state.dart';
import 'package:tegra_ecommerce_app/features/cart/presentation/widget/cart_item/cart_item_widget.dart';
import 'package:tegra_ecommerce_app/features/cart/presentation/widget/cart_item/cart_skeletonizer_item_widget.dart';
import 'package:tegra_ecommerce_app/features/orders/presentation/screens/orders_screen.dart';

// class CartScreen extends StatelessWidget {
//   const CartScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final cubit = context.read<CartCubit>();
//     final RefreshController refreshController = RefreshController();

//     return Scaffold(
//       backgroundColor: AppColors.scaffoldBackground,
//       body: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           MainAppBarWidget(),
//           SizedBox(height: 30.h),
//           Expanded(
//             child: BlocBuilder<CartCubit, CartState>(
//               buildWhen: (previous, current) =>
//                   current is GetCartSuccessState ||
//                   current is GetCartErrorState ||
//                   current is GetCartLoadingState,
//               builder: (context, state) {
//                 return SmartRefresher(
//                   controller: refreshController,
//                   onRefresh: () async {
//                     await cubit.getCart();
//                     refreshController.refreshCompleted();
//                   },
//                   header: CustomHeaderInRefreshIndicatorWidget(),
//                   child: state is GetCartLoadingState
//                       ? Skeletonizer(
//                           enabled: true,
//                           child: ListView.separated(
//                             physics: NeverScrollableScrollPhysics(),
//                             padding: EdgeInsets.symmetric(horizontal: 16.w),
//                             itemCount: 5,
//                             shrinkWrap: true,
//                             separatorBuilder: (_, __) => 16.verticalSpace,
//                             itemBuilder: (_, __) =>
//                                 CartSkeletonizerItemWidget(),
//                           ),
//                         )
//                       : BlocBuilder<CartCubit, CartState>(
//                           buildWhen: (previous, current) =>
//                               current is RemoveCartSuccessState ||
//                               current is GetCartSuccessState ||
//                               current is AddToCartSuccessState,
//                           builder: (context, state) {
//                             return cubit.productModel!.data.isNotEmpty
//                                 ? ListView.separated(
//                                     padding:
//                                         EdgeInsets.symmetric(horizontal: 16.w),
//                                     itemCount: cubit.productModel!.data.length,
//                                     separatorBuilder: (_, __) =>
//                                         16.verticalSpace,
//                                     itemBuilder: (_, index) => CartItemWidget(
//                                       item: cubit.productModel!.data[index],
//                                       index: index,
//                                     ),
//                                   )
//                                 : EmptyWidget(
//                                     imagePath:
//                                         'assets/images/svgs/emptyCart.svg',
//                                     title: 'cart.emptyBasket'.tr(),
//                                     description: 'cart.cartDesc'.tr(),
//                                   );
//                           },
//                         ),
//                 );
//               },
//             ),
//           ),
//         ],
//       ),
//       bottomNavigationBar: BlocBuilder<CartCubit, CartState>(
//         buildWhen: (previous, current) =>
//             current is GetCartSuccessState ||
//             current is GetCartErrorState ||
//             current is RemoveCartBottomState ||
//             current is GetCartLoadingState,
//         builder: (context, state) {
//           if (cubit.productModel == null || cubit.productModel!.data.isEmpty) {
//             return const SizedBox.shrink();
//           }
//           return Skeletonizer(
//             enabled: state is GetCartLoadingState,
//             child: Padding(
//               padding: EdgeInsets.symmetric(horizontal: 16.w),
//               child: CustomButtonWidget(
//                 text: 'cart.payment'.tr(),
//                 onPressed: cubit.productModel == null ||
//                         cubit.productModel!.data.isEmpty
//                     ? null
//                     : () {
//                         Navigator.of(context).push(
//                           PageTransition(
//                             type: PageTransitionType.fade,
//                             child: BlocProvider.value(
//                               value: cubit..getCartDetails(),
//                               child: OrdersScreen(),
//                             ),
//                           ),
//                         );
//                       },
//               ),
//             ),
//           );
//         },
//       ),
//     );
//   }
// }
class CartScreen extends StatelessWidget {
  const CartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<CartCubit>();
    final RefreshController refreshController = RefreshController();

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MainAppBarWidget(),
          SizedBox(height: 30.h),
          Expanded(
            child: BlocBuilder<CartCubit, CartState>(
              buildWhen: (previous, current) =>
                  current is GetCartSuccessState ||
                  current is GetCartErrorState ||
                  current is GetCartLoadingState ||
                  current is AddToCartSuccessState ||
                  current is RemoveCartSuccessState,
              builder: (context, state) {
                return SmartRefresher(
                  controller: refreshController,
                  onRefresh: () async {
                    await cubit.getCart();
                    refreshController.refreshCompleted();
                  },
                  header: CustomHeaderInRefreshIndicatorWidget(),
                  child: state is GetCartLoadingState
                      ? Skeletonizer(
                          enabled: true,
                          child: ListView.separated(
                            physics: const NeverScrollableScrollPhysics(),
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            itemCount: 5,
                            shrinkWrap: true,
                            separatorBuilder: (_, __) => 16.verticalSpace,
                            itemBuilder: (_, __) => CartSkeletonizerItemWidget(),
                          ),
                        )
                      : cubit.productModel != null &&
                              cubit.productModel!.data.isNotEmpty
                          ? ListView.separated(
                              padding: EdgeInsets.symmetric(horizontal: 16.w),
                              itemCount: cubit.productModel!.data.length,
                              separatorBuilder: (_, __) => 16.verticalSpace,
                              itemBuilder: (_, index) => CartItemWidget(
                                item: cubit.productModel!.data[index],
                                index: index,
                              ),
                            )
                          : EmptyWidget(
                              imagePath: 'assets/images/svgs/emptyCart.svg',
                              title: 'cart.emptyBasket'.tr(),
                              description: 'cart.cartDesc'.tr(),
                            ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: BlocBuilder<CartCubit, CartState>(
        buildWhen: (previous, current) =>
            current is GetCartSuccessState ||
            current is GetCartErrorState ||
            current is RemoveCartBottomState ||
            current is GetCartLoadingState,
        builder: (context, state) {
          if (cubit.productModel == null || cubit.productModel!.data.isEmpty) {
            return const SizedBox.shrink();
          }
          return Skeletonizer(
            enabled: state is GetCartLoadingState,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: CustomButtonWidget(
                text: 'cart.payment'.tr(),
                onPressed: () {
                  Navigator.of(context).push(
                    PageTransition(
                      type: PageTransitionType.fade,
                      child: BlocProvider.value(
                        value: cubit..getCartDetails(),
                        child: const OrdersScreen(),
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}

