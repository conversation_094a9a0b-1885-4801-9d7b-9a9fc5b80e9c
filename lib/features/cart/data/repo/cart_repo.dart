import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/cart/data/api_services/cart_api_services.dart';
import 'package:tegra_ecommerce_app/features/cart/data/model/cart_model.dart';
import 'package:tegra_ecommerce_app/features/cart/data/model/cart_recipt_model.dart';

class CartRepository {
  final CartApiServices _apiServices;

  CartRepository(this._apiServices);

  Future<ApiResult<CartModel>> getCart() async {
    try {
      final response = await _apiServices.getCart();
      if (response!.statusCode == 200) {
        return ApiResult.success(CartModel.fromJson(response.data));
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return _handleDioException(e);
    }
  }

  Future<ApiResult<CartReciptModel>> getCartDetails() async {
    try {
      final response = await _apiServices.getCartDetails();
      if (response!.statusCode == 200) {
        return ApiResult.success(CartReciptModel.fromJson(response.data));
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return _handleDioException(e);
    }
  }

  Future<ApiResult<bool>> addToCart(
      {required int proudctId, required int skuId}) async {
    try {
      final response =
          await _apiServices.addToCart(productId: proudctId, skuId: skuId);
      if (response!.statusCode == 200) {
        return const ApiResult.success(true);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return _handleDioException(e);
    }
  }

  Future<ApiResult<bool>> removeFromCart({
    required int cartId,
  }) async {
    try {
      final response = await _apiServices.removeFromCart(cartId: cartId);
      if (response!.statusCode == 200) {
        return const ApiResult.success(true);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return _handleDioException(e);
    }
  }

  Future<ApiResult<bool>> minusIncrementCart({
    required int cartId,
  }) async {
    try {
      final response = await _apiServices.minusIncrementCart(
        cartId: cartId,
      );
      if (response!.statusCode == 200) {
        return const ApiResult.success(true);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return _handleDioException(e);
    }
  }

  Future<ApiResult<bool>> plusIncrementcart({
    required int cartId,
  }) async {
    try {
      final response = await _apiServices.plusIncrementCart(
        cartId: cartId,
      );
      if (response!.statusCode == 200) {
        return const ApiResult.success(true);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return _handleDioException(e);
    }
  }

  ApiResult<T> _handleDioException<T>(DioException e) {
    try {
      handleDioException(e);
    } on ServerException catch (ex) {
      return ApiResult.failure(ex.errorModel.errorMessage);
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  Future<ApiResult<String>> applyCoupon({required String coupon}) async {
    try {
      final response = await _apiServices.applyCoupon(coupon: coupon);
      if (response!.statusCode == 200) {
        return ApiResult.success('Coupon Applied Successfully');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(
            response.statusCode,
            response,
          ),
        );
      }
    } on DioException catch (e) {
      return _handleDioException(e);
    }
  }

  Future<ApiResult<String>> removeCoupon() async {
    try {
      final response = await _apiServices.removeCoupon();
      if (response!.statusCode == 200) {
        return ApiResult.success('Coupon removed Successfully');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(
            response.statusCode,
            response,
          ),
        );
      }
    } on DioException catch (e) {
      return _handleDioException(e);
    }
  }
}
