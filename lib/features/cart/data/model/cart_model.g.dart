// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cart_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CartModel _$CartModelFromJson(Map<String, dynamic> json) => CartModel(
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) => ProductCartItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CartModelToJson(CartModel instance) => <String, dynamic>{
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
      'data': instance.data,
    };

ProductCartItem _$ProductCartItemFromJson(Map<String, dynamic> json) =>
    ProductCartItem(
      productId: (json['productId'] as num).toInt(),
      name: json['name'] as String,
      thumbnail: json['thumbnail'] as String,
      price: json['price'] as num,
      discountRounded: json['discountRounded'] as num,
      priceAfterDiscount: json['priceAfterDiscount'] as num,
      brand: Brand.fromJson(json['brand'] as Map<String, dynamic>),
      label: json['label'] as String,
      cartId: (json['cartId'] as num).toInt(),
      quantity: (json['quantity'] as num).toInt(),
      skuAttributes: (json['skuAttributes'] as List<dynamic>)
          .map((e) => SkuAttribute.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ProductCartItemToJson(ProductCartItem instance) =>
    <String, dynamic>{
      'productId': instance.productId,
      'name': instance.name,
      'thumbnail': instance.thumbnail,
      'price': instance.price,
      'discountRounded': instance.discountRounded,
      'priceAfterDiscount': instance.priceAfterDiscount,
      'brand': instance.brand,
      'label': instance.label,
      'quantity': instance.quantity,
      'cartId': instance.cartId,
      'skuAttributes': instance.skuAttributes,
    };

Brand _$BrandFromJson(Map<String, dynamic> json) => Brand(
      title: json['title'] as String,
      thumbnail: json['thumbnail'] as String,
    );

Map<String, dynamic> _$BrandToJson(Brand instance) => <String, dynamic>{
      'title': instance.title,
      'thumbnail': instance.thumbnail,
    };

SkuAttribute _$SkuAttributeFromJson(Map<String, dynamic> json) => SkuAttribute(
      key: json['key'] as String,
      value: json['value'] as String,
    );

Map<String, dynamic> _$SkuAttributeToJson(SkuAttribute instance) =>
    <String, dynamic>{
      'key': instance.key,
      'value': instance.value,
    };
