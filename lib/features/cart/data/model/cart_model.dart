import 'package:json_annotation/json_annotation.dart';

part 'cart_model.g.dart';

@JsonSerializable()
class CartModel {
  final String status;
  final String error;
  final int code;
  final List<ProductCartItem> data;

  CartModel({
    required this.status,
    required this.error,
    required this.code,
    required this.data,
  });

  factory CartModel.fromJson(Map<String, dynamic> json) =>
      _$CartModelFromJson(json);
  Map<String, dynamic> toJson() => _$CartModelToJson(this);
}

@JsonSerializable()
class ProductCartItem {
  final int productId;
  final String name;
  final String thumbnail;
  final num price;
  final num discountRounded;
  final num priceAfterDiscount;
  final Brand brand;
  final String label;
  int quantity;
  final int cartId;
  final List<SkuAttribute> skuAttributes;

  ProductCartItem({
    required this.productId,
    required this.name,
    required this.thumbnail,
    required this.price,
    required this.discountRounded,
    required this.priceAfterDiscount,
    required this.brand,
    required this.label,
    required this.cartId,
    required this.quantity,
    required this.skuAttributes,
  });

  factory ProductCartItem.fromJson(Map<String, dynamic> json) =>
      _$ProductCartItemFromJson(json);
  Map<String, dynamic> toJson() => _$ProductCartItemToJson(this);
}

@JsonSerializable()
class Brand {
  final String title;
  final String thumbnail;

  Brand({required this.title, required this.thumbnail});

  factory Brand.fromJson(Map<String, dynamic> json) => _$BrandFromJson(json);
  Map<String, dynamic> toJson() => _$BrandToJson(this);
}

@JsonSerializable()
class SkuAttribute {
  final String key;
  final String value;

  SkuAttribute({required this.key, required this.value});

  factory SkuAttribute.fromJson(Map<String, dynamic> json) =>
      _$SkuAttributeFromJson(json);
  Map<String, dynamic> toJson() => _$SkuAttributeToJson(this);
}
