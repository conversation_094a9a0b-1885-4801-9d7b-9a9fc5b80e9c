import 'package:json_annotation/json_annotation.dart';

part 'cart_recipt_model.g.dart';

@JsonSerializable()
class CartReciptModel {
  final Data data;
  final String status;
  final String error;
  final int code;

  CartReciptModel({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory CartReciptModel.fromJson(Map<String, dynamic> json) =>
      _$CartReciptModelFromJson(json);
        Map<String, dynamic> toJson() => _$CartReciptModelToJson(this);

}

@JsonSerializable()
class Data {
  final num subTotal;
  final num subTotalAfterApplyCoupon;
  final num couponDiscountAmount;

  final String? couponCode;
  final num tax;
  final num shipping;
  final String currency;
  final num total;

  Data({
    required this.subTotal,
    required this.subTotalAfterApplyCoupon,
    required this.couponDiscountAmount,
    this.couponCode,
    required this.tax,
    required this.shipping,
    required this.currency,
    required this.total,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
  Map<String, dynamic> toJson() => _$DataToJson(this);
}
