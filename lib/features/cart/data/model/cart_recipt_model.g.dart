// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cart_recipt_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CartReciptModel _$CartReciptModelFromJson(Map<String, dynamic> json) =>
    CartReciptModel(
      data: Data.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
    );

Map<String, dynamic> _$CartReciptModelToJson(CartReciptModel instance) =>
    <String, dynamic>{
      'data': instance.data,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      subTotal: json['subTotal'] as num,
      subTotalAfterApplyCoupon: json['subTotalAfterApplyCoupon'] as num,
      couponDiscountAmount: json['couponDiscountAmount'] as num,
      couponCode: json['couponCode'] as String?,
      tax: json['tax'] as num,
      shipping: json['shipping'] as num,
      currency: json['currency'] as String,
      total: json['total'] as num,
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      'subTotal': instance.subTotal,
      'subTotalAfterApplyCoupon': instance.subTotalAfterApplyCoupon,
      'couponDiscountAmount': instance.couponDiscountAmount,
      'couponCode': instance.couponCode,
      'tax': instance.tax,
      'shipping': instance.shipping,
      'currency': instance.currency,
      'total': instance.total,
    };
