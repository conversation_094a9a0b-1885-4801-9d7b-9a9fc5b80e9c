import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class CartApiServices {
  CartApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Login

  Future<Response?> addToCart({
    required int productId,
    required int skuId,
  }) async {
    return _dioFactory.post(
      endPoint: EndPoints.addToCart,
      data: {
        "product_id": productId,
        "sku_id": skuId,
      },
    );
  }

  Future<Response?> removeFromCart({
    required int cartId,
  }) async {
    return _dioFactory
        .post(endPoint: EndPoints.removeFromCart, data: {"cart_id": cartId});
  }

  Future<Response?> getCart() async {
    return _dioFactory.get(endPoint: EndPoints.getCart);
  }

  Future<Response?> plusIncrementCart({
    required int cartId,
  }) async {
    return _dioFactory
        .post(endPoint: EndPoints.plusIncrementCart, data: {"cart_id": cartId});
  }

  Future<Response?> minusIncrementCart({
    required int cartId,
  }) async {
    return _dioFactory.post(
        endPoint: EndPoints.minusIncrementCart, data: {"cart_id": cartId});
  }

  Future<Response?> getCartDetails() async {
    return _dioFactory.get(
      endPoint: EndPoints.getCartDetails,
    );
  }

  Future<Response?> applyCoupon({required String coupon}) async {
    return _dioFactory
        .post(endPoint: EndPoints.applyCoupon, data: {"coupon": coupon});
  }

  Future<Response?> removeCoupon() async {
    return _dioFactory.get(endPoint: EndPoints.removeCoupon);
  }
}
