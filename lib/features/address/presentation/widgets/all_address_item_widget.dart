import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/address/business_logic/cubit/address_cubit.dart';
import 'package:tegra_ecommerce_app/features/address/data/model/address_model.dart';

class AllAddressItemWidget extends StatelessWidget {
  const AllAddressItemWidget({
    super.key,
    required this.address,
    this.onPressedDelete,
    this.isSelected = false,
    this.onTap,
    required this.index,
  });

  final Address address;
  final Function()? onPressedDelete;
  final bool isSelected;
  final Function()? onTap;
  final int index;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.sp, horizontal: 8.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            color: isSelected
                ? AppColors.primaryColor900
                : AppColors.neutralColor600,
            width: isSelected ? 2.w : 1.w,
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                // Column(
                //   crossAxisAlignment: CrossAxisAlignment.start,
                //   children: [
                //     Text(
                //       'address.theAddress'.tr(),
                //       style: Styles.contentEmphasis.copyWith(
                //         fontWeight: FontWeight.w700,
                //         fontSize: 16.sp,
                //       ),
                //     ),
                //     Text(
                //       address.address,
                //       style: Styles.contentEmphasis.copyWith(
                //         fontWeight: FontWeight.w400,
                //         fontSize: 14.sp,
                //         color: AppColors.neutralColor600,
                //       ),
                //     ),

                //   ],
                // ),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'address.theAddress'.tr(),
                        style: Styles.contentEmphasis.copyWith(
                          fontWeight: FontWeight.w700,
                          fontSize: 16.sp,
                        ),
                      ),
                      Text(
                        address.address,
                        style: Styles.contentEmphasis.copyWith(
                          fontWeight: FontWeight.w400,
                          fontSize: 14.sp,
                          color: AppColors.neutralColor600,
                        ),
                      ),
                    ],
                  ),
                ),

                // const Spacer(),

                IconButton(
                  onPressed: onPressedDelete,
                  icon: Icon(
                    Icons.delete_forever,
                    color: AppColors.primaryColor900,
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 20.sp,
                  color: AppColors.primaryColor900,
                ),
              ],
            ),
            SwitchListTile(
              contentPadding: EdgeInsets.zero,
              title: Text(
                'address.defaultAddress'.tr(),
                style: Styles.contentEmphasis,
              ),
              value: index == context.read<AddressCubit>().selectedAddressIdex,
              activeColor: AppColors.primaryColor900,
              onChanged: (value) {
                // if (value) {
                // context.read<AddressCubit>().toggleDefaultAddress(value);
                // }
                context.read<AddressCubit>().changeSelected(index, value);
              },
            ),
          ],
        ),
      ),
    );
  }
}
