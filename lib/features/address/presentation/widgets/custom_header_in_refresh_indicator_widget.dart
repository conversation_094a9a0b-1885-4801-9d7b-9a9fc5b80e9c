import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';

class CustomHeaderInRefreshIndicatorWidget extends StatelessWidget {
  const CustomHeaderInRefreshIndicatorWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return CustomHeader(
      builder: (context, mode) {
        Widget body;
        if (mode == RefreshStatus.idle) {
          body = SpinKitCircle(
              color: AppColors.neutralColor400, size: 30);
        } else if (mode == RefreshStatus.refreshing) {
          body = SpinKitCircle(
              color: AppColors.neutralColor400, size: 30);
        } else if (mode == RefreshStatus.completed) {
          body =
              Icon(Icons.check, color: AppColors.neutralColor400);
        } else {
          body = SpinKitCircle(
              color: AppColors.neutralColor400, size: 30);
        }
        return SizedBox(
          height: 60.0,
          child: Center(child: body),
        );
      },
    );
  }
}