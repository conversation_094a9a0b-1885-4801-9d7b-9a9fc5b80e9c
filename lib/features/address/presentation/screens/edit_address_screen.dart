import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/address/business_logic/cubit/address_cubit.dart';
import 'package:tegra_ecommerce_app/features/address/data/model/address_model.dart';

class EditAddressScreen extends StatelessWidget {
  const EditAddressScreen({super.key, required this.address});
  final Address address;

  @override
  Widget build(BuildContext context) {
    hideLoading();
    final cubit = context.read<AddressCubit>();
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 16.w,
              children: [
                BackButtonWidget(onTap: () => context.pop()),
                Text(
                  'address.editAddress'.tr(),
                  style: Styles.heading2.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
              ],
            ),
          ),
          30.verticalSpace,
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 16.sp, vertical: 5.sp),
                child: Column(
                  spacing: 16.sp,
                  children: [
                    CustomTextFormFieldWidget(
                      controller: cubit.nameController,
                      labelText: 'address.fullName'.tr(),
                      backgroundColor: Colors.white,
                    ),
                    CustomTextFormFieldWidget(
                      controller: cubit.phoneController,
                      labelText: 'address.phoneNumber'.tr(),
                      backgroundColor: Colors.white,
                    ),
                    CustomTextFormFieldWidget(
                      controller: cubit.cityController,
                      labelText: 'address.city'.tr(),
                      backgroundColor: Colors.white,
                    ),
                    CustomTextFormFieldWidget(
                      controller: cubit.addressController,
                      labelText: 'address.address'.tr(),
                      backgroundColor: Colors.white,
                    ),
                    Row(
                      spacing: 12.sp,
                      children: [
                        Expanded(
                          child: CustomTextFormFieldWidget(
                            controller: cubit.noOfFloorController,
                            labelText: address.flatNumber.isEmpty
                                ? 'address.appartmentNumber'.tr()
                                : address.flatNumber,
                            backgroundColor: Colors.white,
                          ),
                        ),
                        Expanded(
                          child: CustomTextFormFieldWidget(
                            controller: cubit.noOfApartmentController,
                            labelText: 'address.buildingNumber'.tr(),
                            backgroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    CustomTextFormFieldWidget(
                      labelText: 'address.notes'.tr(),
                      backgroundColor: Colors.white,
                      height: 50.sp,
                    ),
                    ValueListenableBuilder<bool>(
                      valueListenable: cubit.isDefaultAddress,
                      builder: (context, isChecked, _) {
                        return CheckboxListTile(
                          title: Text(
                            'address.defaultAddress'.tr(),
                            style: Styles.contentEmphasis,
                          ),
                          value: isChecked,
                          activeColor: AppColors.primaryColor900,
                          onChanged: (value) {
                            if (value != null) {
                              cubit.toggleDefaultAddress(value);
                            }
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.all(20.sp),
        child: BlocListener<AddressCubit, AddressState>(
          listener: (context, state) {
            if (state is EditAddressSuccess) {
              context.pop();
            }
          },
          child: CustomButtonWidget(
            text: "address.update".tr(),
            onPressed: () {
              cubit.editAddress(addressId: address.id);
            },
          ),
        ),
      ),
    );
  }
}
