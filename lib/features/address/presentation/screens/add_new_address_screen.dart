import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/helper_functions/validation.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/address/business_logic/cubit/address_cubit.dart';

class AddNewAddressScreen extends StatelessWidget {
  const AddNewAddressScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final addressesCubit = context.read<AddressCubit>();

    return Form(
      key: addressesCubit.formKey,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          children: [
            AppBarWidget(
              rowWidget: Row(
                spacing: 16.w,
                children: [
                  BackButtonWidget(onTap: () => context.pop()),
                  Text(
                    'address.addAddress'.tr(),
                    style: Styles.heading2.copyWith(
                      color: AppColors.scaffoldBackground,
                    ),
                  ),
                ],
              ),
            ),
            // AppBarWidget(
            //   rowWidget: Row(
            //     spacing: 16.w,
            //     children: [
            //       InkWell(
            //         onTap: () => context.pop(),
            //         child: Container(
            //           padding: EdgeInsets.all(12.sp),
            //           decoration: BoxDecoration(
            //             borderRadius:
            //                 BorderRadius.circular(AppConstants.borderRadius),
            //             color: AppColors.scaffoldBackground,
            //           ),
            //           child: Center(
            //             child: SvgPicture.asset(
            //               Assets.assetsImagesSvgsArrowRightIcon,
            //             ),
            //           ),
            //         ),
            //       ),
            //       Text(
            //         'address.addAddress'.tr(),
            //         style: Styles.heading2.copyWith(
            //           color: AppColors.scaffoldBackground,
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
            30.verticalSpace,
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.sp),
                  child: Column(
                    spacing: 16.sp,
                    children: [
                      CustomTextFormFieldWidget(
                        controller: addressesCubit.nameController,
                        labelText: 'address.fullName'.tr(),
                        backgroundColor: Colors.white,
                        validator: (value) =>
                            AppValidator.validateUsername(value),
                        // controller: TextEditingController(),
                      ),
                      CustomTextFormFieldWidget(
                        controller: addressesCubit.phoneController,
                        labelText: 'auth.phoneNumber'.tr(),
                        keyboardType: TextInputType.phone,
                        textInputAction: TextInputAction.next,
                        inputFormatters: [
                          // FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                          // LengthLimitingTextInputFormatter(9),
                        ],
                        prefixIcon: SvgPicture.asset(
                          'assets/images/svgs/phone_icon.svg',
                          fit: BoxFit.scaleDown,
                        ),
                        suffixIcon: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.w, vertical: 14.h),
                          child: Text(
                            '966+',
                            style: Styles.contentEmphasis.copyWith(
                              color: AppColors.neutralColor1200,
                            ),
                          ),
                        ),
                        backgroundColor: Colors.white,
                        borderRadius: AppConstants.borderRadius,
                        // validator: (value) =>
                        //     AppValidator.validateSaudiPhoneNumber(value),
                      ),

                      // CustomTextFormFieldWidget(
                      //   controller: addressesCubit.phoneController,
                      //   labelText: 'address.phoneNumber'.tr(),
                      //   backgroundColor: Colors.white,
                      //   // validator: (value) => AppValidator.validateSaudiPhoneNumber(value),
                      // ),
                      CustomTextFormFieldWidget(
                        controller: addressesCubit.cityController,
                        labelText: 'address.city'.tr(),
                        backgroundColor: Colors.white,
                        validator: (value) => AppValidator.validateCity(value),
                      ),
                      CustomTextFormFieldWidget(
                        controller: addressesCubit.addressController,
                        labelText: 'address.address'.tr(),
                        backgroundColor: Colors.white,
                        validator: (value) =>
                            AppValidator.validateAddress(value),
                      ),
                      Row(
                        spacing: 12.sp,
                        children: [
                          Expanded(
                            child: CustomTextFormFieldWidget(
                              controller:
                                  addressesCubit.noOfApartmentController,
                              labelText: 'address.appartmentNumber'.tr(),
                              backgroundColor: Colors.white,
                              validator: (value) =>
                                  AppValidator.validateApartmentNumber(value),
                            ),
                          ),
                          Expanded(
                            child: CustomTextFormFieldWidget(
                              controller: addressesCubit.noOfFloorController,
                              labelText: 'address.buildingNumber'.tr(),
                              backgroundColor: Colors.white,
                              validator: (value) =>
                                  AppValidator.validateBuildingNumber(value),
                            ),
                          ),
                        ],
                      ),
                      CustomTextFormFieldWidget(
                        controller: addressesCubit.notesController,
                        labelText: 'address.notes'.tr(),
                        backgroundColor: Colors.white,
                        height: 50.sp,
                        // validator: (value) => AppValidator.validateNote(value),
                      ),

                      /// ✅ Checkbox for "Set as Default Address"
                      ValueListenableBuilder<bool>(
                        valueListenable: addressesCubit.isDefaultAddress,
                        builder: (context, isChecked, _) {
                          return CheckboxListTile(
                            title: Text(
                              'address.defaultAddress'.tr(),
                              style: Styles.contentEmphasis,
                            ),
                            value: isChecked,
                            activeColor: AppColors.primaryColor900,
                            onChanged: (value) {
                              if (value != null) {
                                addressesCubit.toggleDefaultAddress(value);
                              }
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        bottomNavigationBar: BlocListener<AddressCubit, AddressState>(
          listener: (context, state) {
            if (state is AddAddressSuccess) {
              addressesCubit.getAllAddresses();
              context.pop();
            }
          },
          child: SafeArea(
            minimum: EdgeInsets.all(20.sp),
            child: CustomButtonWidget(
              text: "address.saveAddress".tr(),
              onPressed: () {
                if (addressesCubit.formKey.currentState!.validate()) {
                  addressesCubit.addAddress(context: context);
                }
              },
            ),
          ),
        ),
      ),
    );
  }
}
