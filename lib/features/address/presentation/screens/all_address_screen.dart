import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/empty/empty_widget.dart';
import 'package:tegra_ecommerce_app/features/address/business_logic/cubit/address_cubit.dart';
import 'package:tegra_ecommerce_app/features/address/data/model/address_model.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/screens/add_new_address_screen.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/screens/edit_address_screen.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/widgets/all_address_item_widget.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/widgets/custom_header_in_refresh_indicator_widget.dart';

class AllAddressScreen extends StatelessWidget {
  const AllAddressScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final RefreshController refreshController = RefreshController();
    final addressCubit = context.read<AddressCubit>();
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 16.w,
              children: [
                BackButtonWidget(onTap: () => Navigator.pop(context, true)),
                Text(
                  'address.address'.tr(),
                  style: Styles.heading2.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
              ],
            ),
          ),
          30.verticalSpace,
          Expanded(
            child: BlocConsumer<AddressCubit, AddressState>(
              listener: (context, state) {
                if (state is DeleteAddressSuccess) {
                  context.read<AddressCubit>().getAllAddresses();
                }
              },
              buildWhen: (previous, current) =>
                  current is GetAllAddressesLoadingMore ||
                  current is GetAllAddressesLoading ||
                  current is GetAllAddressesSuccess ||
                  current is GetAllAddressesError ||
                  current is ChosseAddressChangeState,
              builder: (context, state) {
                if (state is GetAllAddressesLoading) {
                  return Skeletonizer(
                    enabled: true,
                    child: ListView.separated(
                      physics: const AlwaysScrollableScrollPhysics(),
                      itemCount: 10,
                      padding: EdgeInsets.symmetric(horizontal: 16.sp),
                      separatorBuilder: (_, __) => 16.verticalSpace,
                      itemBuilder: (context, index) {
                        return AllAddressItemWidget(
                          address: Address(
                            id: 1,
                            name: 'dummy',
                            phone: '01000000000',
                            city: 'city',
                            address: 'dummy address',
                            buildingNumber: '5',
                            flatNumber: '1',
                            isDefault: 1,
                            notes: 'notes',
                          ),
                          index: index,
                        );
                      },
                    ),
                  );
                }

                final isAddressListEmpty =
                    addressCubit.addressesModel == null ||
                        addressCubit.addressesModel!.data.data.isEmpty;

                return SmartRefresher(
                  controller: refreshController,
                  onRefresh: () async {
                    await context.read<AddressCubit>().getAllAddresses();
                    refreshController.refreshCompleted();
                  },
                  header: CustomHeaderInRefreshIndicatorWidget(),
                  child: isAddressListEmpty
                      ? SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: MediaQuery.of(context).size.height * 0.5,
                            child: EmptyWidget(
                              imagePath:
                                  'assets/images/svgs/emptyAddresses.svg',
                              title: 'address.noSavedAddresses'.tr(),
                              description:
                                  'address.addANewAddressToDeliver'.tr(),
                            ),
                          ),
                        )
                      : ListView.separated(
                          physics: const AlwaysScrollableScrollPhysics(),
                          itemCount:
                              addressCubit.addressesModel!.data.data.length,
                          padding: EdgeInsets.symmetric(horizontal: 16.sp),
                          separatorBuilder: (_, __) => 16.verticalSpace,
                          itemBuilder: (context, index) {
                            final address =
                                addressCubit.addressesModel!.data.data[index];

                            return AllAddressItemWidget(
                              address: address,
                              isSelected:
                                  index == addressCubit.selectedAddressIdex,
                              onTap: () {
                                PersistentNavBarNavigator.pushNewScreen(
                                  context,
                                  screen: BlocProvider.value(
                                    value: addressCubit..fillData(address),
                                    child: EditAddressScreen(
                                      address: address,
                                    ),
                                  ),
                                  withNavBar: true,
                                  pageTransitionAnimation:
                                      PageTransitionAnimation.cupertino,
                                );
                              },
                              onPressedDelete: () {
                                addressCubit.deleteAddress(
                                  addressId: address.id,
                                );
                              },
                              index: index,
                            );
                          },
                        ),
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.all(20.sp),
        child: CustomButtonWidget(
          text: "address.addNewAddress".tr(),
          onPressed: () {
            PersistentNavBarNavigator.pushNewScreen(
              context,
              screen: BlocProvider(
                create: (context) => AddressCubit((getIt())),
                child: AddNewAddressScreen(),
              ),
            );
          },
        ),
      ),
    );
  }
}
