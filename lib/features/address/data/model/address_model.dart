import 'package:json_annotation/json_annotation.dart';
part 'address_model.g.dart';

@JsonSerializable()
class AddressesModel {
  final String status;
  final String error;
  final int code;
  final AddressData data;

  AddressesModel({
    required this.status,
    required this.error,
    required this.code,
    required this.data,
  });

  factory AddressesModel.fromJson(Map<String, dynamic> json) => _$AddressesModelFromJson(json);
  Map<String, dynamic> toJson() => _$AddressesModelToJson(this);
}

@JsonSerializable()
class AddressData {
  final List<Address> data;
  final Links links;
  final Meta meta;

  AddressData({
    required this.data,
    required this.links,
    required this.meta,
  });

  factory AddressData.fromJson(Map<String, dynamic> json) => _$AddressDataFromJson(json);
  Map<String, dynamic> toJson() => _$AddressDataToJson(this);
}

@JsonSerializable()
class Address {
  final int id;
  final String name;
  final String phone;
  final String city;
  final String address;
  final String buildingNumber;
  final String flatNumber;
  final String? long;
  final String? lat;
   int isDefault;
  final String notes;

  Address({
    required this.id,
    required this.name,
    required this.phone,
    required this.city,
    required this.address,
    required this.buildingNumber,
    required this.flatNumber,
    this.long,
    this.lat,
    required this.isDefault,
    required this.notes,
  });

  factory Address.fromJson(Map<String, dynamic> json) => _$AddressFromJson(json);
  Map<String, dynamic> toJson() => _$AddressToJson(this);
}

@JsonSerializable()
class Links {
  final String first;
  final String last;
  final String? prev;
  final String? next;

  Links({
    required this.first,
    required this.last,
    this.prev,
    this.next,
  });

  factory Links.fromJson(Map<String, dynamic> json) => _$LinksFromJson(json);
  Map<String, dynamic> toJson() => _$LinksToJson(this);
}

@JsonSerializable()
class Meta {
  @JsonKey(name: 'current_page', defaultValue: 0)
  final int currentPage;

  @JsonKey(defaultValue: 0)
  final int from;

  @JsonKey(name: 'last_page', defaultValue: 0)
  final int lastPage;

  final List<PageLink> links;
  final String path;

  @JsonKey(name: 'per_page', defaultValue: 0)
  final int perPage;

  @JsonKey(defaultValue: 0)
  final int to;

  @JsonKey(defaultValue: 0)
  final int total;

  Meta({
    required this.currentPage,
    required this.from,
    required this.lastPage,
    required this.links,
    required this.path,
    required this.perPage,
    required this.to,
    required this.total,
  });

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);
  Map<String, dynamic> toJson() => _$MetaToJson(this);
}

@JsonSerializable()
class PageLink {
  final String? url;
  final String label;
  final bool active;

  PageLink({
    this.url,
    required this.label,
    required this.active,
  });

  factory PageLink.fromJson(Map<String, dynamic> json) => _$PageLinkFromJson(json);
  Map<String, dynamic> toJson() => _$PageLinkToJson(this);
}