import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class AddressesApiServices {
  AddressesApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Get All Addresses
  Future<Response?> getAllAddresses() async {
    return _dioFactory.get(endPoint: EndPoints.getAllAddresses);
  }

  /// Add Address
  Future<Response?> addAddress({
    required String name,
    required String phone,
    required String address,
    required String city,
    required String buildingNumber,
    required String flatNumber,
    String? notes,
    int? isDefault,
  }) async {
    return _dioFactory.post(endPoint: EndPoints.addAddress, data: {
      'name': name,
      'phone': phone,
      'address': address,
      'city': city,
      'building_number': buildingNumber,
      'flat_number': flatNumber,
      if (notes != null) 'notes': notes,
      if (isDefault != null) 'is_default': isDefault,
    });
  }

  /// Edit Address
  Future<Response?> editAddress({
    required int addressId,
    required String name,
    required String phone,
    required String address,
    required String city,
    required String buildingNumber,
    required String flatNumber,
    String? notes,
    int? isDefault,
  }) async {
    return _dioFactory.post(endPoint: EndPoints.editAddress(addressId), data: {
      'name': name,
      'phone': phone,
      'address': address,
      'city': city,
      'building_number': buildingNumber,
      'flat_number': flatNumber,
      if (notes != null) 'notes': notes,
      if (isDefault != null) 'is_default': isDefault,
    });
  }

  /// Delete Address
  Future<Response?> deleteAddress({required int addressId}) async {
    return _dioFactory.post(endPoint: EndPoints.deleteAddress(addressId));
  }
}
