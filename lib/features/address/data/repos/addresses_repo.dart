import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/address/data/api_servies/api_services.dart';
import 'package:tegra_ecommerce_app/features/address/data/model/address_model.dart';

class AddressesRepository {
  final AddressesApiServices addressesApiServices;

  AddressesRepository(this.addressesApiServices);

  /// Get All Addresses
  Future<ApiResult<AddressesModel>> getAllAddresses() async {
    final response = await addressesApiServices.getAllAddresses();
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        AddressesModel addressesModel = AddressesModel.fromJson(response.data);
        return ApiResult.success(addressesModel);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Add Address
  Future<ApiResult<String>> addAddress({
    required String name,
    required String phone,
    required String address,
    required String city,
    required String buildingNumber,
    required String flatNumber,
    String? notes,
    int? isDefault,
    required BuildContext context,
  }) async {
    try {
      final response = await addressesApiServices.addAddress(
        name: name,
        phone: phone,
        address: address,
        city: city,
        buildingNumber: buildingNumber,
        flatNumber: flatNumber,
        notes: notes,
        isDefault: isDefault,
      );

      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success('Address Added Successfully');
      } else {
        final Map<String, dynamic>? responseData =
            response.data is Map<String, dynamic> ? response.data : null;

        final errorMessage =
            responseData != null && responseData.containsKey('message')
                ? responseData['message'].toString()
                : 'An unknown error occurred';

        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(
        //     content: Text(errorMessage),
        //     backgroundColor: Colors.red,
        //   ),
        // );

        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, responseData),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Edit Address
  Future<ApiResult<String>> editAddress({
    required int addressId,
    required String name,
    required String phone,
    required String address,
    required String city,
    required String buildingNumber,
    required String flatNumber,
    String? notes,
    int? isDefault,
  }) async {
    try {
      final response = await addressesApiServices.editAddress(
        addressId: addressId,
        name: name,
        phone: phone,
        address: address,
        city: city,
        buildingNumber: buildingNumber,
        flatNumber: flatNumber,
        notes: notes,
        isDefault: isDefault,
      );

      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success('Address Updated Successfully');
      } else {
        logError(response.data['message']);
        return ApiResult.failure(
          ServerException.fromResponse(
            response.statusCode,
            response.data['message'].toString(),
          ),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Delete Address
  Future<ApiResult<String>> deleteAddress({required int addressId}) async {
    try {
      final response =
          await addressesApiServices.deleteAddress(addressId: addressId);
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success('Address Deleted Successfully');
      } else {
        logError(response.data['message']);
        return ApiResult.failure(
          ServerException.fromResponse(
            response.statusCode,
            response.data['message'].toString(),
          ),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
