import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_helper.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_keys.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/features/address/data/model/address_model.dart';
import 'package:tegra_ecommerce_app/features/address/data/repos/addresses_repo.dart';

part 'address_state.dart';

class AddressCubit extends Cubit<AddressState> {
  AddressCubit(this.addressesRepository) : super(AddressInitial());

  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController noOfApartmentController = TextEditingController();
  final TextEditingController noOfFloorController = TextEditingController();
  final TextEditingController notesController = TextEditingController();

  final ValueNotifier<bool> isDefaultAddress = ValueNotifier(false);
  final formKey = GlobalKey<FormState>();

  final AddressesRepository addressesRepository;
  AddressesModel? addressesModel;
  bool isLoadingMore = false;

  Future getAllAddresses() async {
    emit(GetAllAddressesLoading());
    final result = await addressesRepository.getAllAddresses();
    result.when(success: (data) async {
      addressesModel = data;

      final defaultAddress = data.data.data.firstWhere(
        (address) => address.isDefault == 1,
        orElse: () => Address(
          id: 0,
          name: "",
          phone: "",
          city: "",
          address: "No default address found",
          buildingNumber: "",
          flatNumber: "",
          long: null,
          lat: null,
          isDefault: 0,
          notes: "",
        ),
      );
      if (defaultAddress.id != 0) {
        await CacheHelper.saveData(
            key: CacheKeys.addressCity, value: defaultAddress.city);
        await CacheHelper.saveData(
            key: CacheKeys.addressId, value: defaultAddress.id);
        selectedAddressIdex =
            data.data.data.indexWhere((address) => address.isDefault == 1);
      } else {
        selectedAddressIdex = -1;
        await CacheHelper.removeData(key: CacheKeys.addressCity);
        await CacheHelper.removeData(key: CacheKeys.addressId);
      }
      emit(GetAllAddressesSuccess());
    }, failure: (error) {
      emit(GetAllAddressesError());
    });
    print(selectedAddressIdex);
  }

  Future loadAddressPagination() async {
    if (isLoadingMore ||
        (addressesModel?.data.meta.currentPage ?? 1) >=
            (addressesModel?.data.meta.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(GetAllAddressesLoadingMore());

    final nextPage = addressesModel?.data.meta.currentPage ?? 1;
    final result = await addressesRepository.getAllAddresses();

    result.when(success: (data) {
      addressesModel?.data.data.addAll(data.data.data);
      addressesModel?.data.meta.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(GetAllAddressesSuccess());
    }, failure: (error) {
      isLoadingMore = false;
      emit(GetAllAddressesError());
    });
  }

  void toggleDefaultAddress(bool value) {
    isDefaultAddress.value = value;
    emit(ChosseAddressChangeState());
  }

  Future addAddress({required BuildContext context}) async {
    showLoading();
    emit(AddAddressLoading());
    final result = await addressesRepository.addAddress(
      name: nameController.text,
      phone: phoneController.text,
      address: addressController.text,
      city: cityController.text,
      buildingNumber: noOfFloorController.text,
      flatNumber: noOfApartmentController.text,
      notes: notesController.text,
      isDefault: isDefaultAddress.value ? 1 : 0,
      context: context,
    );
    result.when(success: (data) {
      hideLoading();
      emit(AddAddressSuccess());
    }, failure: (error) {
      hideLoading();
      emit(AddAddressError());
    });
  }

  Future editAddress({required int addressId, bool isDeafult = false}) async {
    showLoading();
    emit(EditAddressLoading());
    final result = await addressesRepository.editAddress(
      addressId: addressId,
      name: nameController.text,
      phone: phoneController.text,
      address: addressController.text,
      city: cityController.text,
      buildingNumber: noOfFloorController.text,
      flatNumber: noOfApartmentController.text,
      notes: notesController.text,
      isDefault: isDefaultAddress.value ? 1 : 0,
    );
    result.when(success: (data) async {
      hideLoading();
      if (!isDeafult) {
        await getAllAddresses();
      }
      emit(EditAddressSuccess());
    }, failure: (error) {
      hideLoading();
      emit(EditAddressError());
    });
  }

  Future deleteAddress({required int addressId}) async {
    showLoading();
    emit(DeleteAddressLoading());

    final isDefaultMatch = addressesModel!.data.data
        .where((address) => address.isDefault == 1)
        .any((address) => address.id == addressId);

    if (isDefaultMatch) {
      await CacheHelper.removeData(key: CacheKeys.addressCity);
      await CacheHelper.removeData(key: CacheKeys.addressId);
      selectedAddressIdex = -1;
    }
    final result =
        await addressesRepository.deleteAddress(addressId: addressId);
    result.when(success: (data) {
      hideLoading();
      emit(DeleteAddressSuccess());
    }, failure: (error) {
      hideLoading();
      emit(DeleteAddressError());
    });
  }

  int selectedAddressIdex = -1;
  void changeSelected(int v, bool check) async {
    if (check) {
      selectedAddressIdex = v;
      addressesModel!.data.data[v].isDefault = 1;
      await CacheHelper.saveData(
          key: CacheKeys.addressCity, value: addressesModel!.data.data[v].city);
      await CacheHelper.saveData(
          key: CacheKeys.addressId, value: addressesModel!.data.data[v].id);
    } else {
      addressesModel!.data.data[v].isDefault = 0;
      selectedAddressIdex = -1;
      await CacheHelper.removeData(key: CacheKeys.addressCity);
      await CacheHelper.removeData(key: CacheKeys.addressId);
    }
    emit(ChosseAddressChangeState());
    fillData(addressesModel!.data.data[v]);
    await editAddress(
        addressId: addressesModel!.data.data[v].id, isDeafult: true);
    clearData();
  }

  void fillData(Address address) {
    nameController.text = address.name;
    phoneController.text = address.phone;
    cityController.text = address.city;
    addressController.text = address.address;
    noOfFloorController.text = address.buildingNumber;
    noOfApartmentController.text = address.flatNumber;
    notesController.text = address.notes;
    isDefaultAddress.value = address.isDefault == 1;
  }

  void clearData() {
    nameController.clear();
    phoneController.clear();
    cityController.clear();
    addressController.clear();
    noOfFloorController.clear();
    noOfApartmentController.clear();
    notesController.clear();
    isDefaultAddress.value = false;
  }

  @override
  Future<void> close() {
    nameController.dispose();
    phoneController.dispose();
    cityController.dispose();
    addressController.dispose();
    noOfApartmentController.dispose();
    noOfFloorController.dispose();
    notesController.dispose();
    isDefaultAddress.dispose();
    return super.close();
  }
}
