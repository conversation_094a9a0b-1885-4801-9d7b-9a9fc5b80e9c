part of 'address_cubit.dart';

@immutable
sealed class AddressState {}

final class AddressInitial extends AddressState {}

/// Get All Addresses States
final class GetAllAddressesLoading extends AddressState {}

final class GetAllAddressesSuccess extends AddressState {}

final class GetAllAddressesError extends AddressState {}

final class GetAllAddressesLoadingMore extends AddressState {}

/// Add Address States
final class AddAddressLoading extends AddressState {}

final class AddAddressSuccess extends AddressState {}

final class AddAddressError extends AddressState {}

/// Edit Address States
final class EditAddressLoading extends AddressState {}

final class EditAddressSuccess extends AddressState {}

final class EditAddressError extends AddressState {}

/// Delete Address States
final class DeleteAddressLoading extends AddressState {}

final class DeleteAddressSuccess extends AddressState {}

final class DeleteAddressError extends AddressState {}

/// address Selected
final class ChosseAddressChangeState extends AddressState {}
