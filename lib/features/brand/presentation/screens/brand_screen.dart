import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/main_app_bar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/empty/empty_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/brand/business_logic/brand_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand/presentation/screens/brand_filter_screen.dart';
import 'package:tegra_ecommerce_app/features/brand/presentation/widgets/brands_in_brand_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/brand/presentation/widgets/brands_in_brand_widget.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/screens/search_screen.dart';

class BrandScreen extends StatelessWidget {
  const BrandScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    final brandCubit = context.read<BrandCubit>();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (brandCubit.list[brandCubit.selectedIndex].name ==
            "brands.foryou".tr()) {
          brandCubit.loadMoreBrands();
        } else {
          brandCubit.loadMoreBrandsByCategory(brandCubit.selectedCategoryId!);
        }
      }
    });

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MainAppBarWidget(),
          SizedBox(height: 20.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: CustomTextFormFieldWidget(
              readOnly: true,
              onTap: () {
                PersistentNavBarNavigator.pushNewScreen(
                  context,
                  screen: BlocProvider(
                    create: (context) => SearchCubit(getIt()),
                    child: SearchScreen(),
                  ),
                  withNavBar: true,
                  pageTransitionAnimation: PageTransitionAnimation.cupertino,
                );
              },
              backgroundColor: AppColors.neutralColor100,
              borderRadius: AppConstants.borderRadius + 2,
              borderColor: AppColors.neutralColor100,
              borderWidth: 1.w,
              prefixIcon: SvgPicture.asset(
                'assets/images/svgs/search_icon.svg',
                fit: BoxFit.scaleDown,
              ),
              hintText: 'home.whatAreYouThinkingAbout'.tr(),
              hintStyle: Styles.contentRegular.copyWith(
                color: AppColors.neutralColor1200,
              ),
            ),
          ),
          SizedBox(height: 8.h),
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 20.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "brands.brands".tr(),
                        style: Styles.heading5.copyWith(
                          color: Colors.black,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          PersistentNavBarNavigator.pushNewScreen(
                            context,
                            screen: BlocProvider.value(
                              value: brandCubit,
                              child: BrandFilterScreen(),
                            ),
                            withNavBar: true,
                            pageTransitionAnimation:
                                PageTransitionAnimation.fade,
                          );
                        },
                        child: Image.asset(
                          Assets.assetsImagesPngsSortIcon,
                          fit: BoxFit.scaleDown,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20.h),
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        BlocBuilder<BrandCubit, BrandState>(
                          buildWhen: (previous, current) =>
                              current is GetAllBrandsLoading ||
                              current is GetAllBrandsSuccess ||
                              current is GetBrandsByCategoryError ||
                              current is GetBrandsByCategorySuccess ||
                              current is GetBrandsByCategoryLoading ||
                              current is GetAllBrandsError,
                          builder: (context, state) {
                            return state is GetBrandsByCategoryLoading ||
                                    brandCubit.allBrandsByCategoryModel ==
                                        null ||
                                    state is GetAllBrandsLoading
                                ? BrandsInBrandSkeletonizerWidget()
                                : brandCubit.allBrandsByCategoryModel!
                                                .brandsData ==
                                            null ||
                                        brandCubit.allBrandsByCategoryModel!
                                            .brandsData!.data!.isEmpty
                                    ? EmptyWidget(
                                        imagePath:
                                            'assets/images/svgs/emptyCategory.svg',
                                        title: 'NoBrands'.tr(),
                                        description: 'NoBrandsDescription'.tr(),
                                      )
                                    : BrandsInBrandWidget(
                                        scrollController: scrollController,
                                      );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
