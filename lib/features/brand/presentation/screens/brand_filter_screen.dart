import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/drop_down/custom_drop_down_widget.dart';
import 'package:tegra_ecommerce_app/features/brand/business_logic/brand_cubit.dart';
import 'package:tegra_ecommerce_app/features/category/business_logic/category_cubit.dart';
import 'package:tegra_ecommerce_app/features/category/data/model/all_categories_model.dart';

class BrandFilterScreen extends StatelessWidget {
  const BrandFilterScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final brandCubit = context.read<BrandCubit>();

    return Scaffold(
      backgroundColor: Color(0xffffffff),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppBarWidget(
            rowWidget: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "filter.filterAndSelect".tr(),
                  style: Styles.heading1.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
                Row(
                  spacing: 16.sp,
                  children: [
                    Container(
                      padding: EdgeInsets.all(13.sp),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(AppConstants.borderRadius),
                        color: Colors.white,
                      ),
                      child: SvgPicture.asset(
                        Assets.assetsImagesSvgsDeleteIcon,
                        fit: BoxFit.scaleDown,
                      ),
                    ),
                    InkWell(
                      onTap: () => context.pop(),
                      child: Container(
                        padding: EdgeInsets.all(13.sp),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius:
                              BorderRadius.circular(AppConstants.borderRadius),
                          color: Colors.white,
                        ),
                        child: SvgPicture.asset(
                          Assets.assetsImagesSvgsCloseIcon,
                          fit: BoxFit.scaleDown,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    32.verticalSpace,
                    Text(
                      "filter.theCategory".tr(),
                      style: Styles.heading5.copyWith(
                        color: Colors.black,
                      ),
                    ),
                    12.verticalSpace,
                    BlocProvider(
                      create: (context) =>
                          CategoryCubit(getIt())..getAllCategories(),
                      child: BlocBuilder<CategoryCubit, CategoryState>(
                        builder: (context, state) {
                          if (state is GetAllCategoriesLoading) {
                            return Skeletonizer(
                              enabled: true,
                              child: CustomDropdownButtonWidget<String>(
                                hint: 'filter.allBrands'.tr(),
                                items: ["PUMA", "Adidas", "Nike"],
                                getItemText: (item) => item,
                                getItemIcon: (item) {
                                  switch (item) {
                                    case "PUMA":
                                      return Assets.assetsImagesPngsAdidasCover;
                                    case "Adidas":
                                      return Assets.assetsImagesPngsAdidasCover;
                                    case "Nike":
                                      return Assets.assetsImagesPngsAdidasCover;
                                    default:
                                      return "";
                                  }
                                },
                                onChanged: (selectedBrand) {},
                              ),
                            );
                          } else {
                            return CustomDropdownButtonWidget<Category>(
                              hint: 'filter.allCategories'.tr(),
                              isString: false,
                              value: brandCubit.selectedCategory,
                              items: BlocProvider.of<CategoryCubit>(context)
                                  .categoriesModel!
                                  .categories!,
                              getItemText: (item) => item.name ?? "Unknown",
                              getItemIcon: (item) => item.image ?? "",
                              onChanged: (selectedCategory) {
                                brandCubit.selectBrand(
                                  selectedCategory!,
                                );
                                logSuccess(
                                    " category ${brandCubit.selectedCategory!.name!.toString()}");
                              },
                            );
                          }
                        },
                      ),
                    ),
                    32.verticalSpace,
                  ],
                ),
              ),
            ),
          ),
          CustomButtonWidget(
            width: double.infinity,
            margin: EdgeInsets.symmetric(horizontal: 20.sp),
            text: "filter.filtering".tr(),
            onPressed: () {
              context.pop();
            },
          )
        ],
      ),
    );
  }
}
