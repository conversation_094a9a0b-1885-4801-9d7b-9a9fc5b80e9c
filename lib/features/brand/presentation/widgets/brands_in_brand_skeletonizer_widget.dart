import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/features/brand/data/models/brands_by_category/brands_by_category_model.dart';
// import 'package:tegra_ecommerce_app/features/brand/data/models/brand_data_model.dart';
import 'package:tegra_ecommerce_app/features/brand/presentation/widgets/brand_item_widget.dart';

class BrandsInBrandSkeletonizerWidget extends StatelessWidget {
  const BrandsInBrandSkeletonizerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: 5,
      child: GridView.builder(
        shrinkWrap: true,
        itemCount: 16,
        padding: EdgeInsets.zero,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisExtent: 220,
            mainAxisSpacing: 32.h,
            crossAxisSpacing: 10.h),
        itemBuilder: (context, index) {
          return Skeletonizer(
            enabled: true,
            child: BrandItemWidget(
              isHome: false,
              brandData: BrandDataItem(
                logo: 'assets/images/pngs/brand1_image.png',
                banner: 'assets/images/pngs/clothes_icon.png',
                rate: 42,
                text: 'adsfpihadvdsafghdsafvguoihwdfh9wdh',
                title: 'aqwed',
              ),
            ),
          );
        },
      ),
    );
  }
}
