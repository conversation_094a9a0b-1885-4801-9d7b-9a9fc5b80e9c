import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/features/brand/business_logic/brand_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand/presentation/widgets/brand_item_widget.dart';

class BrandsInBrandWidget extends StatelessWidget {
  const BrandsInBrandWidget({
    super.key,
    required this.scrollController,
  });

  final ScrollController scrollController;

  @override
  Widget build(BuildContext context) {
    final brandCubit = context.read<BrandCubit>();

    return Expanded(
      flex: 5,
      child: GridView.builder(
        controller: scrollController,
        shrinkWrap: true,
        itemCount:
            brandCubit.allBrandsByCategoryModel!.brandsData!.data!.length,
        padding: EdgeInsets.zero,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisExtent: 220,
          mainAxisSpacing: 32.h,
          crossAxisSpacing: 10.h,
        ),
        itemBuilder: (context, index) {
          final brand =
              brandCubit.allBrandsByCategoryModel!.brandsData!.data![index];

          return BrandItemWidget(
            isHome: false,
            brandData: brand,
          );
        },
      ),
    );
  }
}
