import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/cache_network_image/imag_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/container/clip_container_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/gradient_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/total_rate_widget.dart';
import 'package:tegra_ecommerce_app/features/brand/data/models/brands_by_category/brands_by_category_model.dart';
import 'package:tegra_ecommerce_app/features/brand_details/business_logic/brand_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand_details/presentation/screens/brand_details_screen.dart';

class BrandItemWidget extends StatelessWidget {
  final bool isHome;
  final BrandDataItem brandData;

  const BrandItemWidget({
    super.key,
    required this.isHome,
    required this.brandData,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // context.pushNamed(
        //   Routes.brandDetailsScreen,
        //   arguments: brandData,
        // );

        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: BlocProvider(
            create: (context) =>
                BrandDetailsCubit(getIt())..getProductsByBrands(brandData.id!)..getBrandDetails( brandData.id!),
            child: BrandDetailsScreen(),
          ),
          withNavBar: true,
          pageTransitionAnimation: PageTransitionAnimation.cupertino,
        );
      },
      child: Container(
        width: isHome ? 218.w : 128.w,
        padding: EdgeInsets.only(left: isHome ? 10.w : 0),
        child: Column(
          spacing: 8.h,
          children: [
            Container(
              alignment: Alignment.topCenter,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadius,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(AppConstants.borderRadius),
                        topRight: Radius.circular(AppConstants.borderRadius),
                      ),
                      child: CacheNetworkImagesWidget(
                        image: brandData.banner!,
                        height: isHome ? 116.h : 69.h,
                        boxFit: BoxFit.cover,
                      ),
                    ),
                  ),
                  const ClipContainerInSpecificProductWidget(),
                  const GradientInSpecificProductWidget(),
                  Positioned(
                    bottom: -12.r,
                    left: 0,
                    right: 0,
                    child: CircleAvatar(
                      radius: isHome ? 40.r : 25.r,
                      backgroundColor: Colors.transparent,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(40.r),
                        child: CacheNetworkImagesWidget(
                          image: brandData.logo!,
                          height: isHome ? 69.h : 49.h,
                          width: isHome ? 69.w : 49.w,
                          boxFit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 8.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          brandData.title!,
                          style: Styles.captionEmphasis.copyWith(
                            color: Colors.black,
                          ),
                        ),
                        TotalRateWidget(rate: brandData.rate!.toString()),
                      ],
                    ),
                    Expanded(
                      child: Text(
                        brandData.text!,
                        style: Styles.footnoteRegular.copyWith(
                          color: AppColors.neutralColor600,
                        ),
                        maxLines: 6,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'brands.viewProducts'.tr(),
                          style: Styles.captionRegular.copyWith(
                            color: AppColors.neutralColor1200,
                          ),
                        ),
                        2.horizontalSpace,
                        Icon(
                          size: 11.sp,
                          Icons.arrow_forward_ios_outlined,
                          color: AppColors.neutralColor1200,
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
