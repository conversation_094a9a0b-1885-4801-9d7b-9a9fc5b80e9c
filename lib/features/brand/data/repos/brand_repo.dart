import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/brand/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/brand/data/models/brand_data/brand_data_model.dart';
import 'package:tegra_ecommerce_app/features/brand/data/models/brands_by_category/brands_by_category_model.dart';

class BrandRepository {
  final BrandApiServices brandApiServices;

  BrandRepository(this.brandApiServices);

  ///  Get All Brands
  Future<ApiResult<BrandsByCategoryModel>> getAllBrands() async {
    final response = await brandApiServices.getAllBrands();
    try {
      if (response!.statusCode == 200) {
        BrandsByCategoryModel brandDataModel =
            BrandsByCategoryModel.fromJson(response.data);

        return ApiResult.success(brandDataModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Get Brands By Category
  Future<ApiResult<BrandsByCategoryModel>> getBrandsByCategory(
      int categoryId) async {
    final response = await brandApiServices.getBrandsByCategory(categoryId);

    try {
      if (response!.statusCode == 200) {
        BrandsByCategoryModel brandsByCategoryModel =
            BrandsByCategoryModel.fromJson(response.data);
        return ApiResult.success(brandsByCategoryModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }
}
