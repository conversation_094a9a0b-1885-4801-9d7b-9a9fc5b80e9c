import 'package:json_annotation/json_annotation.dart';

part 'brand_data_model.g.dart';

@JsonSerializable()
class BrandDataModel {
  @JsonKey(name: 'data')
  BrandData? brands;

  String? status;
  String? error;
  int? code;

  BrandDataModel({this.brands, this.status, this.error, this.code});

  factory BrandDataModel.fromJson(Map<String, dynamic> json) =>
      _$BrandDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$BrandDataModelToJson(this);
}

@JsonSerializable()
class BrandData {
  int? id;
  String? title;
  String? text;
  String? logo;
  String? banner;
  int? rate;
  @Json<PERSON>ey(name: 'isRatedBefore') // Added this field
  bool? isRatedBefore;

  BrandData({
    this.id,
    this.title,
    this.text,
    this.logo,
    this.banner,
    this.rate,
    this.isRatedBefore,
  });

  factory BrandData.fromJson(Map<String, dynamic> json) =>
      _$BrandDataFromJson(json);

  Map<String, dynamic> toJson() => _$BrandDataTo<PERSON>son(this);
}
