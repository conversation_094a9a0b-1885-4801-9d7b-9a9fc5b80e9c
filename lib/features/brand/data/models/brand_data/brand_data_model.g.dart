// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'brand_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BrandDataModel _$BrandDataModelFromJson(Map<String, dynamic> json) =>
    BrandDataModel(
      brands: json['data'] == null
          ? null
          : BrandData.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String?,
      error: json['error'] as String?,
      code: (json['code'] as num?)?.toInt(),
    );

Map<String, dynamic> _$BrandData<PERSON>odelToJson(BrandDataModel instance) =>
    <String, dynamic>{
      'data': instance.brands,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

BrandData _$BrandDataFromJson(Map<String, dynamic> json) => BrandData(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      text: json['text'] as String?,
      logo: json['logo'] as String?,
      banner: json['banner'] as String?,
      rate: (json['rate'] as num?)?.toInt(),
      isRatedBefore: json['isRatedBefore'] as bool?,
    );

Map<String, dynamic> _$BrandDataToJson(BrandData instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'text': instance.text,
      'logo': instance.logo,
      'banner': instance.banner,
      'rate': instance.rate,
      'isRatedBefore': instance.isRatedBefore,
    };
