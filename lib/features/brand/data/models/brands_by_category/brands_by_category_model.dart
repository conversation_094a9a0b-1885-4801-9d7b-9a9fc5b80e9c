// import 'package:json_annotation/json_annotation.dart';

// part 'brands_by_category_model.g.dart';

// @JsonSerializable()
// class BrandsByCategoryModel {
//   @JsonKey(name: 'data')
//   BrandsData? brandsData;

//   String? status;
//   String? error;
//   int? code;

//   BrandsByCategoryModel({this.brandsData, this.status, this.error, this.code});

//   factory BrandsByCategoryModel.fromJson(Map<String, dynamic> json) =>
//       _$BrandsByCategoryModelFromJson(json);

//   Map<String, dynamic> toJson() => _$BrandsByCategoryModelToJson(this);
// }

// @JsonSerializable()
// class BrandsData {
//   List<BrandData>? data;
//   Links? links;
//   Meta? meta;

//   BrandsData({this.data, this.links, this.meta});

//   factory BrandsData.fromJson(Map<String, dynamic> json) =>
//       _$BrandsDataFromJson(json);

//   Map<String, dynamic> toJson() => _$BrandsDataToJson(this);
// }

// @JsonSerializable()
// class BrandData {
//   int? id;
//   String? title;
//   String? text;
//   String? logo;
//   String? banner;
//   int? rate;

//   BrandData({
//     this.id,
//     this.title,
//     this.text,
//     this.logo,
//     this.banner,
//     this.rate,
//   });

//   factory BrandData.fromJson(Map<String, dynamic> json) =>
//       _$BrandDataFromJson(json);

//   Map<String, dynamic> toJson() => _$BrandDataToJson(this);
// }

// @JsonSerializable()
// class Links {
//   String? first;
//   String? last;
//   String? prev;
//   String? next;

//   Links({this.first, this.last, this.prev, this.next});

//   factory Links.fromJson(Map<String, dynamic> json) =>
//       _$LinksFromJson(json);

//   Map<String, dynamic> toJson() => _$LinksToJson(this);
// }

// @JsonSerializable()
// class Meta {
//   int? current_page;
//   int? from;
//   int? last_page;
//   List<PageLink>? links;
//   String? path;
//   int? per_page;
//   int? to;
//   int? total;

//   Meta({
//     this.current_page,
//     this.from,
//     this.last_page,
//     this.links,
//     this.path,
//     this.per_page,
//     this.to,
//     this.total,
//   });

//   factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);

//   Map<String, dynamic> toJson() => _$MetaToJson(this);
// }

// @JsonSerializable()
// class PageLink {
//   String? url;
//   String? label;
//   bool? active;

//   PageLink({this.url, this.label, this.active});

//   factory PageLink.fromJson(Map<String, dynamic> json) =>
//       _$PageLinkFromJson(json);

//   Map<String, dynamic> toJson() => _$PageLinkToJson(this);
// }
import 'package:json_annotation/json_annotation.dart';

part 'brands_by_category_model.g.dart';

@JsonSerializable()
class BrandsByCategoryModel {
  @JsonKey(name: 'data')
  BrandsData? brandsData;

  @JsonKey(name: 'status')
  String? status;

  @JsonKey(name: 'error')
  String? error;

  @JsonKey(name: 'code')
  int? code;

  BrandsByCategoryModel({this.brandsData, this.status, this.error, this.code});

  factory BrandsByCategoryModel.fromJson(Map<String, dynamic> json) =>
      _$BrandsByCategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$BrandsByCategoryModelToJson(this);
}

@JsonSerializable()
class BrandsData {
  @JsonKey(name: 'data')
  List<BrandDataItem>? data;

  @JsonKey(name: 'links')
  Links? links;

  @JsonKey(name: 'meta')
  Meta? meta;

  BrandsData({this.data, this.links, this.meta});

  factory BrandsData.fromJson(Map<String, dynamic> json) =>
      _$BrandsDataFromJson(json);

  Map<String, dynamic> toJson() => _$BrandsDataToJson(this);
}

@JsonSerializable()
class BrandDataItem {
  @JsonKey(name: 'id')
  int? id;

  @JsonKey(name: 'title')
  String? title;

  @JsonKey(name: 'text')
  String? text;

  @JsonKey(name: 'logo')
  String? logo;

  @JsonKey(name: 'banner')
  String? banner;

  @JsonKey(name: 'rate')
  int? rate;

  BrandDataItem({
    this.id,
    this.title,
    this.text,
    this.logo,
    this.banner,
    this.rate,
  });

  factory BrandDataItem.fromJson(Map<String, dynamic> json) =>
      _$BrandDataItemFromJson(json);

  Map<String, dynamic> toJson() => _$BrandDataItemToJson(this);
}

@JsonSerializable()
class Links {
  @JsonKey(name: 'first')
  String? first;

  @JsonKey(name: 'last')
  String? last;

  @JsonKey(name: 'prev')
  String? prev;

  @JsonKey(name: 'next')
  String? next;

  Links({this.first, this.last, this.prev, this.next});

  factory Links.fromJson(Map<String, dynamic> json) => _$LinksFromJson(json);

  Map<String, dynamic> toJson() => _$LinksToJson(this);
}

@JsonSerializable()
class Meta {
  @JsonKey(name: 'current_page')
  int? currentPage;

  @JsonKey(name: 'from')
  int? from;

  @JsonKey(name: 'last_page')
  int? lastPage;

  @JsonKey(name: 'links')
  List<PageLink>? links;

  @JsonKey(name: 'path')
  String? path;

  @JsonKey(name: 'per_page')
  int? perPage;

  @JsonKey(name: 'to')
  int? to;

  @JsonKey(name: 'total')
  int? total;

  Meta({
    this.currentPage,
    this.from,
    this.lastPage,
    this.links,
    this.path,
    this.perPage,
    this.to,
    this.total,
  });

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);

  Map<String, dynamic> toJson() => _$MetaToJson(this);
}

@JsonSerializable()
class PageLink {
  @JsonKey(name: 'url')
  String? url;

  @JsonKey(name: 'label')
  String? label;

  @JsonKey(name: 'active')
  bool? active;

  PageLink({this.url, this.label, this.active});

  factory PageLink.fromJson(Map<String, dynamic> json) =>
      _$PageLinkFromJson(json);

  Map<String, dynamic> toJson() => _$PageLinkToJson(this);
}
