// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'brands_by_category_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BrandsByCategoryModel _$BrandsByCategoryModelFromJson(
        Map<String, dynamic> json) =>
    BrandsByCategoryModel(
      brandsData: json['data'] == null
          ? null
          : BrandsData.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String?,
      error: json['error'] as String?,
      code: (json['code'] as num?)?.toInt(),
    );

Map<String, dynamic> _$BrandsByCategoryModelToJson(
        BrandsByCategoryModel instance) =>
    <String, dynamic>{
      'data': instance.brandsData,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

BrandsData _$BrandsDataFromJson(Map<String, dynamic> json) => BrandsData(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => BrandDataItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      links: json['links'] == null
          ? null
          : Links.fromJson(json['links'] as Map<String, dynamic>),
      meta: json['meta'] == null
          ? null
          : Meta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$BrandsDataToJson(BrandsData instance) =>
    <String, dynamic>{
      'data': instance.data,
      'links': instance.links,
      'meta': instance.meta,
    };

BrandDataItem _$BrandDataItemFromJson(Map<String, dynamic> json) =>
    BrandDataItem(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      text: json['text'] as String?,
      logo: json['logo'] as String?,
      banner: json['banner'] as String?,
      rate: (json['rate'] as num?)?.toInt(),
    );

Map<String, dynamic> _$BrandDataItemToJson(BrandDataItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'text': instance.text,
      'logo': instance.logo,
      'banner': instance.banner,
      'rate': instance.rate,
    };

Links _$LinksFromJson(Map<String, dynamic> json) => Links(
      first: json['first'] as String?,
      last: json['last'] as String?,
      prev: json['prev'] as String?,
      next: json['next'] as String?,
    );

Map<String, dynamic> _$LinksToJson(Links instance) => <String, dynamic>{
      'first': instance.first,
      'last': instance.last,
      'prev': instance.prev,
      'next': instance.next,
    };

Meta _$MetaFromJson(Map<String, dynamic> json) => Meta(
      currentPage: (json['current_page'] as num?)?.toInt(),
      from: (json['from'] as num?)?.toInt(),
      lastPage: (json['last_page'] as num?)?.toInt(),
      links: (json['links'] as List<dynamic>?)
          ?.map((e) => PageLink.fromJson(e as Map<String, dynamic>))
          .toList(),
      path: json['path'] as String?,
      perPage: (json['per_page'] as num?)?.toInt(),
      to: (json['to'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MetaToJson(Meta instance) => <String, dynamic>{
      'current_page': instance.currentPage,
      'from': instance.from,
      'last_page': instance.lastPage,
      'links': instance.links,
      'path': instance.path,
      'per_page': instance.perPage,
      'to': instance.to,
      'total': instance.total,
    };

PageLink _$PageLinkFromJson(Map<String, dynamic> json) => PageLink(
      url: json['url'] as String?,
      label: json['label'] as String?,
      active: json['active'] as bool?,
    );

Map<String, dynamic> _$PageLinkToJson(PageLink instance) => <String, dynamic>{
      'url': instance.url,
      'label': instance.label,
      'active': instance.active,
    };
