import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class BrandApiServices {
  BrandApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Get All Brands
  Future<Response?> getAllBrands() async {
    return _dioFactory.get(
      endPoint: EndPoints.getAllBrands,
    );
  }

  /// Get Brands By Category
  Future<Response?> getBrandsByCategory(int categoryId) async {
    return _dioFactory.get(
      endPoint: EndPoints.getBrandsByCategory(categoryId),
    );
  }
}
