import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/features/brand/data/models/brands_by_category/brands_by_category_model.dart';
import 'package:tegra_ecommerce_app/features/brand/data/repos/brand_repo.dart';
import 'package:tegra_ecommerce_app/features/category/data/model/all_categories_model.dart';

part 'brand_state.dart';

class BrandCubit extends Cubit<BrandState> {
  BrandCubit(this.brandRepository) : super(BrandInitial());

  final BrandRepository brandRepository;
  BrandsByCategoryModel? allBrandsByCategoryModel;
  bool isLoadingMore = false;
  int currentPage = 1;

  int selectedIndex = 0;
  int? selectedCategoryId;

  List<Category> list = [
    Category(name: "brands.foryou".tr()),
  ];
  void selectBrand(Category category) async {
    emit(BrandSelectedState());

    selectedCategory = category;
    allBrandsByCategoryModel = null;
    await getBrandsByCategory(category.id ?? 0);
  }

  Category? selectedCategory;

  Future getAllBrands() async {
    emit(GetAllBrandsLoading());
    final result = await brandRepository.getAllBrands();

    result.when(
      success: (data) {
        allBrandsByCategoryModel = data;
        emit(GetAllBrandsSuccess());
      },
      failure: (errorHandler) {
        emit(GetAllBrandsError());
      },
    );
  }

  Future<void> loadMoreBrands() async {
    if (isLoadingMore ||
        (allBrandsByCategoryModel?.brandsData?.meta?.currentPage ?? 1) >=
            (allBrandsByCategoryModel?.brandsData?.meta?.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(GetAllBrandsLoadingMore());

    final nextPage = currentPage + 1;
    final result = await brandRepository.getAllBrands();

    result.when(success: (data) {
      allBrandsByCategoryModel?.brandsData?.data
          ?.addAll(data.brandsData?.data ?? []);

      currentPage = data.brandsData?.meta?.currentPage ?? nextPage;

      isLoadingMore = false;
      emit(GetAllBrandsSuccess());
    }, failure: (errorHandler) {
      isLoadingMore = false;
      emit(GetAllBrandsError());
    });
  }

  Future<void> getBrandsByCategory(int categoryId) async {
    emit(GetBrandsByCategoryLoading());
    selectedCategoryId = categoryId;
    final result = await brandRepository.getBrandsByCategory(categoryId);

    result.when(
      success: (data) {
        allBrandsByCategoryModel = data;
        emit(GetBrandsByCategorySuccess());
      },
      failure: (errorHandler) {
        emit(GetBrandsByCategoryError());
      },
    );
  }

  Future<void> loadMoreBrandsByCategory(int categoryId) async {
    if (isLoadingMore ||
        (allBrandsByCategoryModel?.brandsData?.meta?.currentPage ?? 1) >=
            (allBrandsByCategoryModel?.brandsData?.meta?.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(GetBrandsByCategoryLoadingMore());

    final nextPage = currentPage + 1;
    final result = await brandRepository.getBrandsByCategory(categoryId);

    result.when(success: (data) {
      allBrandsByCategoryModel?.brandsData?.data
          ?.addAll(data.brandsData?.data ?? []);
      currentPage = data.brandsData?.meta?.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(GetBrandsByCategorySuccess());
    }, failure: (errorHandler) {
      isLoadingMore = false;
      emit(GetBrandsByCategoryError());
    });
  }
}
