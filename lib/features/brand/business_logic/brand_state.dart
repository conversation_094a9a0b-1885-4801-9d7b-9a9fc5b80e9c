part of 'brand_cubit.dart';

@immutable
sealed class BrandState {}

final class BrandInitial extends BrandState {}

final class BrandSelectedState extends BrandState {}

/// Get All Brands
final class GetAllBrandsLoading extends BrandState {}

final class GetAllBrandsSuccess extends BrandState {}

final class GetAllBrandsError extends BrandState {}

final class GetAllBrandsLoadingMore extends BrandState {}


/// Get Brands By Category
final class GetBrandsByCategoryLoading extends BrandState {}

final class GetBrandsByCategorySuccess extends BrandState {}

final class GetBrandsByCategoryError extends BrandState {}

final class GetBrandsByCategoryLoadingMore extends BrandState {}