import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/features/intro/business_logic/intro_cubit.dart';

class IntroScreen extends StatelessWidget {
  const IntroScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<IntroCubit, IntroState>(
      listener: (context, state) {
        if (state is IntroNavigateToMain) {
          Navigator.pushReplacementNamed(context, Routes.mainlayoutScreen);
        } else if (state is IntroNavigateToLogin) {
          Navigator.pushReplacementNamed(context, Routes.onBoardingScreen);
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.scaffoldBackground,
        body: Stack(
          children: [
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                    "assets/images/pngs/bg_image_in_splash_screen.png",
                  ),
                  fit: BoxFit.fill,
                ),
              ),
              child: Center(
                child: SvgPicture.asset(
                  'assets/images/svgs/logo_green_color.svg',
                  colorFilter: ColorFilter.mode(
                      AppColors.scaffoldBackground, BlendMode.srcIn),
                ),
              ),
            ),
            Positioned(
              bottom: 63.h,
              right: 0,
              left: 0,
              child: Center(
                child: SizedBox(
                  height: 40.h,
                  width: 40.w,
                  child: LoadingIndicator(
                    indicatorType: Indicator.lineSpinFadeLoader,
                    // indicatorType: Indicator.lineSpinFadeLoader,
                    colors: [Colors.white], // Customize the color
                    backgroundColor: Colors.transparent,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
