
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

part 'intro_state.dart';

class IntroCubit extends Cubit<IntroState> {
  IntroCubit() : super(IntroInitial());

  void checkTokenAndNavigate() async {
    emit(IntroLoading());
    // AppConstants.userToken = await CacheHelper.getSecuredString(key: CacheKeys.userToken);

    await Future.delayed(const Duration(seconds: 3));
    if (AppConstants.userToken == null) {
      emit(IntroNavigateToLogin());
    } else {
      emit(IntroNavigateToMain());
    }
  }
}
