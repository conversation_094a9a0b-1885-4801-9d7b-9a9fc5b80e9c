import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/categories/category_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/category_item_in_home_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/search/search_list_view_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/search/search_list_view_widget_skeletonizer.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/business_logic/product_by_categrory_cubit.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/screens/search_screen.dart';

import '../../../home/<USER>/widgets/search/search_grid_view_widget.dart';

class ProductsByCategoryScreen extends StatelessWidget {
  const ProductsByCategoryScreen({super.key, required this.categroyName});
  final String categroyName;

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ProcutsByCategoryCubit>();

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 16.w,
              children: [
                BackButtonWidget(
                  onTap: () => context.pop(),
                ),
                Text(
                  categroyName,
                  style: Styles.heading2.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 20.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: 10.h,
                      bottom: 10.h,
                    ),
                    child: CustomTextFormFieldWidget(
                      readOnly: true,
                      onTap: () {
                        PersistentNavBarNavigator.pushNewScreen(
                          context,
                          screen: BlocProvider(
                            create: (context) => SearchCubit(getIt()),
                            child: SearchScreen(),
                          ),
                          withNavBar: true,
                          pageTransitionAnimation:
                              PageTransitionAnimation.cupertino,
                        );
                      },
                      backgroundColor: AppColors.neutralColor100,
                      borderRadius: AppConstants.borderRadius + 2,
                      borderColor: AppColors.neutralColor100,
                      borderWidth: 1.w,
                      prefixIcon: SvgPicture.asset(
                        'assets/images/svgs/search_icon.svg',
                        fit: BoxFit.scaleDown,
                      ),
                      hintText: 'home.whatAreYouThinkingAbout'.tr(),
                      hintStyle: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ),
                ),
                // SizedBox(width: 12.w),
                // BlocBuilder<ProcutsByCategoryCubit, ProcutsByCategoryState>(
                //   buildWhen: (previous, current) =>
                //       current is SearchModeToggleState,
                //   builder: (context, state) {
                //     if (!cubit.isListSearch) {
                //       return InkWell(
                //         onTap: () {
                //           cubit.toggleSearchMode();
                //         },
                //         child: SvgPicture.asset(
                //           'assets/images/svgs/four_dots_icon.svg',
                //           fit: BoxFit.scaleDown,
                //         ),
                //       );
                //     } else {
                //       return InkWell(
                //         onTap: () {
                //           cubit.toggleSearchMode();
                //         },
                //         child: SvgPicture.asset(
                //           'assets/images/svgs/drawer_icon.svg',
                //           fit: BoxFit.scaleDown,
                //         ),
                //       );
                //     }
                //   },
                // ),
                // // SizedBox(width: 16.w),
                // InkWell(
                //   onTap: () {
                //     PersistentNavBarNavigator.pushNewScreen(
                //       context,
                //       screen: BlocProvider(
                //         create: (context) =>
                //             SearchCubit(getIt()),
                //         child: FilterScreen(),
                //       ),
                //       withNavBar: true,
                //       pageTransitionAnimation: PageTransitionAnimation.fade,
                //     );
                //   },
                //   //context.pushNamed(Routes.filterScreen),
                //   child: Image.asset(
                //     Assets.assetsImagesPngsSortIcon,
                //     fit: BoxFit.scaleDown,
                //   ),
                // ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 16.h),
                    BlocBuilder<ProcutsByCategoryCubit, ProcutsByCategoryState>(
                      buildWhen: (previous, current) =>
                          current is GetChildrenCategoriesSuccess ||
                          current is GetChildrenCategoriesLoading ||
                          current is GetChildrenCategoriesError,
                      builder: (context, state) {
                        return cubit.categoriesModel == null ||
                                state is GetChildrenCategoriesLoading
                            ? CategorySkeletonizerWidget()
                            : SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: cubit.categoriesModel!.categories ==
                                            null ||
                                        cubit.categoriesModel!.categories!
                                            .isEmpty
                                    ? SizedBox()
                                    : Row(
                                        children: List.generate(
                                          cubit.categoriesModel!.categories!
                                              .length,
                                          (index) {
                                            final category = cubit
                                                .categoriesModel!
                                                .categories![index];

                                            return CategoryItemInHomeWidget(
                                              imageUrl: category.image,
                                              categoryName: category.name!,
                                              onTap: () {
                                                PersistentNavBarNavigator
                                                    .pushNewScreen(
                                                  context,
                                                  screen: BlocProvider(
                                                    create: (context) =>
                                                        ProcutsByCategoryCubit(
                                                            getIt())
                                                          ..getChildrenCategories(
                                                              category.id!)
                                                          ..intController()
                                                          ..getProcutsByCategory(
                                                              categoryId:
                                                                  category.id!,
                                                              page: 1),
                                                    child:
                                                        ProductsByCategoryScreen(
                                                      categroyName:
                                                          category.name!,
                                                    ),
                                                  ),
                                                  withNavBar: true,
                                                  pageTransitionAnimation:
                                                      PageTransitionAnimation
                                                          .fade,
                                                );
                                                // context.pushNamed(
                                                //   Routes
                                                //       .productsByCategoryScreen,
                                                //   arguments:
                                                //       ProductsByCategoryArgumets(
                                                //     categoryId: category.id!,
                                                //     name: category.name!,
                                                //   ),
                                                // );
                                              },
                                            );
                                          },
                                        ),
                                      ),
                              );
                      },
                    ),
                    // SizedBox(height: 16.h),
                    // Container(
                    //   width: double.infinity,
                    //   padding: EdgeInsets.all(8.sp),
                    //   child: SingleChildScrollView(
                    //     scrollDirection: Axis.horizontal,
                    //     child: Row(
                    //       children: List.generate(
                    //         AppConstants.subFiltersList.length,
                    //         (index) {
                    //           return SubFilterInProductByCategoryWidget(
                    //             title: AppConstants.subFiltersList[index],
                    //             isLastSubFilter: index ==
                    //                 AppConstants.subFiltersList.length - 1,
                    //           );
                    //         },
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    SizedBox(height: 16.h),
                    BlocBuilder<ProcutsByCategoryCubit, ProcutsByCategoryState>(
                      buildWhen: (previous, current) =>
                          current is SearchModeToggleState ||
                          current is GetProductsByCategoryLoading ||
                          current is GetProductsByCategorySuccess ||
                          current is GetProductsByCategoryError,
                      builder: (context, state) {
                        if (state is GetProductsByCategorySuccess ||
                            state is SearchModeToggleState) {
                          if (cubit.isListSearch) {
                            return SearchListViewWidget(
                              products: context
                                  .read<ProcutsByCategoryCubit>()
                                  .productsByCategoryModel!
                                  .data!
                                  .product!,
                            );
                          } else {
                            return SearchGridViewWidget(
                              products: context
                                  .read<ProcutsByCategoryCubit>()
                                  .productsByCategoryModel!
                                  .data!
                                  .product!,
                            );
                          }
                        }

                        return SearchListViewWidgetSkeletonizer();
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
