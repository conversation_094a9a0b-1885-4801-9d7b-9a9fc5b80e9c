import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class ProductByCategroryApiServices {
  ProductByCategroryApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  Future<Response?> getChildrenCategories(int mainCategoryId) async {
    return _dioFactory.get(
      endPoint: EndPoints.getChildrenCategories(mainCategoryId),
    );
  }

  Future<Response?> getProcutsByCategory(
      {required int categoryId, required int page}) async {
    return _dioFactory.get(
      endPoint: EndPoints.getProductsByCategory(categoryId),
      data: {
        'page': page,
      },
    );
  }
}
