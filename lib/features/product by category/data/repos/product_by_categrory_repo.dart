import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/category/data/model/all_categories_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/data/api_services/product_by_categrory_api_services.dart';

class ProductByCategroryRepo {
  final ProductByCategroryApiServices homeApiServices;

  ProductByCategroryRepo(this.homeApiServices);

  Future<ApiResult<CategoriesModel>> getChildrenCategories(
      int mainCategoryId) async {
    final response =
        await homeApiServices.getChildrenCategories(mainCategoryId);

    try {
      if (response!.statusCode == 200) {
        CategoriesModel categoriesModel =
            CategoriesModel.fromJson(response.data);
        return ApiResult.success(categoriesModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  Future<ApiResult<ProductCardPaginatedModel>> getProcutsByCategory(
      {required int categoryId, required int page}) async {
    final response = await homeApiServices.getProcutsByCategory(
        categoryId: categoryId, page: page);
    try {
      if (response!.statusCode == 200) {
        ProductCardPaginatedModel productCardModel =
            ProductCardPaginatedModel.fromJson(response.data);
        return ApiResult.success(productCardModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
