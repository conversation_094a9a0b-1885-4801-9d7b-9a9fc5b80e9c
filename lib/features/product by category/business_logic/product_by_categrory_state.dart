part of 'product_by_categrory_cubit.dart';

abstract class ProcutsByCategoryState {}

final class ProductByCategoryInitial extends ProcutsByCategoryState {}

final class HomeIndexChanged extends ProcutsByCategoryState {}

final class SearchModeToggleState extends ProcutsByCategoryState {}

final class CheckIsSearchState extends ProcutsByCategoryState {}

final class FilterCategorySelected extends ProcutsByCategoryState {}

final class FilterOfferSelected extends ProcutsByCategoryState {}

final class FilterRatingSelected extends ProcutsByCategoryState {}



final class GetChildrenCategoriesLoading extends ProcutsByCategoryState {}

final class GetChildrenCategoriesSuccess extends ProcutsByCategoryState {}

final class GetChildrenCategoriesError extends ProcutsByCategoryState {}


final class GetProductsByCategoryLoading extends ProcutsByCategoryState {}

final class GetProductsByCategorySuccess extends ProcutsByCategoryState {}

final class GetProductsByCategoryError extends ProcutsByCategoryState {}

final class LoadProductsLoadingState extends ProcutsByCategoryState {}

final class LoadProductsSuccessState extends ProcutsByCategoryState {}

final class LoadProductsErrorState extends ProcutsByCategoryState {}
