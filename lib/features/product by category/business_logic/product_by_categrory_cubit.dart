import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/features/category/data/model/all_categories_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/data/repos/product_by_categrory_repo.dart';

part 'product_by_categrory_state.dart';

class ProcutsByCategoryCubit extends Cubit<ProcutsByCategoryState> {
  ProcutsByCategoryCubit(this.homeRepository)
      : super(ProductByCategoryInitial());

  final ProductByCategroryRepo homeRepository;

  ProductCardPaginatedModel? productsByCategoryModel;

  bool isListSearch = true;
  CategoriesModel? categoriesModel;

  void toggleSearchMode() {
    isListSearch = !isListSearch;
    emit(SearchModeToggleState());
  }

 

  Future getChildrenCategories(int mainCategoryId) async {
    emit(GetChildrenCategoriesLoading());
    final result = await homeRepository.getChildrenCategories(mainCategoryId);

    result.when(
      success: (data) {
        categoriesModel = data;
        emit(GetChildrenCategoriesSuccess());
      },
      failure: (errorHandler) {
        emit(GetChildrenCategoriesError());
      },
    );
  }

  Future getProcutsByCategory(
      {required int categoryId, required int page}) async {
    emit(GetProductsByCategoryLoading());
    final result = await homeRepository.getProcutsByCategory(
        categoryId: categoryId, page: page);

    result.when(
      success: (data) {
        productsByCategoryModel = data;
        emit(GetProductsByCategorySuccess());
      },
      failure: (errorHandler) {
        emit(GetProductsByCategoryError());
      },
    );
  }

  final ScrollController scrollController = ScrollController();
  int currentPage = 1;
  int? lastpage;
  int? categoryId;
  void intController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (state is LoadProductsLoadingState) {
          return;
        } else {
          loadMoreData();
        }
      }
    });
  }

  Future<void> loadMoreData() async {
    if (currentPage < lastpage!) {
      currentPage++;

      emit(LoadProductsLoadingState());

      final result = await homeRepository.getProcutsByCategory(
          categoryId: categoryId!, page: currentPage);

      result.when(success: (products) {
        productsByCategoryModel!.data!.product!.addAll(products.data!.product!);
        emit(GetProductsByCategorySuccess());
      }, failure: (error) {
        emit(LoadProductsErrorState());
      });
    }
  }


}
