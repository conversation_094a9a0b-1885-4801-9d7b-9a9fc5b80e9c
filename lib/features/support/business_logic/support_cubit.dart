import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/features/support/data/models/ticket_data_model/ticket_data_model.dart';
import 'package:tegra_ecommerce_app/features/support/data/repos/support_repo.dart';

part 'support_state.dart';

class SupportCubit extends Cubit<SupportState> {
  SupportCubit(this.supportRepository) : super(SupportInitial());

  final SupportRepository supportRepository;
  final TextEditingController titleController = TextEditingController();
  final TextEditingController messageController = TextEditingController();

  String? selectedPriority;

  TicketDataModel? ticketDataModel;

  /// Toggle Priority
  void selectPriority(String priority) {
    selectedPriority = priority;
    emit(PrioritySelectedState(selectedPriority!));
  }

  /// Get All Tickets
  Future<void> getAllTickets() async {
    emit(GetAllTicketsLoading());
    final result = await supportRepository.getAllTickets();
    result.when(success: (data) {
      ticketDataModel = data;
      emit(GetAllTicketsSuccess());
    }, failure: (error) {
      emit(GetAllTicketsError());
    });
  }



  /// Create ticket
  Future<void> createTicket({
    required String priority,
    required String title,
    required String message,
  }) async {
    showLoading();
    emit(CreateTicketLoading());
    final result = await supportRepository.createTicket(
      priority: priority,
      title: title,
      message: message,
    );
    result.when(success: (data) async {
      hideLoading();
      emit(CreateTicketSuccess());
    }, failure: (error) {
      hideLoading();
      emit(CreateTicketError());
    });
  }

  @override
  Future<void> close() {
    titleController.dispose();
    messageController.dispose();
    return super.close();
  }
}
