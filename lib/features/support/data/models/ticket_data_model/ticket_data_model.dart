import 'package:json_annotation/json_annotation.dart';

part 'ticket_data_model.g.dart';

@JsonSerializable()
class TicketDataModel {
  final String status;
  final String error;
  final int code;
  final List<Ticket> data;

  TicketDataModel({
    required this.status,
    required this.error,
    required this.code,
    required this.data,
  });

  factory TicketDataModel.fromJson(Map<String, dynamic> json) => _$TicketDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$TicketDataModelToJson(this);
}

@JsonSerializable()
class Ticket {
  final int id;
  final String title;
  final String user;
  final String status;
  final String priority;

  Ticket({
    required this.id,
    required this.title,
    required this.user,
    required this.status,
    required this.priority,
  });

  factory Ticket.fromJson(Map<String, dynamic> json) => _$TicketFromJson(json);

  Map<String, dynamic> toJson() => _$TicketToJson(this);
}
