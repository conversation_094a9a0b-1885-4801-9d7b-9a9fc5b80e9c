// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketDataModel _$TicketDataModelFromJson(Map<String, dynamic> json) =>
    TicketDataModel(
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
      data: (json['data'] as List<dynamic>)
          .map((e) => Ticket.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TicketDataModelToJson(TicketDataModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
      'data': instance.data,
    };

Ticket _$TicketFromJson(Map<String, dynamic> json) => Ticket(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
      user: json['user'] as String,
      status: json['status'] as String,
      priority: json['priority'] as String,
    );

Map<String, dynamic> _$TicketToJson(Ticket instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'user': instance.user,
      'status': instance.status,
      'priority': instance.priority,
    };
