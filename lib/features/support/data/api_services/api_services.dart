import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class SupportApiServices {
  SupportApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Create ticket
  Future<Response?> createTicket({
    required String priority,
    required String title,
    required String message,
  }) async {
    return _dioFactory.post(endPoint: EndPoints.createTicket, data: {
      'priority': priority,
      'title': title,
      'message': message,
    });
  }

  /// Get All Tickets
  Future<Response?> getAllTickets() async {
    return _dioFactory.get(endPoint: EndPoints.getAllTickets);
  }


}
