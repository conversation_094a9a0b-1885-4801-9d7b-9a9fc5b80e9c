import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/support/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/support/data/models/ticket_data_model/ticket_data_model.dart';

class SupportRepository {
  final SupportApiServices supportApiServices;

  SupportRepository(this.supportApiServices);

  /// Get All Tickets
  Future<ApiResult<TicketDataModel>> getAllTickets() async {
    final response = await supportApiServices.getAllTickets();
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        TicketDataModel ticketDataModel =
            TicketDataModel.fromJson(response.data);
        return ApiResult.success(ticketDataModel);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }



  /// Create ticket
  Future<ApiResult<String>> createTicket({
    required String priority,
    required String title,
    required String message,
  }) async {
    final response = await supportApiServices.createTicket(
      priority: priority,
      title: title,
      message: message,
    );
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success('Ticket Created Successfully');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
