import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class AllTicketNumberSkeletonizerWidget extends StatelessWidget {
  const AllTicketNumberSkeletonizerWidget({super.key});


  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.sp, horizontal: 8.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(color: AppColors.neutralColor500),
          // color: AppColors.white,
        ),
        child: Row(
          children: [
            Column(
              spacing: 4.sp,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'support.ticketNumber'.tr(),
                  style: Styles.heading5.copyWith(
                      fontWeight: FontWeight.w700,
                      fontSize: 16.sp,
                      color: AppColors.neutralColor1200),
                ),
                Text(
                  "#TICKET-111",
                  style: Styles.contentRegular.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: 16.sp,
                      color: AppColors.neutralColor600),
                ),
              ],
            ),
            Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 14.sp,
              color: AppColors.primaryColor900,
            ),
            // SvgPicture.asset(
            //   Assets.assetsImagesSvgsArrowLeftIcon,
            //   color: AppColors.primaryColor900,
            // ),
          ],
        ),
      ),
    );
  }
}