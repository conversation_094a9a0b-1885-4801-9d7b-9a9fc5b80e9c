import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/drop_down/custom_drop_down_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/support/business_logic/support_cubit.dart';

class ContactSupportScreen extends StatelessWidget {
  const ContactSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        physics: NeverScrollableScrollPhysics(),
        child: Column(
          children: [
            AppBarWidget(
              rowWidget: Row(
                spacing: 16.w,
                children: [
                  BackButtonWidget(onTap: () => context.pop()),
                  Text(
                    'support.contactSupport'.tr(),
                    style: Styles.heading2.copyWith(
                      color: AppColors.scaffoldBackground,
                    ),
                  ),
                ],
              ),
            ),
            30.verticalSpace,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                children: [
                  BlocBuilder<SupportCubit, SupportState>(
                    buildWhen: (previous, current) =>
                        current is PrioritySelectedState,
                    builder: (context, state) {
                      var cubit = context.read<SupportCubit>();
                      return CustomDropdownButtonWidget<String>(
                        hint: 'support.messageStatus'.tr(),
                        items: [
                          'support.urgent'.tr(),
                          'support.medium'.tr(),
                          'support.normal'.tr()
                        ],
                        value: cubit.selectedPriority,
                        onChanged: (selectedValue) {
                          cubit.selectPriority(selectedValue!);
                        },
                      );
                    },
                  ),
                  16.verticalSpace,
                  CustomTextFormFieldWidget(
                    controller: context.read<SupportCubit>().titleController,
                    labelText: 'support.messageTitle'.tr(),
                    labelStyle: Styles.contentEmphasis.copyWith(
                      color: AppColors.neutralColor1200,
                    ),
                    keyboardType: TextInputType.text,
                    textInputAction: TextInputAction.next,
                    prefixIcon: Icon(
                      Iconsax.message,
                      size: 23.sp,
                    ),
                    backgroundColor: Colors.white,
                    borderRadius: AppConstants.borderRadius,
                  ),
                  16.verticalSpace,
                  CustomTextFormFieldWidget(
                    controller: context.read<SupportCubit>().messageController,
                    borderRadius: AppConstants.borderRadius,
                    // height: 80.h,
                    backgroundColor: Colors.white,
                    borderColor: AppColors.neutralColor200,
                    labelText: 'support.messageDetails'.tr(),
                    maxLines: 7,
                    labelStyle: Styles.contentEmphasis.copyWith(
                      color: AppColors.neutralColor1200,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BlocConsumer<SupportCubit, SupportState>(
        listener: (context, state) {
          if (state is CreateTicketSuccess) {
            context.pop();
            context.read<SupportCubit>().getAllTickets();
          }
        },
        builder: (context, state) {
          final supportCubit = context.read<SupportCubit>();

          return SafeArea(
            minimum: EdgeInsets.all(20.sp),
            child: CustomButtonWidget(
              text: 'support.send'.tr(),
              onPressed: () {
                if (supportCubit.titleController.text.isEmpty ||
                    supportCubit.messageController.text.isEmpty) {}
                supportCubit.createTicket(
                  priority: supportCubit.selectedPriority! ==
                          'support.urgent'.tr()
                      ? 'high '
                      : supportCubit.selectedPriority! == 'support.medium'.tr()
                          ? 'medium'
                          : 'low',
                  title: supportCubit.titleController.text,
                  message: supportCubit.messageController.text,
                );
                // AppConstants.popUpSendDialog(context);
              },
            ),
          );
        },
      ),
    );
  }
}
