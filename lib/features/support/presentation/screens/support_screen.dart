import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/empty/empty_widget.dart';
import 'package:tegra_ecommerce_app/features/support/business_logic/support_cubit.dart';
import 'package:tegra_ecommerce_app/features/support/presentation/screens/contact_support.dart';
import 'package:tegra_ecommerce_app/features/support/presentation/widgets/all_ticket_number_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/support/presentation/widgets/all_ticket_number_widget.dart';

class SupportScreen extends StatelessWidget {
  const SupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 16.w,
              children: [
                BackButtonWidget(onTap: () => context.pop()),
                Text(
                  'support.supportText'.tr(),
                  style: Styles.heading2.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
              ],
            ),
          ),
          // 30.verticalSpace,
          BlocBuilder<SupportCubit, SupportState>(
            builder: (context, state) {
              final cubit = context.read<SupportCubit>();

              return Expanded(
                child: state is GetAllTicketsLoading ||
                        cubit.ticketDataModel == null
                    ? ListView.separated(
                        shrinkWrap: true,
                        itemCount: 10,
                        padding:
                            EdgeInsets.only(left: 16.w, right: 16.w, top: 30.h),
                        separatorBuilder: (_, __) => 16.verticalSpace,
                        itemBuilder: (context, index) {
                          return AllTicketNumberSkeletonizerWidget();
                        },
                      )
                    : cubit.ticketDataModel!.data.isEmpty
                        ? EmptyWidget(
                            imagePath: '',
                            title: 'support.noTickets'.tr(),
                            description: 'support.noTicketsDescription'.tr(),
                          )
                        : ListView.separated(
                            shrinkWrap: true,
                            itemCount: cubit.ticketDataModel!.data.length,
                            padding: EdgeInsets.only(
                                left: 16.w, right: 16.w, top: 30.h),
                            separatorBuilder: (_, __) => 16.verticalSpace,
                            itemBuilder: (context, index) {
                              return AllTicketNumberWidget(
                                id: cubit.ticketDataModel!.data[index].id,
                              );
                            },
                          ),
              );
            },
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.all(20.sp),
        child: BlocProvider.value(
          value: SupportCubit(getIt()),
          child: CustomButtonWidget(
            text: 'support.contactSupport'.tr(),
            onPressed: () {
              PersistentNavBarNavigator.pushNewScreen(
                context,
                screen: BlocProvider(
                  create: (context) => SupportCubit(getIt())
                    ..getAllTickets().then((value) {
                      context.read<SupportCubit>().getAllTickets();
                    }),
                  child: ContactSupportScreen(),
                ),
                withNavBar: true,
                pageTransitionAnimation: PageTransitionAnimation.cupertino,
              );
              // context.pushNamed(Routes.contactSupportScreen).then(
              //   (value) {
              //     context.read<SupportCubit>().getAllTickets();
              //   },
              // );
            },
          ),
        ),
      ),
    );
  }
}
