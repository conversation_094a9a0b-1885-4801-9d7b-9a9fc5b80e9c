
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/text/custom_text_rich_widget.dart';
import 'package:tegra_ecommerce_app/features/on_boarding/presentation/widgets/animated_overlay_widget.dart';

class OnBoardingScreen extends StatefulWidget {
  const OnBoardingScreen({super.key});

  @override
  OnboardingScreenState createState() => OnboardingScreenState();
}

class OnboardingScreenState extends State<OnBoardingScreen> {
  int _currentPage = 0;
  final PageController _pageController = PageController();

  List<Map<String, String>> onboardingData = [
    {
      "image": "assets/images/pngs/phone_onboarding_1.png",
      "title": 'onboarding.shopEase'.tr(),
      "highlight": 'onboarding.supportLocal'.tr(),
      "subtitle": 'onboarding.enjoyTheQuality'.tr(),
      "overlay": "assets/images/svgs/search_widget_image.svg",
    },
    {
      "image": "assets/images/pngs/phone_onboarding_2.png",
      "title": 'onboarding.allBrandsNeed'.tr(),
      "highlight": 'onboarding.saudi'.tr(),
      "subtitle": 'onboarding.inOnePlace'.tr(),
      "overlay": "assets/images/pngs/overlay_brands.png",
    },
    {
      "image": "assets/images/pngs/phone_onboarding_3.png",
      "title": 'onboarding.shopBestBrands'.tr(),
      "highlight": 'onboarding.saudi'.tr(),
      "subtitle": 'onboarding.fashionToFurniture'.tr(),
      "overlay": "assets/images/pngs/overlay_fashion.png",
    },
  ];

  void _nextPage() {
    if (_currentPage < onboardingData.length - 1) {
      _pageController.nextPage(
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    } else {
      context.pushNamed(Routes.startScreen);
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              physics: NeverScrollableScrollPhysics(),
              itemCount: onboardingData.length,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    Stack(
                      alignment: AlignmentDirectional.bottomCenter,
                      children: [
                        /// BG Image
                        Image.asset(
                          'assets/images/pngs/onboarding_bg_image.png',
                          width: size.width,
                          height: size.height * 0.64,
                          fit: BoxFit.fill,
                        ),

                        /// Phone Image
                        Positioned(
                          bottom: 0,
                          child: ClipRRect(
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(25.r),
                              topLeft: Radius.circular(25.r),
                            ),
                            child: Image.asset(
                              onboardingData[index]["image"]!,
                              width: size.width * 0.49,
                              height: size.height * 0.45,
                              fit: BoxFit.fill,
                            ),
                          ),
                        ),

                        /// Overlay Image with Animation
                        AnimatedOverlayWidget(
                          index: index,
                          currentPage: _currentPage,
                          size: size,
                          overlay: onboardingData[index]["overlay"]!,
                        ),

                        /// Mask Image
                        Positioned(
                          right: 0,
                          left: 0,
                          bottom: -20.sp,
                          child: SvgPicture.asset(
                            'assets/images/svgs/mask_image.svg',
                            fit: BoxFit.fill,
                          ),
                        ),
                      ],
                    ),
                    28.verticalSpace,
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: CustomRichText(
                        text1: onboardingData[index]["title"]!,
                        text2: onboardingData[index]["highlight"]!,
                        textStyle1: Styles.highlightBold.copyWith(
                          color: Colors.black,
                        ),
                        textStyle2: Styles.highlightBold.copyWith(
                          color: AppColors.primaryColor900,
                        ),
                      ),
                    ),
                    10.verticalSpace,
                    Text(
                      onboardingData[index]["subtitle"]!,
                      style: Styles.highlightEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  onPressed: _nextPage,
                  icon: Row(
                    spacing: 4.w,
                    children: [
                      Icon(
                        Icons.arrow_back,
                        color: AppColors.primaryColor900,
                        size: 16.sp,
                      ),
                      Text(
                        'onboarding.next'.tr(),
                        style: Styles.highlightStandard.copyWith(
                          color: AppColors.primaryColor900,
                        ),
                      ),
                    ],
                  ),
                ),
                if (_currentPage > 0)
                  IconButton(
                    onPressed: _previousPage,
                    icon: Row(
                      children: [
                        Text(
                          'onboarding.previous'.tr(),
                          style: Styles.highlightStandard.copyWith(
                            color: AppColors.neutralColor1200,
                          ),
                        ),
                        SizedBox(width: 4.w),
                        Icon(
                          Icons.arrow_forward,
                          color: AppColors.neutralColor1200,
                          size: 16.sp,
                        ),
                      ],
                    ),
                  )
                else
                  SizedBox(),
              ],
            ),
          ),
          30.verticalSpace,
        ],
      ),
    );
  }
}