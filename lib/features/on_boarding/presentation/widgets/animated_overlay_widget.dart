
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AnimatedOverlayWidget extends StatefulWidget {
  final int index;
  final int currentPage;
  final Size size;
  final String overlay;

  const AnimatedOverlayWidget({
    super.key,
    required this.index,
    required this.currentPage,
    required this.size,
    required this.overlay,
  });

  @override
  State<AnimatedOverlayWidget> createState() => _AnimatedOverlayWidgetState();
}

class _AnimatedOverlayWidgetState extends State<AnimatedOverlayWidget> {
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    _startAnimation();
  }

  void _startAnimation() {
    Future.delayed(const Duration(milliseconds: 600), () {
      if (mounted) {
        setState(() {
          _isAnimating = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeInOut,
      bottom: widget.index == 2
          ? widget.size.height * 0.19
          : widget.index == 1
          ? widget.size.height * 0.15
          : widget.size.height * 0.328,
      left: widget.index == 0
          ? (_isAnimating
          ? widget.size.width * 0.218 // Moves into position from left
          : -widget.size.width * 0.5) // Starts off-screen on the left
          : widget.index == 2
          ? (_isAnimating
          ? widget.size.width * 0.19 // Moves into position from right
          : widget.size.width) // Starts off-screen on the right
          : widget.index == 1
          ? (widget.currentPage == 1 ? -20 : widget.size.width)
          : null,
      child: widget.overlay.endsWith('.svg')
          ? SvgPicture.asset(
        widget.overlay,
        width: widget.size.width * 0.65,
        height: widget.size.height * 0.03,
        fit: BoxFit.cover,
      )
          : Image.asset(
        widget.overlay,
        width: widget.index == 2
            ? widget.size.width * 0.6
            : widget.size.width * 0.65,
        height: widget.index == 2
            ? widget.size.height * 0.18
            : widget.index == 1
            ? widget.size.height * 0.2
            : widget.size.height * 0.03,
        fit: BoxFit.scaleDown,
      ),
    );
  }
}