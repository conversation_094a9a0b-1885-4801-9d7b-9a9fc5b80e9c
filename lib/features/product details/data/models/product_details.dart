class ShowProductDetails {
  ProductDetailsItem? productDetailsItem;
  String? status;
  String? error;
  int? code;

  ShowProductDetails(
      {this.productDetailsItem, this.status, this.error, this.code});

  ShowProductDetails.fromJson(Map<String, dynamic> json) {
    productDetailsItem =
        json['data'] != null ? ProductDetailsItem.fromJson(json['data']) : null;
    status = json['status'];
    error = json['error'];
    code = json['code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (productDetailsItem != null) {
      data['data'] = productDetailsItem!.toJson();
    }
    data['status'] = status;
    data['error'] = error;
    data['code'] = code;
    return data;
  }
}

class ProductDetailsItem {
  int? id;
  String? name;
  String? description;
  String? thumbnail;
  num? price;
  num? discountRounded;
  num? priceAfterDiscount;
  int? stockAmount;
  List<Details>? details;
  List<String>? availableVariantsAttributes;
  Variants? variants;
  bool? isFavourite;
  ProductDetailsItem(
      {this.id,
      this.name,
      this.description,
      this.thumbnail,
      this.price,
      this.isFavourite,
      this.discountRounded,
      this.priceAfterDiscount,
      this.stockAmount,
      this.details,
      this.availableVariantsAttributes,
      this.variants});

  ProductDetailsItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    thumbnail = json['thumbnail'];
    price = json['price'];
    discountRounded = json['discountRounded'];
    priceAfterDiscount = json['priceAfterDiscount'];
    stockAmount = json['stockAmount'];
    isFavourite = json['isFavourite'];
    if (json['details'] != null) {
      details = <Details>[];
      json['details'].forEach((v) {
        details!.add(Details.fromJson(v));
      });
    }
    availableVariantsAttributes =
        json['availableVariantsAttributes'].cast<String>();
    variants =
        json['variants'] != null ? Variants.fromJson(json['variants']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['thumbnail'] = thumbnail;
    data['price'] = price;
    data['discountRounded'] = discountRounded;
    data['priceAfterDiscount'] = priceAfterDiscount;
    data['stockAmount'] = stockAmount;
    if (details != null) {
      data['details'] = details!.map((v) => v.toJson()).toList();
    }
    data['availableVariantsAttributes'] = availableVariantsAttributes;
    if (variants != null) {
      data['variants'] = variants!.toJson();
    }
    return data;
  }
}

class Details {
  String? key;
  String? value;

  Details({this.key, this.value});

  Details.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    return data;
  }
}

class Variants {
  Map<String, List<Color>> variantMap = {};
  List<Sku>? sku;

  /// ✅ Add SKU separately

  Variants({required this.variantMap, this.sku});

  /// **Parse JSON**
  Variants.fromJson(Map<String, dynamic> json) {
    sku = json['sku'] != null
        ? (json['sku'] as List).map((v) => Sku.fromJson(v)).toList()
        : null;

    /// ✅ Parse SKU separately

    json.forEach((key, value) {
      if (key != 'sku' && value is List) {
        /// ✅ Ignore 'sku' in dynamic map
        variantMap[key] = value.map((v) => Color.fromJson(v)).toList();
      }
    });
  }

  /// **Get a variant list dynamically**
  List<Color>? getVariantList(String key) {
    return variantMap[key];
  }
  // /*
  /// **Get a variant list dynamically**
  /*ist<Color>? getVariantList(String key) {
    var list = variantMap[key];
    var emptyList = [];

    if(list != null){
      if(list.length > 1) {
        for(int i = 0; i < list.length - 1; i++) {
          if(list[i].value != list[i + 1].value) {
            emptyList.add(list[i].value);
          }
        }
      }
    }

*/
  // return emptyList;
  // return variantMap[key];
  // }

  // */

  /// **Convert to JSON**
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (sku != null) {
      data['sku'] = sku!.map((v) => v.toJson()).toList();
    }

    variantMap.forEach((key, value) {
      data[key] = value.map((v) => v.toJson()).toList();
    });

    return data;
  }
}

class Sku {
  int? skuId;
  String? sku;
  int? stockAmount;
  int? price;
  int? priceAfterDiscount;
  int? discountRounded;

  Sku(
      {this.skuId,
      this.sku,
      this.stockAmount,
      this.price,
      this.priceAfterDiscount,
      this.discountRounded});

  Sku.fromJson(Map<String, dynamic> json) {
    skuId = json['skuId'];
    sku = json['sku'];
    stockAmount = json['stockAmount'];
    price = json['price'];
    priceAfterDiscount = json['priceAfterDiscount'];
    discountRounded = json['discountRounded'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['skuId'] = skuId;
    data['sku'] = sku;
    data['stockAmount'] = stockAmount;
    data['price'] = price;
    data['priceAfterDiscount'] = priceAfterDiscount;
    data['discountRounded'] = discountRounded;
    return data;
  }
}

class Color {
  int? skuId;
  String? value;

  Color({this.skuId, this.value});

  Color.fromJson(Map<String, dynamic> json) {
    skuId = json['skuId'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['skuId'] = skuId;
    data['value'] = value;
    return data;
  }
}

// class Variants {
//   List<Sku>? sku;
//   List<Color>? color;
//   List<Color>? size;
//   List<Color>? weight;
//   List<Color>? material;
//
//   Variants({this.sku, this.color, this.size, this.weight, this.material});
//
//   Variants.fromJson(Map<String, dynamic> json) {
//     if (json['sku'] != null) {
//       sku = <Sku>[];
//       json['sku'].forEach((v) {
//         sku!.add(new Sku.fromJson(v));
//       });
//     }
//     if (json['Color'] != null) {
//       color = <Color>[];
//       json['Color'].forEach((v) {
//         color!.add(new Color.fromJson(v));
//       });
//     }
//     if (json['Size'] != null) {
//       size = <Color>[];
//       json['Size'].forEach((v) {
//         size!.add(new Color.fromJson(v));
//       });
//     }
//     if (json['Weight'] != null) {
//       weight = <Color>[];
//       json['Weight'].forEach((v) {
//         weight!.add(new Color.fromJson(v));
//       });
//     }
//     if (json['Material'] != null) {
//       material = <Color>[];
//       json['Material'].forEach((v) {
//         material!.add(new Color.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     if (this.sku != null) {
//       data['sku'] = this.sku!.map((v) => v.toJson()).toList();
//     }
//     if (this.color != null) {
//       data['Color'] = this.color!.map((v) => v.toJson()).toList();
//     }
//     if (this.size != null) {
//       data['Size'] = this.size!.map((v) => v.toJson()).toList();
//     }
//     if (this.weight != null) {
//       data['Weight'] = this.weight!.map((v) => v.toJson()).toList();
//     }
//     if (this.material != null) {
//       data['Material'] = this.material!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }

// class Variants {
//   Map<String, List<Color>> variantMap = {};
//
//   Variants({required this.variantMap});
//
//   Variants.fromJson(Map<String, dynamic> json) {
//     json.forEach((key, value) {
//       if (value is List) {
//         variantMap[key] = value.map((v) => Color.fromJson(v)).toList();
//       }
//     });
//   }
//
//   /// 🔹 **Method to dynamically fetch variant list**
//   List<Color>? getVariantList(String key) {
//     return variantMap[key];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = {};
//     variantMap.forEach((key, value) {
//       data[key] = value.map((v) => v.toJson()).toList();
//     });
//     return data;
//   }
// }
