import 'package:json_annotation/json_annotation.dart';

part 'product_reviews_model.g.dart';

@JsonSerializable()
class ProductReviewsResponse {
  @JsonKey(name: 'data')
  ProductReviewsData? productReviewsData;
  String? status;
  String? error;
  int? code;

  ProductReviewsResponse({this.productReviewsData, this.status, this.error, this.code});

  factory ProductReviewsResponse.fromJson(Map<String, dynamic> json) => _$ProductReviewsResponseFromJson(json);
  Map<String, dynamic> toJson() => _$ProductReviewsResponseToJson(this);
}

@JsonSerializable()
class ProductReviewsData {
  @JsonKey(name: 'data')
  List<Review>? reviews;
  PaginationLinks? links;
  PaginationMeta? meta;

  ProductReviewsData({this.reviews, this.links, this.meta});

  factory ProductReviewsData.fromJson(Map<String, dynamic> json) => _$ProductReviewsDataFromJson(json);
  Map<String, dynamic> toJson() => _$ProductReviewsDataToJson(this);
}

@JsonSerializable()
class Review {
  int? id;
  String? text;
  String? date;
  String? userName;
  String? userImage;

  Review({this.id, this.text, this.date, this.userName, this.userImage});

  factory Review.fromJson(Map<String, dynamic> json) => _$ReviewFromJson(json);
  Map<String, dynamic> toJson() => _$ReviewToJson(this);
}

@JsonSerializable()
class PaginationLinks {
  String? first;
  String? last;
  String? prev;
  String? next;

  PaginationLinks({this.first, this.last, this.prev, this.next});

  factory PaginationLinks.fromJson(Map<String, dynamic> json) => _$PaginationLinksFromJson(json);
  Map<String, dynamic> toJson() => _$PaginationLinksToJson(this);
}

@JsonSerializable()
class PaginationMeta {
  @JsonKey(name: 'current_page')
  int? currentPage;
  int? from;
  @JsonKey(name: 'last_page')
  int? lastPage;
  List<PaginationLink>? links;
  String? path;
  @JsonKey(name: 'per_page')
  int? perPage;
  int? to;
  int? total;

  PaginationMeta({
    this.currentPage,
    this.from,
    this.lastPage,
    this.links,
    this.path,
    this.perPage,
    this.to,
    this.total,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) => _$PaginationMetaFromJson(json);
  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}

@JsonSerializable()
class PaginationLink {
  String? url;
  String? label;
  bool? active;

  PaginationLink({this.url, this.label, this.active});

  factory PaginationLink.fromJson(Map<String, dynamic> json) => _$PaginationLinkFromJson(json);
  Map<String, dynamic> toJson() => _$PaginationLinkToJson(this);
}
