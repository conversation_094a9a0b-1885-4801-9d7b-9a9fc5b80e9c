import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class ProductDetailsApiServices {
  ProductDetailsApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Show Product Details
  Future<Response?> showProductDetails(int productId) async {
    return _dioFactory.get(
      endPoint: EndPoints.showProductDetails(productId),
    );
  }

  /// Get Similar Products
  Future<Response?> getSimilarProducts(int productId) async {
    return _dioFactory.get(
      endPoint: EndPoints.getSimilarProducts(productId),
    );
  }

  /// Get Product Reviews
  Future<Response?> getProductReviews(int productId) async {
    return _dioFactory.get(
      endPoint: EndPoints.getProductReviews(productId),
    );
  }

  /// Make Rate Product
  Future<Response?> makeRateProduct(
    int productId,
    double rate,
    String message,
  ) async {
    return _dioFactory.post(
      endPoint: EndPoints.makeRateToProduct,
      data: {
        "product_id": productId,
        "rate": rate,
        "message": message,
      },
    );
  }
}
