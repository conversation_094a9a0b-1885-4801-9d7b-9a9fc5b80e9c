import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/models/product_details.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/models/reviews/product_reviews_model.dart';

class ProductDetailsRepository {
  final ProductDetailsApiServices productDetailsApiServices;

  ProductDetailsRepository(this.productDetailsApiServices);

  /// Show Product Details
  Future<ApiResult<ShowProductDetails>> showProductDetails(
      int productId) async {
    final response = await productDetailsApiServices.showProductDetails(
      productId,
    );
    try {
      if (response!.statusCode == 200) {
        ShowProductDetails showProductDetails =
            ShowProductDetails.fromJson(response.data);
        return ApiResult.success(showProductDetails);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Get Similar Products
  Future<ApiResult<ProductCardPaginatedModel>> getSimilarProducts(
    int productId,
  ) async {
    final response = await productDetailsApiServices.getSimilarProducts(
      productId,
    );
    try {
      if (response!.statusCode == 200) {
        ProductCardPaginatedModel similarProducts =
            ProductCardPaginatedModel.fromJson(response.data);
        return ApiResult.success(similarProducts);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Get Product Reviews
  Future<ApiResult<ProductReviewsResponse>> getProductReviews(
      int productId) async {
    final response =
        await productDetailsApiServices.getProductReviews(productId);
    try {
      if (response!.statusCode == 200) {
        ProductReviewsResponse productReviews =
            ProductReviewsResponse.fromJson(response.data);
        return ApiResult.success(productReviews);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Make Rate Product
  Future<ApiResult<String>> makeRateProduct(
    int productId,
    double rate,
    String message,
  ) async {
    final response = await productDetailsApiServices.makeRateProduct(
      productId,
      rate,
      message,
    );
    try {
      if (response!.statusCode == 200) {
        return ApiResult.success('Product Rated Successfully');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
