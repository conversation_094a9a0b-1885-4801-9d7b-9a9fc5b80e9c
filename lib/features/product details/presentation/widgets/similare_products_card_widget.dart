// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
// import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
// import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
// import 'package:tegra_ecommerce_app/core/widgets/show_more/show_more_skeletonizer_widget.dart';
// import 'package:tegra_ecommerce_app/core/widgets/show_more/show_more_widget.dart';
// import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/best_seller_skeletonizer_widget.dart';
// import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_card_widget.dart';
// import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';

// /// Similar Products Card
// class SimilarProductsCardWidget extends StatelessWidget {
//   const SimilarProductsCardWidget({
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final homeCubit = context.read<ProductDetailsCubit>();
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       spacing: 16.h,
//       children: [
//         /// Similar Products Text
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Text(
//               'productDetails.similarproducts'.tr(),
//               style: Styles.heading5.copyWith(
//                 color: Colors.black,
//               ),
//             ),
//             BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
//               buildWhen: (previous, current) =>
//                   current is GetSimilarProductsLoadingState ||
//                   current is GetSimilarProductsSuccessState ||
//                   current is GetSimilarProductsErrorState,
//               builder: (context, state) {
//                 return context
//                             .read<ProductDetailsCubit>()
//                             .similarProductsModel ==
//                         null
//                     ? ShowMoreSkeletonizerWidget()
//                     : ShowMoreWidget(
//                         onTapShowMore: () {
//                           context.pushNamed(
//                             Routes.similarProductsScreen,
//                             arguments: homeCubit.showProductDetailsModel!
//                                 .productDetailsItem!.id,
//                           );
//                         },
//                       );
//               },
//             ),
//           ],
//         ),

//         /// Best Sellers List
//         SizedBox(
//           height: 230.h,
//           child: BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
//             buildWhen: (previous, current) =>
//                 current is GetSimilarProductsLoadingState ||
//                 current is GetSimilarProductsSuccessState ||
//                 current is GetSimilarProductsErrorState,
//             builder: (context, state) {
//               return homeCubit.similarProductsModel == null ||
//                       state is GetSimilarProductsLoadingState
//                   ? BestSellerSkeletonizerWidget()
//                   : homeCubit.similarProductsModel!.data!.product == null ||
//                           homeCubit.similarProductsModel!.data!.product!.isEmpty
//                       ? Container(
//                           alignment: Alignment.center,
//                           child: Text(
//                            'productDetails.NoSimilarProducts'.tr(),
//                           ),
//                         )
//                       : ListView.separated(
//                           shrinkWrap: true,
//                           itemCount: homeCubit.similarProductsModel!.data!
//                                       .product!.length >
//                                   5
//                               ? 5
//                               : homeCubit
//                                   .similarProductsModel!.data!.product!.length,
//                           scrollDirection: Axis.horizontal,
//                           separatorBuilder: (context, index) {
//                             return SizedBox(width: 16.w);
//                           },
//                           itemBuilder: (context, index) {
//                             return InkWell(
//                               onTap: () {
//                                 context.pushNamed(
//                                   Routes.productsDetailsScreen,
//                                   arguments: homeCubit.similarProductsModel!
//                                       .data!.product![index].id,
//                                 );

//                               },
//                               child: ProductCardWidget(
//                                 productItem: homeCubit.similarProductsModel!
//                                     .data!.product![index],
//                                 // onTapFavorite: () {},
//                               ),
//                             );
//                           },
//                         );
//             },
//           ),
//         ),
//       ],
//     );
//   }
// }

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/show_more/show_more_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/show_more/show_more_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/best_seller/best_seller_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_card_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_details_screen.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/similar_products_screen.dart';

/// Similar Products Card
class SimilarProductsCardWidget extends StatelessWidget {
  const SimilarProductsCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final homeCubit = context.read<ProductDetailsCubit>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 16.h,
      children: [
        /// Similar Products Text
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'productDetails.similarproducts'.tr(),
              style: Styles.heading5.copyWith(
                color: Colors.black,
              ),
            ),
            BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
              buildWhen: (previous, current) =>
                  current is GetSimilarProductsLoadingState ||
                  current is GetSimilarProductsSuccessState ||
                  current is GetSimilarProductsErrorState,
              builder: (context, state) {
                return homeCubit.similarProductsModel == null
                    ? ShowMoreSkeletonizerWidget()
                    : ShowMoreWidget(
                        onTapShowMore: () {
                          PersistentNavBarNavigator.pushNewScreen(
                            context,
                            screen: BlocProvider(
                              create: (context) => ProductDetailsCubit(getIt())
                                ..loadSimilarProducts(homeCubit
                                    .showProductDetailsModel!
                                    .productDetailsItem!
                                    .id!),
                              child: SimilarProductsScreen(
                                  productId: homeCubit.showProductDetailsModel!
                                      .productDetailsItem!.id!),
                            ),
                            withNavBar: true,
                            pageTransitionAnimation:
                                PageTransitionAnimation.cupertino,
                          );
                          // context.pushNamed(
                          //   Routes.similarProductsScreen,
                          //   arguments: homeCubit.showProductDetailsModel!
                          //       .productDetailsItem!.id,
                          // );
                        },
                      );
              },
            ),
          ],
        ),

        /// Best Sellers List
        SizedBox(
          height: 230.h,
          child: BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
            buildWhen: (previous, current) =>
                current is GetSimilarProductsLoadingState ||
                current is GetSimilarProductsSuccessState ||
                current is GetSimilarProductsErrorState,
            builder: (context, state) {
              // Check if similar products are null or loading
              if (homeCubit.similarProductsModel == null ||
                  state is GetSimilarProductsLoadingState) {
                return BestSellerSkeletonizerWidget();
              }

              // Check if the product list is null or empty
              if (homeCubit.similarProductsModel!.data!.product == null ||
                  homeCubit.similarProductsModel!.data!.product!.isEmpty) {
                return SizedBox.shrink();
              }

              // Render the list of similar products
              return ListView.separated(
                shrinkWrap: true,
                itemCount:
                    homeCubit.similarProductsModel!.data!.product!.length > 5
                        ? 5
                        : homeCubit.similarProductsModel!.data!.product!.length,
                scrollDirection: Axis.horizontal,
                separatorBuilder: (context, index) {
                  return SizedBox(width: 16.w);
                },
                itemBuilder: (context, index) {
                  return InkWell(
                    onTap: () {
                      PersistentNavBarNavigator.pushNewScreen(
                        context,
                        screen: BlocProvider(
                            create: (context) => ProductDetailsCubit(getIt())
                              ..showProductDetails(homeCubit
                                  .similarProductsModel!
                                  .data!
                                  .product![index]
                                  .id!)
                              ..loadSimilarProducts(homeCubit
                                  .similarProductsModel!
                                  .data!
                                  .product![index]
                                  .id!)
                              ..getProductReviews(homeCubit
                                  .similarProductsModel!
                                  .data!
                                  .product![index]
                                  .id!),
                            child: ProductDetailsScreen()),
                        withNavBar: true,
                        pageTransitionAnimation:
                            PageTransitionAnimation.cupertino,
                      );
                      // context.pushNamed(
                      //   Routes.productsDetailsScreen,
                      //   arguments: ProductDetailsArguments(
                      //     productId: homeCubit
                      //         .similarProductsModel!.data!.product![index].id!,
                      //   ),
                      // );
                    },
                    child: ProductCardWidget(
                      productItem:
                          homeCubit.similarProductsModel!.data!.product![index],
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
