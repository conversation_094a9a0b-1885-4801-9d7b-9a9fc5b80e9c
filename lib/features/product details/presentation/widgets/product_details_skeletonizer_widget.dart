import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/rates_details_card_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/similare_products_card_widget.dart';

class ProductDetailsSkeletonizerWidget extends StatelessWidget {
  const ProductDetailsSkeletonizerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          30.verticalSpace,
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Column(
                  spacing: 20.h,
                  children: [
                    /// Product Image Container
                    Stack(
                      children: [
                        CarouselSlider.builder(
                          itemCount: 5,
                          options: CarouselOptions(
                            enlargeCenterPage: true,
                            viewportFraction: 0.9,
                            enableInfiniteScroll: true,
                            height: 215.h,
                            autoPlayAnimationDuration:
                                const Duration(milliseconds: 800),
                            autoPlayInterval: const Duration(seconds: 3),
                            onPageChanged: (index, reason) {},
                          ),
                          itemBuilder: (context, index, realIndex) {
                            return Stack(
                              children: [
                                Container(
                                  width: double.infinity,
                                  height: 215.h,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                      AppConstants.borderRadius + 4,
                                    ),
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        Colors.black
                                            .withAlpha((0.0 * 255).toInt()),
                                        Colors.black
                                            .withAlpha((0.16 * 255).toInt()),
                                      ],
                                      stops: [0.0, 1.0],
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(
                                      AppConstants.borderRadius + 4,
                                    ),
                                    child: Image.asset(
                                      'assets/images/pngs/product_details.png',
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),

                                /// Gradient
                                Positioned.fill(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(
                                        AppConstants.borderRadius + 4,
                                      ),
                                      gradient: LinearGradient(
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                        colors: [
                                          Colors.black
                                              .withAlpha((0.0 * 255).toInt()),
                                          Colors.black
                                              .withAlpha((0.16 * 255).toInt()),
                                        ],
                                        stops: [0.0, 1.0],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          },
                        ),

                        /// Dotes
                        BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
                          buildWhen: (previous, current) =>
                              current is ChangeSliderState,
                          builder: (context, state) {
                            return Positioned(
                              left: 0,
                              right: 0,
                              bottom: 10.h,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: List.generate(
                                  5,
                                  (index) {
                                    return Container(
                                      margin:
                                          EdgeInsets.symmetric(horizontal: 4.w),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 12.w,
                                        vertical: 4.h,
                                      ),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          AppConstants.borderRadius + 10.r,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),

                    /// Product Details Card
                    // ProductDetailsCardWidget(),

                    /// Rates Card
                    RatesDetailsCardWidget(),

                    /// Similar Products Card
                    SimilarProductsCardWidget(),
                    // BlocProvider(
                    //   create: (context) => HomeCubit(getIt()),
                    //   child: SimilarProductsCardWidget(),
                    // ),

                    SizedBox(height: 30.h)
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
