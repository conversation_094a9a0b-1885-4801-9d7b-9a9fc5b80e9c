import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/show_more/show_more_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_reviews_screen.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/rate_widget.dart';

class RatesDetailsCardWidget extends StatelessWidget {
  const RatesDetailsCardWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
      buildWhen: (previous, current) =>
          current is GetProductReviewsLoadingState ||
          current is GetProductReviewsSuccessState ||
          current is GetProductReviewsErrorState ||
          current is GetProductReviewsLoadingMoreState,
      builder: (context, state) {
        final productDetailsCubit = context.read<ProductDetailsCubit>();
        final reviews = productDetailsCubit
            .reviewsResponseModel?.productReviewsData?.reviews;

        return Column(
          spacing: 8.h,
          children: [
            if (reviews != null && reviews.isNotEmpty) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        'productDetails.ratings'.tr(),
                        style: Styles.heading5.copyWith(
                          color: AppColors.neutralColor1200,
                        ),
                      ),
                      Text(
                        ' ( ',
                      ),
                      Text(
                        'productDetails.Evaluation'.tr(),
                        style: Styles.captionRegular.copyWith(
                          color: AppColors.neutralColor1200,
                        ),
                      ),
                      Text(
                        ' ${reviews.length}',
                        style: Styles.captionRegular.copyWith(
                          color: AppColors.neutralColor1200,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      SvgPicture.asset(
                        Assets.assetsImagesSvgsStarIcon,
                        fit: BoxFit.scaleDown,
                      ),
                      Text(
                        ' ) ',
                      ),
                    ],
                  ),
                  ShowMoreWidget(
                    onTapShowMore: () =>
                        PersistentNavBarNavigator.pushNewScreen(
                      context,
                      screen: BlocProvider(
                        create: (context) => ProductDetailsCubit(getIt())
                          ..getProductReviews(productDetailsCubit
                              .showProductDetailsModel!
                              .productDetailsItem!
                              .id!),
                        child: ProductReviewsScreen(
                          productId: productDetailsCubit
                              .showProductDetailsModel!.productDetailsItem!.id!,
                        ),
                      ),
                      withNavBar: true,
                      pageTransitionAnimation:
                          PageTransitionAnimation.cupertino,
                    ),
                    //  context.pushNamed(
                    //   Routes.productReviewsScreen,
                    //   arguments: productDetailsCubit
                    //       .showProductDetailsModel!.productDetailsItem!.id!,
                    // ),
                  ),
                ],
              ),
            ],
            productDetailsCubit.reviewsResponseModel?.productReviewsData?.meta
                            ?.total ==
                        0 ||
                    reviews == null ||
                    reviews.isEmpty
                ? SizedBox.shrink()
                : ListView.separated(
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    physics: NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      return RateWidget(
                        reviews: reviews[index],
                      );
                    },
                    separatorBuilder: (context, index) => 12.verticalSpace,
                    itemCount: 2 < reviews.length ? 2 : reviews.length,
                  ),
          ],
        );
      },
    );
  }
}
