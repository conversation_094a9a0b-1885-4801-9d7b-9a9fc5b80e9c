import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';

class BottomNavBarInProductDetailsSkeletonizerWidget extends StatelessWidget {
  const BottomNavBarInProductDetailsSkeletonizerWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Container(
        width: double.infinity,
        height: 60.h,
        padding: EdgeInsets.symmetric(horizontal: 18.w),
        child: Row(
          spacing: 12.w,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// Add to cart
            Expanded(
              child: CustomButtonWidget(
                borderRadius: AppConstants.borderRadius - 2,
                color: AppColors.neutralColor300,
                // Button color based on selection
                text: '',
                onPressed: null,
              ),
            ),

            /// Favorite Icon
            Container(
              padding: EdgeInsets.all(13.sp),
              decoration: BoxDecoration(
                color: AppColors.neutralColor300,
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(
                  width: 1.w,
                  color: AppColors.neutralColor200,
                ),
              ),
              child: SvgPicture.asset(
                'assets/images/svgs/favorite_icon_in_details.svg',
                fit: BoxFit.scaleDown,
                colorFilter: ColorFilter.mode(
                  Colors.transparent,
                  BlendMode.src,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
