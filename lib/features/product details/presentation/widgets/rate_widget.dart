import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/models/reviews/product_reviews_model.dart';

class RateWidget extends StatelessWidget {
  const RateWidget({
    super.key,
    this.reviews,
  });

  final Review? reviews;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        spacing: 4.h,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Person Image - Person Name - Date
          Row(
            spacing: 4.w,
            children: [
              reviews!.userImage != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(45.r),
                      child: CachedNetworkImage(
                        width: 45.w,
                        height: 45.h,
                        fit: BoxFit.cover,
                        imageUrl: reviews!.userImage!,
                        errorWidget: (context, url, error) => Image.asset(
                          Assets.assetsImagesPngsProfileImage,
                          width: 45.w,
                          height: 45.h,
                        ),
                      ),
                    )
                  : Image.asset(
                      Assets.assetsImagesPngsProfileImage,
                      width: 45.w,
                      height: 45.h,
                    ),
              Column(
                spacing: 2.h,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    reviews!.userName!,
                    // 'عمر عبدالعزيز',
                    style: Styles.highlightBold.copyWith(
                      color: AppColors.neutralColor1200,
                    ),
                  ),
                  Text(
                    reviews!.date!,
                    // '2/10/2024',
                    style: Styles.captionRegular.copyWith(
                      color: AppColors.neutralColor600,
                    ),
                  ),
                ],
              )
            ],
          ),

          Text(
            'productDetails.PumaTshirtscombine'.tr(),
            style: Styles.captionRegular.copyWith(
              color: AppColors.neutralColor600,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
