import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';

class ProductVariantSelectorWidget extends StatelessWidget {
  const ProductVariantSelectorWidget({super.key});

  Color hexToColor(String hex) {
    hex = hex.replaceAll("#", "");
    if (hex.length == 6) {
      hex = "FF$hex";
    }
    return Color(int.parse(hex, radix: 16));
  }

// Color hexToColor(String hex) {
//   hex = hex.replaceAll("#", "");
//   if (hex.length == 6) {
//     hex = "FF$hex"; // إضافة الشفافية (alpha = 100%) إذا لم تكن موجودة
//   }
//   return Color(int.parse(hex, radix: 16));
// }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
      builder: (context, state) {
        final productDetailsCubit = context.read<ProductDetailsCubit>();

        if (productDetailsCubit.uniqueVariantMap.isEmpty) {
          return Container();
        }

        return Column(
          children: productDetailsCubit.uniqueVariantMap.entries
              .toList()
              .asMap()
              .entries
              .map((attrEntry) {
            int attrIndex = attrEntry.key;
            var attribute = attrEntry.value.value;
            String attributeKey = attrEntry.value.key;
            int currentSelectedIndex =
                productDetailsCubit.selectedVariantIndex[attributeKey] ?? -1;

            return Padding(
              padding: EdgeInsets.only(bottom: 4.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    attributeKey,
                    style: Styles.heading5.copyWith(
                      color: AppColors.neutralColor1200,
                    ),
                  ),
                  SizedBox(height: 4),
                  Row(
                    children: List.generate(
                      attribute.length * 2 - 1,
                      (i) {
                        if (i.isEven) {
                          int optionIndex = i ~/ 2;
                          String value = attribute[optionIndex];

                          bool isOptionAvailable = productDetailsCubit
                              .showProductDetailsModel!
                              .productDetailsItem!
                              .variants!
                              .sku!
                              .any((sku) {
                            Map<String, String> tempSelection = {};
                            productDetailsCubit.selectedVariantIndex
                                .forEach((key, index) {
                              if (key != attributeKey) {
                                tempSelection[key] = productDetailsCubit
                                    .uniqueVariantMap[key]![index];
                              }
                            });
                            tempSelection[attributeKey] = value;

                            bool matches = true;
                            tempSelection.forEach((attr, selectedValue) {
                              bool hasMatch = productDetailsCubit
                                  .showProductDetailsModel!
                                  .productDetailsItem!
                                  .variants!
                                  .variantMap[attr]!
                                  .any((variantItem) =>
                                      variantItem.skuId == sku.skuId &&
                                      variantItem.value == selectedValue);
                              if (!hasMatch) {
                                matches = false;
                              }
                            });
                            return matches;
                          });

                          Widget optionWidget;

                          /// Color
                          if (attributeKey.toLowerCase() == 'color') {
                            Color bgColor = hexToColor(value);
                            optionWidget = Container(
                              width: 30,
                              height: 30,
                              decoration: BoxDecoration(
                                color: bgColor,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: currentSelectedIndex == optionIndex
                                      ? Colors.blue
                                      : Colors.black12,
                                  width: currentSelectedIndex == optionIndex
                                      ? 3
                                      : 1,
                                ),
                              ),
                            );
                          }

                          /// Size
                          else if (attributeKey.toLowerCase() == 'size') {
                            String displayText;
                            if (value == 'صغير') {
                              displayText = 'M';
                            } else if (value == 'كبير') {
                              displayText = 'XL';
                            } else if (value == 'متوسط') {
                              displayText = 'L';
                            } else {
                              displayText = value;
                            }
                            optionWidget = Container(
                              width: 30,
                              height: 30,
                              decoration: BoxDecoration(
                                color: currentSelectedIndex == optionIndex
                                    ? Colors.blueAccent
                                    : AppColors.scaffoldBackground,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: currentSelectedIndex == optionIndex
                                      ? Colors.blue
                                      : Colors.black12,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  textAlign: TextAlign.center,
                                  displayText,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: currentSelectedIndex == optionIndex
                                        ? Colors.white
                                        : Colors.black,
                                  ),
                                ),
                              ),
                            );
                          }

                          /// Any Data
                          else {
                            optionWidget = Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.w,
                                vertical: 8.h,
                              ),
                              decoration: BoxDecoration(
                                color: currentSelectedIndex == optionIndex
                                    ? Colors.blueAccent
                                    : Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(8.r),
                                border: Border.all(
                                  color: currentSelectedIndex == optionIndex
                                      ? Colors.blue
                                      : Colors.grey,
                                ),
                              ),
                              child: Text(
                                value,
                                style: TextStyle(
                                  color: currentSelectedIndex == optionIndex
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ),
                            );
                          }

                          return GestureDetector(
                            onTap: () {
                              productDetailsCubit.onOptionTap(
                                  attrIndex, optionIndex);
                            },
                            child: Padding(
                              padding: EdgeInsets.only(right: 20.w),
                              child: Opacity(
                                opacity: isOptionAvailable ? 1.0 : 0.5,
                                child: optionWidget,
                              ),
                            ),
                          );
                        } else {
                          return SizedBox(width: 20.w);
                        }
                      },
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        );
      },
    );
  }
}
