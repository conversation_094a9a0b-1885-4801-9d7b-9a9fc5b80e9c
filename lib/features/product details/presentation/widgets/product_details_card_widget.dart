import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/product_variant_selector_widget.dart';

class ProductDetailsCardWidget extends StatelessWidget {
  const ProductDetailsCardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
      builder: (context, state) {
        final productDetailsCubit = context.read<ProductDetailsCubit>();
        final showProductDetailsModel =
            productDetailsCubit.showProductDetailsModel;

        if (showProductDetailsModel == null) {
          return Skeletonizer(
            enabled: true,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'ajsdfuafo',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      Text(
                        'SAR 341584',
                        style: Styles.heading3.copyWith(
                          color: AppColors.neutralColor1200,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.h),
                  ProductVariantSelectorWidget(),
                ],
              ),
            ),
          );
        }

        num? updatedPrice = showProductDetailsModel.productDetailsItem?.priceAfterDiscount;
        Map<String, int> selectedVariantIndex = {};

        if (state is ProductVariantSelectedState) {
          updatedPrice = state.updatedPrice;
          selectedVariantIndex = state.selectedVariantIndex;
        }

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    showProductDetailsModel.productDetailsItem!.name!,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  Text(
                    'SAR $updatedPrice',
                    style: Styles.heading3.copyWith(
                      color: AppColors.neutralColor1200,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.h),
              ProductVariantSelectorWidget(),
            ],
          ),
        );
      },
    );
  }
}