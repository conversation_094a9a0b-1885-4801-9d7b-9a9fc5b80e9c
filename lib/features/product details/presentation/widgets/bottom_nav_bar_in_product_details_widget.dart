import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/helper_functions/flutter_toast.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_cubit.dart';
import 'package:tegra_ecommerce_app/features/favorite/bloc/cubit/favorite_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';

class BottomNavBarInProductDetailsWidget extends StatelessWidget {
  const BottomNavBarInProductDetailsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 60.h,
      padding: EdgeInsets.symmetric(horizontal: 18.w),
      child: Row(
        spacing: 12.w,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Add to cart
          Expanded(
            child: CustomButtonWidget(
              borderRadius: AppConstants.borderRadius - 2,
              color: AppColors.primaryColor900,
              text: 'productDetails.AddToCart'.tr(),
              onPressed: () {
                if (context
                    .read<ProductDetailsCubit>()
                    .showProductDetailsModel!
                    .productDetailsItem!
                    .availableVariantsAttributes!
                    .isNotEmpty) {
                  if (context.read<ProductDetailsCubit>().selectedSkuId !=
                      null) {
                    CartCubit(getIt()).addToCart(
                        productId: context
                            .read<ProductDetailsCubit>()
                            .showProductDetailsModel!
                            .productDetailsItem!
                            .id!,
                        skuId:
                            context.read<ProductDetailsCubit>().selectedSkuId!);
                  } else {
                    customToast(
                        msg: 'productDetails.selectVaration'.tr(),
                        color: AppColors.redColor200);
                  }
                } else {
                  CartCubit(getIt()).addToCart(
                      productId: context
                          .read<ProductDetailsCubit>()
                          .showProductDetailsModel!
                          .productDetailsItem!
                          .id!,
                      skuId: context
                          .read<ProductDetailsCubit>()
                          .showProductDetailsModel!
                          .productDetailsItem!
                          .variants!
                          .sku![0]
                          .skuId!);
                }
              },
            ),
          ),

          /// Favorite Icon
          BlocProvider(
            create: (context) => FavoriteCubit(getIt()),
            child: BlocBuilder<FavoriteCubit, FavoriteState>(
              builder: (context, state) {
                return InkWell(
                  onTap: () {
                    BlocProvider.of<FavoriteCubit>(context).addToFavorites(
                        productId: context
                            .read<ProductDetailsCubit>()
                            .showProductDetailsModel!
                            .productDetailsItem!
                            .id!);
                    context
                            .read<ProductDetailsCubit>()
                            .showProductDetailsModel!
                            .productDetailsItem!
                            .isFavourite =
                        !context
                            .read<ProductDetailsCubit>()
                            .showProductDetailsModel!
                            .productDetailsItem!
                            .isFavourite!;
                  },
                  child: Container(
                    padding: EdgeInsets.all(13.sp),
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(AppConstants.borderRadius),
                      border: Border.all(
                        width: 1.w,
                        color: AppColors.neutralColor600,
                      ),
                    ),
                    child: SvgPicture.asset(
                      context
                              .read<ProductDetailsCubit>()
                              .showProductDetailsModel!
                              .productDetailsItem!
                              .isFavourite!
                          ? Assets.assetsImagesSvgsIsFavoriteIcon
                          : Assets.assetsImagesSvgsFavoriteIconInDetails,
                      fit: BoxFit.fill,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
