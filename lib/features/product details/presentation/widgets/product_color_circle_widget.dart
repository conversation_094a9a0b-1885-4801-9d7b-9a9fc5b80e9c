import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/hex_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class ProductColorCircleWidget extends StatelessWidget {
  const ProductColorCircleWidget({
    super.key,
    required this.variantValue,
    required this.isSelected,
  });

  final String variantValue;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 4.w,
      children: [
        Container(
          height: 23.h,
          width: 23.w,
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.neutralColor400,
              width: 1.w,
            ),
            shape: BoxShape.circle,
            color: hexToColor(variantValue),
          ),
          alignment: Alignment.center,
        ),
        if (isSelected)
          Container(
            width: 16.w,
            height: 2.h,
            decoration: BoxDecoration(
              color: AppColors.primaryColor900,
              borderRadius: BorderRadius.circular(
                AppConstants.borderRadius,
              ),
            ),
          ),
      ],
    );
  }
}
