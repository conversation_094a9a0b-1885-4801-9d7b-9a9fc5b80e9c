import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';

class ProductDetailsWidget extends StatefulWidget {
  const ProductDetailsWidget({super.key});

  @override
  State<ProductDetailsWidget> createState() => _ProductDetailsWidgetState();
}

class _ProductDetailsWidgetState extends State<ProductDetailsWidget> {
  bool isExpanded = false;
  final String fullText =
      'productDetails.Pumatshirtsareshort'.tr();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: isExpanded
                    ? fullText
                    : '${fullText.substring(0, fullText.length > 100 ? 100 : fullText.length)}...',
                style: Styles.contentEmphasis.copyWith(
                  color: AppColors.neutralColor600,
                ),
              ),
              if (fullText.length > 100)
                WidgetSpan(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        isExpanded = !isExpanded;
                      });
                    },
                    child: Text(
                      isExpanded ? 'productDetails.Showless'.tr() : 'productDetails.Showmore'.tr() ,
                      style: Styles.contentEmphasis.copyWith(
                        color: AppColors.primaryColor900,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
