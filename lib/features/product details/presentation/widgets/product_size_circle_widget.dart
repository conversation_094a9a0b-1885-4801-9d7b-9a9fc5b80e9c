import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/models/variant_selection_data_mode.dart';

class ProductSizeCircleWidget extends StatelessWidget {
  const ProductSizeCircleWidget({
    super.key,
    required this.variantSelectionDataModel,
  });

  final VariantSelectionDataModel variantSelectionDataModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8.sp),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: variantSelectionDataModel
            .selectedVariantIndex[variantSelectionDataModel.attribute] ==
            variantSelectionDataModel.index
            ? AppColors.primaryColor900
            : AppColors.scaffoldBackground,
        border: variantSelectionDataModel
            .selectedVariantIndex[variantSelectionDataModel.attribute] ==
            variantSelectionDataModel.index
            ? null
            : Border.all(
          color: Colors.black,
        ),
      ),
      alignment: Alignment.center,
      child: Text(
        variantSelectionDataModel.variantValue == 'صغير'
            ? 'S'
            : variantSelectionDataModel.variantValue == 'كبير'
            ? 'L'
            : variantSelectionDataModel.variantValue == 'متوسط'
            ? 'M'
            : variantSelectionDataModel.variantValue,
        style: Styles.captionRegular.copyWith(
          color: variantSelectionDataModel
              .selectedVariantIndex[variantSelectionDataModel.attribute] ==
              variantSelectionDataModel.index
              ? AppColors.scaffoldBackground
              : Colors.black,
          height: -.1.h,
        ),
      ),
    );
  }
}
