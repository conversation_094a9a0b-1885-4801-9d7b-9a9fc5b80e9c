import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_card_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_grid/product_grid_view_item_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_details_screen.dart';

class SimilarProductsScreen extends StatelessWidget {
  final int productId;

  const SimilarProductsScreen({super.key, required this.productId});

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    final productDetailsCubit = context.watch<ProductDetailsCubit>();

    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        productDetailsCubit.loadMoreSimilarProducts(productId);
      }
    });

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 16.w,
              children: [
                BackButtonWidget(onTap: () => context.pop()),
                Text(
                  'productDetails.similarproducts'.tr(),
                  style: Styles.heading2.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
              builder: (context, state) {
                final products =
                    productDetailsCubit.similarProductsModel?.data?.product ??
                        [];

                if (state is GetSimilarProductsLoadingState) {
                  return Skeletonizer(
                    enabled: true,
                    child: SizedBox(
                      height: MediaQuery.sizeOf(context).height / 1.3,
                      child: GridView.builder(
                        shrinkWrap: true,
                        physics: const BouncingScrollPhysics(),
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 30.h),
                        controller: scrollController,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          mainAxisExtent: 220.sp,
                          crossAxisSpacing: 10.sp,
                          mainAxisSpacing: 40.sp,
                        ),
                        itemCount: 6,
                        itemBuilder: (BuildContext context, int index) {
                          return ProductGridViewItemSkeletonizerWidget();
                        },
                      ),
                    ),
                  );
                }

                if (products.isEmpty) {
                  return const Center(
                      child: Text('No similar products found.'));
                }

                return Expanded(
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const BouncingScrollPhysics(),
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 30.h),
                    controller: scrollController,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisExtent: 220.sp,
                      crossAxisSpacing: 10.sp,
                      mainAxisSpacing: 40.sp,
                    ),
                    itemCount: products.length,
                    itemBuilder: (BuildContext context, int index) {
                      final product = products[index];

                      return InkWell(
                        onTap: () {
                          PersistentNavBarNavigator.pushNewScreen(
                            context,
                            screen: BlocProvider(
                              create: (context) => ProductDetailsCubit(getIt())
                                ..showProductDetails(product.id!)
                                ..loadSimilarProducts(product.id!)
                                ..getProductReviews(product.id!),
                              child: ProductDetailsScreen(),
                            ),
                            withNavBar: true,
                            pageTransitionAnimation:
                                PageTransitionAnimation.cupertino,
                          );
                          // context.pushNamed(
                          //   Routes.productsDetailsScreen,
                          //   arguments:
                          //       ProductDetailsArguments(productId: product.id!),
                          // );
                          // context.pushNamed(
                          //   Routes.productsDetailsScreen,
                          //   arguments: product.id,
                          // );
                        },
                        child: ProductCardWidget(
                          productItem: product,
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
