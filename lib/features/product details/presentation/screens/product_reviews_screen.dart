import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/models/reviews/product_reviews_model.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/rate_widget.dart';

class ProductReviewsScreen extends StatelessWidget {
  const ProductReviewsScreen({super.key, required this.productId});

  final int productId;

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    final productDetailsCubit = context.read<ProductDetailsCubit>();

    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        productDetailsCubit.loadMoreProductReviews(productId);
      }
    });

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 16.w,
              children: [
                BackButtonWidget(onTap: () => context.pop()),
                Text(
                  'productDetails.ratings'.tr(),
                  style: Styles.heading2.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
              ],
            ),
          ),
          BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
              buildWhen: (previous, current) =>
                  current is GetProductReviewsLoadingState ||
                  current is GetProductReviewsLoadingMoreState ||
                  current is GetProductReviewsSuccessState ||
                  current is GetProductReviewsErrorState,
              builder: (context, state) {
                final reviews = productDetailsCubit
                    .reviewsResponseModel?.productReviewsData?.reviews;

                if (state is GetProductReviewsLoadingState) {
                  return Expanded(
                    child: Center(
                      child: Skeletonizer(
                        enabled: true,
                        child: ListView.builder(
                          shrinkWrap: true,
                          controller: scrollController,
                          itemCount: 10,
                          padding: EdgeInsets.symmetric(vertical: 30.h),
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 8.h,
                              ),
                              child: Column(
                                children: [
                                  RateWidget(
                                    reviews: Review(
                                      text: 'bdsagfiubdsf',
                                      date: 'jgasdfuo',
                                      userName: 'khbadfg',
                                      userImage: null,
                                    ),
                                  )
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                }

                if (state is GetProductReviewsErrorState) {
                  return Expanded(
                    child: Center(
                      child: Text(
                        'productDetails.Somethingwentwrong'.tr(),
                        style: Styles.heading3.copyWith(
                          color: AppColors.neutralColor1200,
                        ),
                      ),
                    ),
                  );
                }

                return Expanded(
                  child: ListView.builder(
                    shrinkWrap: true,
                    controller: scrollController,
                    itemCount: reviews?.length,
                    padding: EdgeInsets.symmetric(vertical: 30.h),
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 8.h,
                        ),
                        child: Column(
                          children: [
                            RateWidget(
                              reviews: reviews![index],
                            )
                          ],
                        ),
                      );
                    },
                  ),
                );
              }),
        ],
      ),
    );
  }
}
