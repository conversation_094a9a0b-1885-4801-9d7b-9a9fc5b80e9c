import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/models/orders_datails/orders_details_data_model.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/bottom_nav_bar_in_product_details_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/bottom_nav_bar_in_product_details_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/product_details_card_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/product_details_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/rates_details_card_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/widgets/similare_products_card_widget.dart';

class ProductDetailsScreen extends StatelessWidget {
  const ProductDetailsScreen({super.key, this.skuAttributes});
// <<<<<<< payment
// =======

// >>>>>>> development
  final List<SkuAttribute>? skuAttributes;

  @override
  Widget build(BuildContext context) {
    final productDetailsCubit = context.read<ProductDetailsCubit>();

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
        buildWhen: (previous, current) =>
        current is ProductDetailsLoadingState ||
            current is ProductDetailsSuccessState ||
            current is ProductDetailsErrorState,
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppBarWidget(
                rowWidget: Row(
                  spacing: 16.w,
                  children: [
                    BackButtonWidget(onTap: () => context.pop()),
                    Text(
                      productDetailsCubit
                          .showProductDetailsModel?.productDetailsItem?.name ??
                          'productDetails.loading'.tr(),
                      style: Styles.heading2.copyWith(
                        color: AppColors.scaffoldBackground,
                      ),
                    ),
                  ],
                ),
              ),

              state is ProductDetailsLoadingState
                  ? Expanded(
                  child: SingleChildScrollView(
                      child: ProductDetailsSkeletonizerWidget()))
                  : Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: 16.w, vertical: 30.h),
                    child: Column(
                      children: [
                        Stack(
                          children: [
                            CarouselSlider.builder(
                              itemCount: 2,
                              options: CarouselOptions(
                                enlargeCenterPage: true,
                                viewportFraction: 0.9,
                                enableInfiniteScroll: true,
                                height: 215.h,
                                autoPlayAnimationDuration:
                                const Duration(milliseconds: 800),
                                autoPlayInterval:
                                const Duration(seconds: 3),
                                onPageChanged: (index, reason) {
                                  productDetailsCubit
                                      .changeSliderIndex(index);
                                },
                              ),
                              itemBuilder: (context, index, realIndex) {
                                return Stack(
                                  children: [
                                    Container(
                                      width: double.infinity,
                                      height: 215.h,
                                      decoration: BoxDecoration(
                                        borderRadius:
                                        BorderRadius.circular(
                                          AppConstants.borderRadius + 4,
                                        ),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          colors: [
                                            Colors.black.withAlpha(
                                                (0.0 * 255).toInt()),
                                            Colors.black.withAlpha(
                                                (0.16 * 255).toInt()),
                                          ],
                                          stops: [0.0, 1.0],
                                        ),
                                      ),
                                      child: ClipRRect(
                                        borderRadius:
                                        BorderRadius.circular(
                                          AppConstants.borderRadius + 4,
                                        ),
                                        child: CachedNetworkImage(
                                          imageUrl: productDetailsCubit
                                              .showProductDetailsModel
                                              ?.productDetailsItem
                                              ?.thumbnail ??
                                              '',
                                          fit: BoxFit.fill,
                                          errorWidget:
                                              (context, url, error) =>
                                              Skeletonizer(
                                                enabled: true,
                                                child: Center(
                                                  child: Image.asset(
                                                    Assets
                                                        .assetsImagesPngsProduct4Image,
                                                    fit: BoxFit.fill,
                                                    height: double.infinity,
                                                    width: double.infinity,
                                                  ),
                                                ),
                                              ),
                                        ),
                                      ),
                                    ),

                                    /// Gradient
                                    Positioned.fill(
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                          BorderRadius.circular(
                                            AppConstants.borderRadius + 4,
                                          ),
                                          gradient: LinearGradient(
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                            colors: [
                                              Colors.black.withAlpha(
                                                  (0.0 * 255).toInt()),
                                              Colors.black.withAlpha(
                                                  (0.16 * 255).toInt()),
                                            ],
                                            stops: [0.0, 1.0],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),

                            /// Dotes
                            BlocBuilder<ProductDetailsCubit,
                                ProductDetailsState>(
                              buildWhen: (previous, current) =>
                              current is ChangeSliderState,
                              builder: (context, state) {
                                return Positioned(
                                  left: 0,
                                  right: 0,
                                  bottom: 10.h,
                                  child: Row(
                                    mainAxisAlignment:
                                    MainAxisAlignment.center,
                                    children: List.generate(
                                      2,
                                          (index) {
                                        return Container(
                                          margin: EdgeInsets.symmetric(
                                            horizontal: 4.w,
                                          ),
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 12.w,
                                            vertical: 4.h,
                                          ),
                                          decoration: BoxDecoration(
                                            color: productDetailsCubit
                                                .currentIndex ==
                                                index
                                                ? AppColors
                                                .primaryColor900
                                                : AppColors
                                                .primaryColor900
                                                .withValues(
                                              alpha: .3,
                                            ),
                                            borderRadius:
                                            BorderRadius.circular(
                                              AppConstants.borderRadius +
                                                  10.r,
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),

                        20.verticalSpace,

                        /// Product Details Card
                        ProductDetailsCardWidget(),
                        8.verticalSpace,

                        /// Rates Card
                        RatesDetailsCardWidget(),
                        8.verticalSpace,

                        /// Similar Products Card
                        SimilarProductsCardWidget(),
                        20.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
      bottomNavigationBar:
      BlocBuilder<ProductDetailsCubit, ProductDetailsState>(
        builder: (context, state) {
          final productDetailsCubit = context.read<ProductDetailsCubit>();

          return state is ProductDetailsLoadingState ||
              productDetailsCubit.showProductDetailsModel == null
              ? BottomNavBarInProductDetailsSkeletonizerWidget()
              : BottomNavBarInProductDetailsWidget();
        },
      ),
    );
  }
}
