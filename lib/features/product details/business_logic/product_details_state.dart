part of 'product_details_cubit.dart';

abstract class ProductDetailsState {}

final class ProductDetailsInitial extends ProductDetailsState {}

final class ChangeSliderState extends ProductDetailsState {}

/// Product Details States
final class ProductDetailsLoadingState extends ProductDetailsState {}

final class ProductDetailsSuccessState extends ProductDetailsState {}

final class ProductDetailsErrorState extends ProductDetailsState {}

/// Product Variant States
final class ProductVariantSelectedState extends ProductDetailsState {
  final num updatedPrice;
  final Map<String, int> selectedVariantIndex;
  final int? selectedSkuId;

  ProductVariantSelectedState({
    required this.updatedPrice,
    required this.selectedVariantIndex,
    this.selectedSkuId,
  });
}

/// Get Similar Products States
final class GetSimilarProductsLoadingState extends ProductDetailsState {}

final class GetSimilarProductsSuccessState extends ProductDetailsState {}

final class GetSimilarProductsErrorState extends ProductDetailsState {}

final class GetSimilarProductsLoadingMoreState extends ProductDetailsState {}

/// Get Product Reviews
final class GetProductReviewsLoadingState extends ProductDetailsState {}

final class GetProductReviewsSuccessState extends ProductDetailsState {}

final class GetProductReviewsErrorState extends ProductDetailsState {}

final class GetProductReviewsLoadingMoreState extends ProductDetailsState {}



final class SelectOption extends ProductDetailsState {}


/// Make Rate Product States
final class MakeRateProductLoadingState extends ProductDetailsState {}

final class MakeRateProductSuccessState extends ProductDetailsState {}

final class MakeRateProductErrorState extends ProductDetailsState {}

final class UpdateRatingState extends ProductDetailsState {
  final double rating;

  UpdateRatingState({required this.rating});
}