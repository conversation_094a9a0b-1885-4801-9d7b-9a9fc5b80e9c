import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/models/product_details.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/models/reviews/product_reviews_model.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/repos/product_details_repo.dart';

part 'product_details_state.dart';

class ProductDetailsCubit extends Cubit<ProductDetailsState> {
  ProductDetailsCubit(this.productDetailsRepository)
      : super(ProductDetailsInitial());

  final ProductDetailsRepository productDetailsRepository;
  ShowProductDetails? showProductDetailsModel;
  ProductCardPaginatedModel? similarProductsModel;
  ProductReviewsResponse? reviewsResponseModel;

  TextEditingController messageController = TextEditingController();
  double selectedRating = 4.0;

  int currentIndex = 0;
  Map<String, int> selectedVariantIndex = {};
  num updatedPrice = 0;
  int? selectedSkuId;
  int currentPage = 1;
  bool isLoadingMore = false;

  Map<String, List<String>> uniqueVariantMap = {};
  List<String> attributeKeys = [];
  List<List<bool>> selectionStates = [];

  void changeSliderIndex(int index) {
    currentIndex = index;
    emit(ChangeSliderState());
  }

  bool areAllVariantsSelected() {
    if (showProductDetailsModel != null &&
        showProductDetailsModel!
                .productDetailsItem?.availableVariantsAttributes !=
            null) {
      for (var attribute in showProductDetailsModel!
          .productDetailsItem!.availableVariantsAttributes!) {
        if (selectedVariantIndex[attribute] == null) {
          return false;
        }
      }
    }
    return true;
  }

  Future<void> showProductDetails(int productId, {int? skuId}) async {
    emit(ProductDetailsLoadingState());

    final result = await productDetailsRepository.showProductDetails(productId);

    result.when(
      success: (data) {
        showProductDetailsModel = data;

        // Populate uniqueVariantMap
        data.productDetailsItem!.variants!.variantMap.forEach((key, valueList) {
          var uniqueValues = valueList
              .map((item) => item.value)
              .whereType<String>()
              .toSet()
              .toList();
          uniqueValues.sort();
          uniqueVariantMap[key] = uniqueValues;
        });

        attributeKeys = uniqueVariantMap.keys.toList();

        selectionStates = uniqueVariantMap.entries.map((entry) {
          return List<bool>.filled(entry.value.length, false);
        }).toList();

        // Set default price
        updatedPrice = data.productDetailsItem?.price ?? 0;

        // If SKU ID is provided, set the selected variant accordingly
        if (skuId != null) {
          _selectVariantBySkuId(skuId);
        }

        emit(ProductDetailsSuccessState());
      },
      failure: (error) {
        emit(ProductDetailsErrorState());
      },
    );
  }

  void _selectVariantBySkuId(int skuId) {
    if (showProductDetailsModel == null ||
        showProductDetailsModel!.productDetailsItem == null ||
        showProductDetailsModel!.productDetailsItem!.variants == null) {
      return;
    }

    // Find the SKU
    List<Sku>? allSkus =
        showProductDetailsModel!.productDetailsItem!.variants!.sku;
    Sku? selectedSku = allSkus?.firstWhere(
      (sku) => sku.skuId == skuId,
    );

    if (selectedSku == null) return;

    selectedSkuId = skuId;

    // Find matching attributes
    Map<String, String> selectedAttributes = {};

    showProductDetailsModel!.productDetailsItem!.variants!.variantMap
        .forEach((attribute, variants) {
      for (var variant in variants) {
        if (variant.skuId == skuId) {
          selectedAttributes[attribute] = variant.value!;
          break;
        }
      }
    });

    // Update selectedVariantIndex and selectionStates
    selectedVariantIndex.clear();

    selectedAttributes.forEach((attribute, value) {
      if (uniqueVariantMap.containsKey(attribute)) {
        int index = uniqueVariantMap[attribute]!.indexOf(value);
        if (index != -1) {
          selectedVariantIndex[attribute] = index;
          int attrIndex = attributeKeys.indexOf(attribute);
          if (attrIndex != -1) {
            selectionStates[attrIndex] =
                List<bool>.filled(uniqueVariantMap[attribute]!.length, false);
            selectionStates[attrIndex][index] = true;
          }
        }
      }
    });

    // Update price
    updatedPrice = selectedSku.priceAfterDiscount ??
        selectedSku.price ??
        showProductDetailsModel!.productDetailsItem!.price ??
        0;

    emit(ProductVariantSelectedState(
      updatedPrice: updatedPrice,
      selectedVariantIndex: Map.from(selectedVariantIndex),
      selectedSkuId: selectedSkuId,
    ));
  }

  void _filterSKUs() {
    if (showProductDetailsModel == null ||
        showProductDetailsModel!.productDetailsItem == null ||
        showProductDetailsModel!.productDetailsItem!.variants == null) {
      return;
    }

    List<Sku>? allSkus =
        showProductDetailsModel!.productDetailsItem!.variants!.sku;
    if (allSkus == null || allSkus.isEmpty) return;

    List<Sku> filteredSkus = List.from(allSkus);

    selectedVariantIndex.forEach((attribute, selectedIndex) {
      if ((uniqueVariantMap[attribute]?.length ?? 0) <= 1) {
        return;
      }
      String selectedValue = uniqueVariantMap[attribute]![selectedIndex];

      filteredSkus = filteredSkus.where((sku) {
        bool matches = showProductDetailsModel!
            .productDetailsItem!.variants!.variantMap[attribute]!
            .any((variantItem) =>
                variantItem.skuId == sku.skuId &&
                variantItem.value == selectedValue);
        return matches;
      }).toList();
    });

    if (areAllVariantsSelected()) {
      if (filteredSkus.isNotEmpty) {
        selectedSkuId = filteredSkus.first.skuId;
        updatedPrice = filteredSkus.first.priceAfterDiscount ??
            filteredSkus.first.price ??
            showProductDetailsModel!.productDetailsItem!.price ??
            0;
      } else {
        selectedSkuId = null;
        updatedPrice = showProductDetailsModel!.productDetailsItem!.price ?? 0;
      }
    } else {
      selectedSkuId = null;
      updatedPrice = showProductDetailsModel!.productDetailsItem!.price ?? 0;
    }

    logSuccess("Filtered SKU: $selectedSkuId, Updated Price: $updatedPrice");
  }

  void onOptionTap(int attributeIndex, int optionIndex) {
    String attributeKey = attributeKeys[attributeIndex];

    for (int i = 0; i < selectionStates[attributeIndex].length; i++) {
      selectionStates[attributeIndex][i] = false;
    }
    selectionStates[attributeIndex][optionIndex] = true;
    selectedVariantIndex[attributeKey] = optionIndex;

    if (!_isCurrentSelectionValid()) {
      selectedVariantIndex = {attributeKey: optionIndex};

      for (int i = 0; i < attributeKeys.length; i++) {
        if (i != attributeIndex) {
          selectionStates[i] = List<bool>.filled(
              uniqueVariantMap[attributeKeys[i]]!.length, false);
        }
      }
    }

    _filterSKUs();

    emit(ProductVariantSelectedState(
      updatedPrice: updatedPrice,
      selectedVariantIndex: Map.from(selectedVariantIndex),
      selectedSkuId: selectedSkuId,
    ));
  }

  bool _isCurrentSelectionValid() {
    if (showProductDetailsModel == null ||
        showProductDetailsModel!.productDetailsItem == null ||
        showProductDetailsModel!.productDetailsItem!.variants == null) {
      return false;
    }

    List<Sku>? allSkus =
        showProductDetailsModel!.productDetailsItem!.variants!.sku;
    if (allSkus == null || allSkus.isEmpty) return false;

    Map<String, String> currentSelection = {};
    selectedVariantIndex.forEach((attr, index) {
      currentSelection[attr] = uniqueVariantMap[attr]![index];
    });

    return allSkus.any((sku) {
      bool matches = true;
      currentSelection.forEach((attr, selectedValue) {
        bool attrMatch = showProductDetailsModel!
            .productDetailsItem!.variants!.variantMap[attr]!
            .any((variantItem) =>
                variantItem.skuId == sku.skuId &&
                variantItem.value == selectedValue);
        if (!attrMatch) {
          matches = false;
        }
      });
      return matches;
    });
  }

  Future<void> loadSimilarProducts(int productId) async {
    emit(GetSimilarProductsLoadingState());

    final result = await productDetailsRepository.getSimilarProducts(productId);

    result.when(success: (data) {
      similarProductsModel = data;
      emit(GetSimilarProductsSuccessState());
    }, failure: (error) {
      emit(GetSimilarProductsErrorState());
    });
  }

  Future<void> loadMoreSimilarProducts(int productId) async {
    if (isLoadingMore ||
        (similarProductsModel?.data?.meta?.currentPage ?? 1) >=
            (similarProductsModel?.data?.meta?.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(GetSimilarProductsLoadingMoreState());

    final nextPage = currentPage + 1;
    final result = await productDetailsRepository.getSimilarProducts(productId);

    result.when(success: (data) {
      similarProductsModel?.data?.product?.addAll(data.data?.product ?? []);
      currentPage = data.data?.meta?.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(GetSimilarProductsSuccessState());
    }, failure: (error) {
      isLoadingMore = false;
      emit(GetSimilarProductsErrorState());
    });
  }

  Future<void> getProductReviews(int productId) async {
    emit(GetProductReviewsLoadingState());

    final result = await productDetailsRepository.getProductReviews(productId);

    result.when(success: (data) {
      reviewsResponseModel = data;
      emit(GetProductReviewsSuccessState());
    }, failure: (error) {
      emit(GetProductReviewsErrorState());
    });
  }

  Future<void> loadMoreProductReviews(int productId) async {
    if (isLoadingMore ||
        (reviewsResponseModel?.productReviewsData?.meta?.currentPage ?? 1) >=
            (reviewsResponseModel?.productReviewsData?.meta?.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(GetProductReviewsLoadingMoreState());

    final nextPage = currentPage + 1;
    final result = await productDetailsRepository.getProductReviews(productId);

    result.when(success: (data) {
      reviewsResponseModel?.productReviewsData?.reviews
          ?.addAll(data.productReviewsData?.reviews ?? []);
      currentPage = data.productReviewsData?.meta?.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(GetProductReviewsSuccessState());
    }, failure: (error) {
      isLoadingMore = false;
      emit(GetProductReviewsErrorState());
    });
  }

  void updateRating(double value) {
    selectedRating = value;
    emit(UpdateRatingState(rating: value)); // Emit new state
  }

  /// Make Rate Product
  Future<void> makeRateProduct(
    int productId,
    double rate,
    String message,
  ) async {
    showLoading();
    emit(MakeRateProductLoadingState());

    final result = await productDetailsRepository.makeRateProduct(
      productId,
      rate,
      message,
    );

    result.when(success: (data) {
      hideLoading();
      emit(MakeRateProductSuccessState());
    }, failure: (error) {
      hideLoading();
      emit(MakeRateProductErrorState());
    });
  }

  @override
  Future<void> close() {
    messageController.dispose();
    return super.close();
  }
}
