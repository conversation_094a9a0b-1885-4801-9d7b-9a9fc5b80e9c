import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class BrandDetailsApiServices {
  BrandDetailsApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Get Products By Brands
  Future<Response?> getProductsByBrands(
    int brandId, {
    String? priceFrom,
    String? priceTo,
    String? rate,
  }) async {
    return _dioFactory.get(
      endPoint: EndPoints.getProductsByBrands(brandId),
      data: {
        if (priceFrom != null) 'price_from': priceFrom,
        if (priceTo != null) 'price_to': priceTo,
        if (rate != null) 'rate': rate,
      },
    );
  }

  /// Make Rate Brand
  Future<Response?> makeRateBrand(
    int brandId,
    double rate,
  ) async {
    return _dioFactory.post(
      endPoint: EndPoints.makeRateBrand,
      data: {
        'rate': rate,
        'brand_id': brandId,
      },
    );
  }

  /// Get Brand Details
  Future<Response?> getBrandDetails(int brandId) async {
    return _dioFactory.get(
      endPoint: EndPoints.showBrandsDetails(brandId),
    );
  }
}
