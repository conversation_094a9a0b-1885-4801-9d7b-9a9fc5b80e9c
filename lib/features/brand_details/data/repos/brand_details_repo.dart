import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/brand/data/models/brand_data/brand_data_model.dart';
import 'package:tegra_ecommerce_app/features/brand_details/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';

class BrandDetailsRepository {
  final BrandDetailsApiServices brandDetailsApiServices;

  BrandDetailsRepository(this.brandDetailsApiServices);

  /// Get Products By Brands
  Future<ApiResult<ProductCardPaginatedModel>> getProductsByBrands(
    int brandId, {
    String? priceFrom,
    String? priceTo,
    String? rate,
  }) async {
    final response = await brandDetailsApiServices.getProductsByBrands(
      brandId,
      priceFrom: priceFrom,
      priceTo: priceTo,
      rate: rate,
    );
    try {
      if (response!.statusCode == 200) {
        ProductCardPaginatedModel productCardPaginatedModel =
            ProductCardPaginatedModel.fromJson(response.data);

        return ApiResult.success(productCardPaginatedModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  /// Make Rate Brand
  Future<ApiResult<String>> makeRateBrand(int brandId, double rate) async {
    final response = await brandDetailsApiServices.makeRateBrand(brandId, rate);
    try {
      if (response!.statusCode == 200) {
        return ApiResult.success('Make Rate Successfully');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
   /// Get Brand Details
  Future<ApiResult<BrandDataModel>> getBrandDetails(int brandId) async {
    final response = await brandDetailsApiServices.getBrandDetails(brandId);
    try {
      if (response!.statusCode == 200) {
        BrandDataModel brandDataModel = BrandDataModel.fromJson(response.data);
        return ApiResult.success(brandDataModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }
}
