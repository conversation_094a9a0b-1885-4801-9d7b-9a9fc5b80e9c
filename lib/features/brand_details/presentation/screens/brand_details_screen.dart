import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/cache_network_image/imag_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/container/clip_container_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/empty/empty_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/gradient_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/show_more/show_more_widget.dart';
import 'package:tegra_ecommerce_app/features/brand_details/business_logic/brand_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand_details/presentation/screens/product_filter_in_brand_details.dart';
import 'package:tegra_ecommerce_app/features/brand_details/presentation/widgets/brand_details_sketlon_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_card_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_grid/product_grid_view_item_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_details_screen.dart';

class BrandDetailsScreen extends StatelessWidget {
  const BrandDetailsScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    final brandDetailsCubit = context.watch<BrandDetailsCubit>();

    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        brandDetailsCubit.getProductsByBrandsWithPagination(
          brandDetailsCubit.brandDetailsDataModel!.brands!.id!,
        );
      }
    });

    return BlocBuilder<BrandDetailsCubit, BrandDetailsState>(
      buildWhen: (previous, current) =>
          current is GetBrandDetailsSuccess ||
          current is GetBrandDetailsError ||
          current is GetBrandDetailsLoading,
      builder: (context, state) {
        final brandDetailsCubit = context.read<BrandDetailsCubit>();
        if (state is GetBrandDetailsLoading) {
          return const BrandDetailsSkeletonWidget();
        } else {
          return Scaffold(
            backgroundColor: AppColors.scaffoldBackground,
            body: SingleChildScrollView(
              controller: scrollController,
              child: Column(
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        SizedBox(
                          width: double.infinity,
                          child: CacheNetworkImagesWidget(
                            image: brandDetailsCubit
                                    .brandDetailsDataModel?.brands?.banner ??
                                '',
                            height: 227.h,
                            boxFit: BoxFit.cover,
                            haveBorder: false,
                          ),
                        ),
                        const ClipContainerInSpecificProductWidget(),
                        const GradientInSpecificProductWidget(),
                        InkWell(
                          onTap: () => context.pop(),
                          child: Container(
                            width: 60.w,
                            alignment: context.locale.languageCode == 'ar'
                                ? Alignment.topLeft
                                : Alignment.topRight,
                            margin: EdgeInsets.symmetric(horizontal: 15.sp),
                            padding: EdgeInsets.only(
                              top: MediaQuery.of(context).padding.top + 40.h,
                              left: context.locale.languageCode == 'ar'
                                  ? 16.w
                                  : 0.w,
                              right: context.locale.languageCode == 'ar'
                                  ? 0.w
                                  : 16.w,
                            ),
                            child: Container(
                              padding: EdgeInsets.all(12.sp),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(
                                    AppConstants.borderRadius),
                                color: AppColors.scaffoldBackground,
                              ),
                              child: Center(child: Icon(Icons.arrow_back)),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: -38.r,
                          left: 0,
                          right: 0,
                          child: Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.06),
                                  blurRadius: 2,
                                  spreadRadius: .6,
                                  offset: Offset(0, 3),
                                ),
                              ],
                            ),
                            child: Container(
                              width: 106.r, // 
                              height: 106.r,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppColors
                                    .neutralColor100,  
                              ),
                              child: Center(
                                child: Container(
                                  width: 100.r, 
                                  height: 100.r,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                  ),
                                  child: CachedNetworkImage(
                                    imageUrl: brandDetailsCubit
                                            .brandDetailsDataModel
                                            ?.brands
                                            ?.logo ??
                                        '',
                                    fit: BoxFit.cover,
                                    errorWidget: (context, url, error) =>
                                        Container(
                                      color: Colors.grey.shade300,
                                      child: Center(child: Icon(Icons.error)),
                                    ),
                                    placeholder: (context, url) => Container(
                                      color: Colors.grey.shade200,
                                      child: Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            // CircleAvatar(
                            //   radius: 53.r,
                            //   backgroundColor: AppColors.neutralColor100,
                            //   child: CircleAvatar(
                            //     radius: 50.r,
                            //     backgroundColor: Colors.transparent,
                            //     backgroundImage: CachedNetworkImageProvider(
                            //       brandDetailsCubit.brandDetailsDataModel
                            //               ?.brands?.logo ??
                            //           '',
                            //     ),
                            //     onBackgroundImageError:
                            //         (exception, stackTrace) =>
                            //             Icon(Icons.error),
                            //   ),
                            // ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  60.verticalSpace,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          spacing: 4.w,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Text(
                                  brandDetailsCubit
                                      .brandDetailsDataModel!.brands!.title!,
                                  style: Styles.heading3.copyWith(
                                    color: Colors.black,
                                  ),
                                ),
                                Row(
                                  spacing: 2.w,
                                  children: [
                                    SvgPicture.asset(
                                        Assets.assetsImagesSvgsStarIcon),
                                    Text(
                                      brandDetailsCubit
                                          .brandDetailsDataModel!.brands!.rate!
                                          .toString(),
                                      style: Styles.captionRegular.copyWith(
                                        color: Colors.black,
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                            if (brandDetailsCubit.brandDetailsDataModel!.brands!
                                    .isRatedBefore ==
                                false)
                              Padding(
                                padding: EdgeInsets.symmetric(vertical: 2.5.h),
                                child: ShowMoreWidget(
                                  onTapShowMore: () {
                                    AppConstants.showRatingDialogForBrand(
                                      context,
                                      brandDetailsCubit
                                          .brandDetailsDataModel!.brands!.id!,
                                    );
                                  },
                                  showMore: false,
                                  text: 'brands.evaluation'.tr(),
                                ),
                              ),
                          ],
                        ),
                        11.verticalSpace,
                        Text(
                          brandDetailsCubit
                                  .brandDetailsDataModel?.brands?.text ??
                              '',
                          style: Styles.contentRegular.copyWith(
                            color: AppColors.neutralColor1000,
                          ),
                        ),
                        12.verticalSpace,
                        Column(
                          children: [
                            Row(
                              spacing: 10.sp,
                              children: [
                                Text(
                                  'brands.products'.tr(),
                                  style: Styles.highlightEmphasis.copyWith(
                                    fontWeight: FontWeight.w700,
                                    color: Colors.black,
                                  ),
                                ),
                                Spacer(),
                                InkWell(
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      PageRouteBuilder(
                                        transitionDuration:
                                            Duration(milliseconds: 500),
                                        pageBuilder: (context, animation,
                                                secondaryAnimation) =>
                                            BlocProvider.value(
                                          value: brandDetailsCubit,
                                          child: ProductFilterInBrandDetails(),
                                        ),
                                        transitionsBuilder: (context, animation,
                                            secondaryAnimation, child) {
                                          return FadeTransition(
                                            opacity: animation,
                                            child: child,
                                          );
                                        },
                                      ),
                                    );
                                  },
                                  child: Image.asset(
                                    Assets.assetsImagesPngsSortIcon,
                                    fit: BoxFit.scaleDown,
                                  ),
                                ),
                              ],
                            ),
                            BlocBuilder<BrandDetailsCubit, BrandDetailsState>(
                              builder: (context, state) {
                                final products = brandDetailsCubit
                                        .productCardModel?.data?.product ??
                                    [];

                                if (state is GetProductsByBrandsLoading) {
                                  return GridView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    padding:
                                        EdgeInsets.symmetric(vertical: 16.h),
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 2,
                                      mainAxisExtent: 220.sp,
                                      crossAxisSpacing: 10.sp,
                                      mainAxisSpacing: 40.sp,
                                    ),
                                    itemCount: 6,
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      return ProductGridViewItemSkeletonizerWidget();
                                    },
                                  );
                                }

                                if (products.isEmpty) {
                                  return EmptyWidget(
                                    imagePath:
                                        'assets/images/svgs/emptyCategory.svg',
                                    title: 'brands.noProductInBrand'.tr(),
                                    description:
                                        'brands.browseOurOTherSections'.tr(),
                                  );
                                }

                                return GridView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  padding: EdgeInsets.symmetric(vertical: 16.h),
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 2,
                                    mainAxisExtent: 220.sp,
                                    crossAxisSpacing: 10.sp,
                                    mainAxisSpacing: 40.sp,
                                  ),
                                  itemCount: products.length,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    final product = products[index];

                                    return InkWell(
                                      onTap: () {
                                        PersistentNavBarNavigator.pushNewScreen(
                                          context,
                                          screen: BlocProvider(
                                            create: (context) =>
                                                ProductDetailsCubit(getIt())
                                                  ..showProductDetails(
                                                      product.id!)
                                                  ..loadSimilarProducts(
                                                      product.id!)
                                                  ..getProductReviews(
                                                      product.id!),
                                            child: ProductDetailsScreen(),
                                          ),
                                          withNavBar: true,
                                          pageTransitionAnimation:
                                              PageTransitionAnimation.fade,
                                        );
                                      },
                                      child: ProductCardWidget(
                                        productItem: product,
                                      ),
                                    );
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }
      },
    );
  }
}
