import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/features/brand/business_logic/brand_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand_details/business_logic/brand_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/filter/price_fom_and_to_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/filter/selected_category_widget.dart';

class ProductFilterInBrandDetails extends StatelessWidget {
  const ProductFilterInBrandDetails({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    BrandDetailsCubit brandDetailsCubit = context.read<BrandDetailsCubit>();
    brandDetailsCubit.fromPriceController.clear();
    brandDetailsCubit.toPriceController.clear();

    return Scaffold(
      backgroundColor: Color(0xffffffff),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: Padding(
          padding: EdgeInsets.only(top: 13.h),
          child: Text(
            'brands.filtering'.tr(),
            style: Styles.heading1.copyWith(
              color: AppColors.neutralColor1200,
            ),
          ),
        ),
        actions: [
          Padding(
            padding: EdgeInsets.only(left: 28.w, top: 13.h, right: 28.w),
            child: InkWell(
              onTap: () {
                context.pop();
              },
              child: SvgPicture.asset(
                width: 24,
                height: 24,
                Assets.assetsImagesSvgsCloseIcon,
              ),
            ),
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            40.verticalSpace,
            Text(
              'brands.price'.tr(),
              style: Styles.heading5.copyWith(color: Colors.black),
            ),
            12.verticalSpace,
            Row(
              spacing: 16.w,
              children: [
                PriceFromANdToWidget(
                  controller: brandDetailsCubit.fromPriceController,
                  text: 'filter.who'.tr(),
                ),
                PriceFromANdToWidget(
                  controller: brandDetailsCubit.toPriceController,
                  text: 'filter.to'.tr(),
                ),
              ],
            ),
            32.verticalSpace,
            Text(
              "filter.evaluation".tr(),
              style: Styles.heading5.copyWith(
                color: Colors.black,
              ),
            ),
            12.verticalSpace,
            BlocProvider(
              create: (context) => BrandCubit(getIt()),
              child: BlocBuilder<BrandCubit, BrandState>(
                buildWhen: (previous, current) =>
                    current is BrandRatingSelected,
                builder: (context, state) {
                  return SelectedCategoryWidget(
                    title: "filter.sortByMostToLeastRated".tr(),
                    starImage: Assets.assetsImagesSvgsStarIcon,
                    onTap: () {
                      brandDetailsCubit.selectRating();
                    },
                    isSelected: brandDetailsCubit.isRatingSelected,
                    index: 0,
                    selectedIndex: brandDetailsCubit.isRatingSelected ? 0 : 1,
                  );
                },
              ),
            ),
            32.verticalSpace,
            BlocListener<BrandDetailsCubit, BrandDetailsState>(
              listener: (context, state) {
                if (state is GetProductsByBrandsSuccess) {
                  context.pop();
                }
              },
              child: CustomButtonWidget(
                text: "filter.filtering".tr(),
                onPressed: () {
                  brandDetailsCubit.getProductsByBrands(
                    brandDetailsCubit.brandDetailsDataModel!.brands!.id!,
                    priceFrom: brandDetailsCubit.fromPriceController.text,
                    priceTo: brandDetailsCubit.toPriceController.text,
                    rate: 'desc',
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
