import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/features/brand_details/business_logic/brand_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand_details/presentation/widgets/custom_rating_widget.dart';

class RatingDialogForBrandWidget extends StatelessWidget {
  const RatingDialogForBrandWidget({
    super.key,
    required this.brandId,
  });

  final int brandId;

  @override
  Widget build(BuildContext context) {
    // final cubit = context.read<BrandDetailsCubit>();

    return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius:
            BorderRadius.circular(AppConstants.borderRadius + 4),
          ),
          child: Container(
            width: double.infinity,
            //height: 298.h,
            padding: EdgeInsets.all(20.sp),
            decoration: BoxDecoration(
              color: AppColors.scaffoldBackground,
              borderRadius:
              BorderRadius.circular((AppConstants.borderRadius + 4)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 20.verticalSpace,
                InkWell(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    width: 50.w,
                    height: 50.h,
                    padding: EdgeInsets.all(12.sp),
                    decoration: BoxDecoration(
                      color: AppColors.neutralColor100,
                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadius,
                      ),
                    ),
                    //color: Colors.black,
                    child: SvgPicture.asset(
                      width: 24,
                      height: 24,
                      Assets.assetsImagesSvgsCloseIcon,
                    ),
                  ),
                ),
                20.verticalSpace,
                Text(
                  'brands.brandevaluation'.tr(),
                  style: Styles.heading3.copyWith(color: Colors.black),
                ),

                Text(
                  'brands.evaluationDesc'.tr(),
                  textAlign: TextAlign.center,
                  style: Styles.contentRegular
                      .copyWith(color: AppColors.neutralColor1000),
                ),
                20.verticalSpace,
                CustomRatingWidget(
                  rating: 4.0,
                  onRatingUpdate: (rating) {
                    context.read<BrandDetailsCubit>().selectedRating = rating;
                    // context.read<BrandDetailsCubit>().updateRating(rating);
                  },
                  totalRatings: 500,
                ),
                20.verticalSpace,
                BlocConsumer<BrandDetailsCubit, BrandDetailsState>(
                  buildWhen: (previous, current) =>
                  current is MakeBrandRateErrorState ||
                      current is MakeBrandRateSuccessState ||
                      current is MakeBrandRateLoadingState,
                  listener: (context, state) {
                    if (state is MakeBrandRateSuccessState) {
                      context.pop();
                    }
                  },
                  builder: (context, state) {
                    return CustomButtonWidget(
                      margin: EdgeInsets.symmetric(horizontal: 70.w),
                      height: 55.h,
                      text: 'brands.toBeSure'.tr(),
                      onPressed: () {
                        // logError('${cubit.selectedRating}');
                        // logError('${brandId}');
                        context.read<BrandDetailsCubit>().makeRateBrand(brandId, context.read<BrandDetailsCubit>().selectedRating);
                      },
                    );
                  },
                )
              ],
            ),
          ),
        );
  }
}
