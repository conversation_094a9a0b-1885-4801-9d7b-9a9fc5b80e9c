import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';

class CustomRatingWidget extends StatelessWidget {
  final double rating; // Current rating (0 to 5)
  final double totalRatings; // Total number of ratings

  final void Function(double) onRatingUpdate;

  const CustomRatingWidget({
    super.key,
    required this.rating,
    required this.totalRatings,
    required this.onRatingUpdate,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        RatingBar.builder(
          initialRating: rating,
          minRating: 0,
          direction: Axis.horizontal,
          allowHalfRating: true,
          itemCount: 5,
          itemSize: 40.sp,
          itemPadding: EdgeInsets.symmetric(horizontal: 4.w),
          itemBuilder: (context, _) => Icon(
            Icons.star,
            color: AppColors.yellowColor100,
          ),
          onRatingUpdate: onRatingUpdate,
        ),
        // SizedBox(height: 8.0),
      ],
    );
  }
}
