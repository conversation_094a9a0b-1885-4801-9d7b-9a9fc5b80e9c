import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/cache_network_image/imag_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/container/clip_container_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/gradient_in_specific_product_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/show_more/show_more_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_grid/product_grid_view_item_skeletonizer_widget.dart';

class BrandDetailsSkeletonWidget extends StatelessWidget {
  const BrandDetailsSkeletonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // final ScrollController scrollController = ScrollController();
    // final brandDetailsCubit = context.read<BrandDetailsCubit>();
    return Skeletonizer(
      enabled: true,
      child: Scaffold(
        backgroundColor: AppColors.scaffoldBackground,
        body: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                alignment: Alignment.topCenter,
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: CacheNetworkImagesWidget(
                        image: "assets/images/pngs/adidas_cover.png",
                        //image: brandData.banner!,
                        height: 227.h,
                        boxFit: BoxFit.cover,
                        haveBorder: false,
                      ),
                    ),
                    const ClipContainerInSpecificProductWidget(),
                    const GradientInSpecificProductWidget(),
                    InkWell(
                      onTap: () => context.pop(),
                      child: Container(
                        width: 60.w,
                        alignment: context.locale.languageCode == 'ar'
                            ? Alignment.topLeft
                            : Alignment.topRight,
                        margin: EdgeInsets.symmetric(horizontal: 15.sp),
                        padding: EdgeInsets.only(
                          top: MediaQuery.of(context).padding.top + 40.h,
                          left:
                              context.locale.languageCode == 'ar' ? 16.w : 0.w,
                          right:
                              context.locale.languageCode == 'ar' ? 0.w : 16.w,
                        ),
                        child: Container(
                          padding: EdgeInsets.all(12.sp),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                AppConstants.borderRadius),
                            color: AppColors.scaffoldBackground,
                          ),
                          child: Center(child: Icon(Icons.arrow_back)),
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: -38.r,
                      left: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.06),
                              blurRadius: 2,
                              spreadRadius: .6,
                              offset: Offset(0, 3),
                            ),
                          ],
                        ),
                        child: CircleAvatar(
                          radius: 53.r,
                          backgroundColor: AppColors.neutralColor100,
                          child: CircleAvatar(
                            radius: 50.r,
                            backgroundColor: Colors.transparent,
                            backgroundImage: AssetImage(
                                "assets/images/pngs/logo_white_color.png"
                                //brandData.logo!,
                                // brandDetailsCubit
                                //     .brandDetailsDataModel!.brands!.first.logo!,
                                ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              60.verticalSpace,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      spacing: 4.w,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Text(
                              "dsdsdsdsdsds",
                              // brandDetailsCubit
                              //     .brandDetailsDataModel!.brands!.first.title!,
                              //brandData.title!,
                              style: Styles.heading3.copyWith(
                                color: Colors.black,
                              ),
                            ),
                            Row(
                              spacing: 2.w,
                              children: [
                                SvgPicture.asset(
                                    Assets.assetsImagesSvgsStarIcon),
                                Text(
                                  "sasasas",
                                  // brandDetailsCubit.brandDetailsDataModel!
                                  //     .brands!.first.rate!
                                  //     .toString(),
                                  //brandData.rate.toString(),
                                  style: Styles.captionRegular.copyWith(
                                    color: Colors.black,
                                  ),
                                )
                              ],
                            ),
                          ],
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 2.5.h),
                          child: ShowMoreWidget(
                            onTapShowMore: () {
                              AppConstants.showRatingDialogForBrand(context, 1
                                  // brandDetailsCubit
                                  //     .brandDetailsDataModel!.brands!.first.id!,
                                  //brandData.id!,
                                  );
                            },
                            showMore: false,
                            text: 'brands.evaluation'.tr(),
                          ),
                        ),
                      ],
                    ),
                    11.verticalSpace,
                    Text(
                      "sacsdsds",
                      // brandDetailsCubit
                      //     .brandDetailsDataModel!.brands!.first.text!,
                      // brandData.text!,
                      style: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1000,
                      ),
                    ),
                    12.verticalSpace,
                    Column(
                      children: [
                        Row(
                          spacing: 10.sp,
                          children: [
                            Text(
                              'brands.products'.tr(),
                              style: Styles.highlightEmphasis.copyWith(
                                fontWeight: FontWeight.w700,
                                color: Colors.black,
                              ),
                            ),
                            Spacer(),
                            Image.asset(
                              Assets.assetsImagesPngsSortIcon,
                              fit: BoxFit.scaleDown,
                            ),
                          ],
                        ),
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            mainAxisExtent: 220.sp,
                            crossAxisSpacing: 10.sp,
                            mainAxisSpacing: 40.sp,
                          ),
                          itemCount: 6,
                          itemBuilder: (BuildContext context, int index) {
                            return ProductGridViewItemSkeletonizerWidget();
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
