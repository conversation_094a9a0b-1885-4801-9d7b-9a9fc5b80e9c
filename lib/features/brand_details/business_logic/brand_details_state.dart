part of 'brand_details_cubit.dart';

abstract class BrandDetailsState {}

final class BrandDetailsInitial extends BrandDetailsState {}

/// Get Products By Brands
final class GetProductsByBrandsLoading extends BrandDetailsState {}

final class GetProductsByBrandsSuc<PERSON> extends BrandDetailsState {}

final class GetProductsByBrandsError extends BrandDetailsState {}

final class GetProductsByBrandsLoadingMore extends BrandDetailsState {}

final class BrandRatingSelected extends BrandDetailsState {}

/// Make Rate Brand
final class MakeBrandRateLoadingState extends BrandDetailsState {}

final class MakeBrandRateSuccessState extends BrandDetailsState {}

final class MakeBrandRateErrorState extends BrandDetailsState {}

final class UpdateRatingState extends BrandDetailsState {
  final double rating;

  UpdateRatingState({required this.rating});
}

/// Get Brands details
final class GetBrandDetailsLoading extends BrandDetailsState {}

final class GetBrandDetailsSuc<PERSON> extends BrandDetailsState {}

final class GetBrandDetailsError extends BrandDetailsState {}
