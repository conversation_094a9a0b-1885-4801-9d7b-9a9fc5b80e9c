import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/features/brand/data/models/brand_data/brand_data_model.dart';
import 'package:tegra_ecommerce_app/features/brand_details/data/repos/brand_details_repo.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';

part 'brand_details_state.dart';

class BrandDetailsCubit extends Cubit<BrandDetailsState> {
  BrandDetailsCubit(this.brandDetailsRepository) : super(BrandDetailsInitial());

  final BrandDetailsRepository brandDetailsRepository;
  ProductCardPaginatedModel? productCardModel;
  BrandDataModel? brandDetailsDataModel;
  int currentPage = 1;
  bool isLoadingMore = false;
  final TextEditingController fromPriceController = TextEditingController();
  final TextEditingController toPriceController = TextEditingController();
  bool isRatingSelected = false;
  double selectedRating = 4.0;

  /// Get Products By Brands
  Future<void> getProductsByBrands(
    int brandId, {
    String? priceFrom,
    String? priceTo,
    String? rate,
  }) async {
    if (priceFrom != null || priceTo != null || rate != null) showLoading();
    emit(GetProductsByBrandsLoading());
    final result = await brandDetailsRepository.getProductsByBrands(
      brandId,
      priceFrom: priceFrom,
      priceTo: priceTo,
      rate: rate,
    );
    result.when(
      success: (data) {
        productCardModel = data;
        if (priceFrom != null || priceTo != null || rate != null) hideLoading();
        emit(GetProductsByBrandsSuccess());
      },
      failure: (errorHandler) {
        if (priceFrom != null || priceTo != null || rate != null) hideLoading();
        emit(GetProductsByBrandsError());
      },
    );
  }

  /// Select Rating
  void selectRating() {
    isRatingSelected = !isRatingSelected;
    emit(BrandRatingSelected());
  }

  /// Get Products By Brands With Pagination
  Future<void> getProductsByBrandsWithPagination(int brandId) async {
    if (isLoadingMore ||
        (productCardModel?.data?.meta?.currentPage ?? 1) >=
            (productCardModel?.data?.meta?.lastPage ?? 1)) {
      return;

      /// Stop if already loading or no more pages
    }

    isLoadingMore = true;
    emit(GetProductsByBrandsLoadingMore());

    final nextPage = currentPage + 1;
    final result = await brandDetailsRepository.getProductsByBrands(brandId);

    result.when(success: (data) {
      productCardModel?.data?.product?.addAll(data.data?.product ?? []);
      currentPage = data.data?.meta?.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(GetProductsByBrandsSuccess());
    }, failure: (error) {
      isLoadingMore = false;
      emit(GetProductsByBrandsError());
    });
  }

  void updateRating(double value) {
    selectedRating = value;
    emit(UpdateRatingState(rating: value)); // Emit new state
  }

  /// Make Rate Brand
  Future<void> makeRateBrand(int brandId, double rate) async {
    showLoading();
    emit(MakeBrandRateLoadingState());
    final result = await brandDetailsRepository.makeRateBrand(brandId, rate);
    result.when(success: (data) {
      hideLoading();
      emit(MakeBrandRateSuccessState());
    }, failure: (error) {
      hideLoading();
      emit(MakeBrandRateErrorState());
    });
  }

  /// Get Brands details
  Future<void> getBrandDetails(int brandId) async {
    emit(GetBrandDetailsLoading());
    final result = await brandDetailsRepository.getBrandDetails(brandId);
    result.when(success: (data) {
      brandDetailsDataModel = data;
      emit(GetBrandDetailsSuccess());
    }, failure: (error) {
      emit(GetBrandDetailsError());
    });
  }

  @override
  Future<void> close() {
    fromPriceController.dispose();
    toPriceController.dispose();
    return super.close();
  }
}
