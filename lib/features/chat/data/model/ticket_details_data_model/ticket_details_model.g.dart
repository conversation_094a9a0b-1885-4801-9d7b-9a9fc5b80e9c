// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketDetailsModel _$TicketDetailsModelFromJson(Map<String, dynamic> json) =>
    TicketDetailsModel(
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
      data: TicketData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TicketDetailsModelToJson(TicketDetailsModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
      'data': instance.data,
    };

TicketData _$TicketDataFromJson(Map<String, dynamic> json) => TicketData(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
      user: json['user'] as String,
      status: json['status'] as String,
      priority: json['priority'] as String,
      messages: (json['messages'] as List<dynamic>)
          .map((e) => TicketMessage.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TicketDataToJson(TicketData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'user': instance.user,
      'status': instance.status,
      'priority': instance.priority,
      'messages': instance.messages,
    };

TicketMessage _$TicketMessageFromJson(Map<String, dynamic> json) =>
    TicketMessage(
      id: (json['id'] as num).toInt(),
      message: json['message'] as String,
      isAdmin: (json['is_admin'] as num).toInt(),
      admin: json['admin'] as String?,
    );

Map<String, dynamic> _$TicketMessageToJson(TicketMessage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'message': instance.message,
      'is_admin': instance.isAdmin,
      'admin': instance.admin,
    };
