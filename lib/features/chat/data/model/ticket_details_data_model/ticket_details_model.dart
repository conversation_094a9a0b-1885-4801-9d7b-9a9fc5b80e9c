import 'package:json_annotation/json_annotation.dart';

part 'ticket_details_model.g.dart';

@JsonSerializable()
class TicketDetailsModel {
  final String status;
  final String error;
  final int code;
  final TicketData data;

  TicketDetailsModel({
    required this.status,
    required this.error,
    required this.code,
    required this.data,
  });

  factory TicketDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$TicketDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$TicketDetailsModelToJson(this);
}

@JsonSerializable()
class TicketData {
  final int id;
  final String title;
  final String user;
  final String status;
  final String priority;
  final List<TicketMessage> messages;

  TicketData({
    required this.id,
    required this.title,
    required this.user,
    required this.status,
    required this.priority,
    required this.messages,
  });

  factory TicketData.fromJson(Map<String, dynamic> json) =>
      _$TicketDataFromJson(json);

  Map<String, dynamic> toJson() => _$TicketDataToJson(this);
}

@JsonSerializable()
class TicketMessage {
  final int id;
  final String message;
  @JsonKey(name: 'is_admin')
  final int isAdmin;
  final String? admin;

  TicketMessage({
    required this.id,
    required this.message,
    required this.isAdmin,
    this.admin,
  });

  factory TicketMessage.fromJson(Map<String, dynamic> json) =>
      _$TicketMessageFromJson(json);

  Map<String, dynamic> toJson() => _$TicketMessageToJson(this);
}
