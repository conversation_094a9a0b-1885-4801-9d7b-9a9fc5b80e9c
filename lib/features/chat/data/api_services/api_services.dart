import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class ChatApiServices {
  ChatApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Get Specific Ticket
  Future<Response?> getSpecificTicket(int ticketId) async {
    return _dioFactory.get(endPoint: EndPoints.getSpecificTicket(ticketId));
  }

  /// Send Message
  Future<Response?> sendMessage(int ticketId, String message) async {
    return _dioFactory.post(endPoint: EndPoints.sendMessage, data: {
      "ticket_id": ticketId,
      "message": message
    });
  }
}