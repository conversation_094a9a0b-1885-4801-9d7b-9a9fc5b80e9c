import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_helper.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_keys.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';

class ChatTextItemWidget extends StatelessWidget {
  const ChatTextItemWidget(
      {super.key, required this.isSender, required this.message});

  final bool isSender;
  final String message;

  // final ChatMasages messageModel;

  @override
  Widget build(BuildContext context) {
    bool isArabic = Directionality.of(context) == TextDirection.rtl;

    return Align(
      alignment: isSender ? Alignment.centerRight : Alignment.centerLeft,
      child: Row(
        mainAxisAlignment:
            isSender ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isSender) ...[
            Image.asset(
              Assets.assetsImagesPngsTicketLogo,
              width: 40.w,
              height: 40.h,
            ),
            SizedBox(width: 8.w),
          ],
          Expanded(
            child: SelectableText(
              message,
              style: Styles.contentEmphasis.copyWith(
                color: AppColors.neutralColor1200,
              ),
              textAlign: isArabic ? TextAlign.right : TextAlign.left,
              textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
            ),
          ),
          if (isSender) ...[
            SizedBox(width: 8.w),
            CircleAvatar(
              radius: 20.r,
              backgroundColor: AppColors.primaryColor800,
              backgroundImage: CachedNetworkImageProvider(
                CacheHelper.getData(key: CacheKeys.userImage) ?? "",
                errorListener: (p0) => Icon(Icons.error),
                
              ),
            ),
          ],
        ],
      ),
    );
  }
}
