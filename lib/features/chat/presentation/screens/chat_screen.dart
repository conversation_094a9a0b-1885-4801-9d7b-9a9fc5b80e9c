import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/loading_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/chat/bloc/cubit/chat_cubit.dart';
import 'package:tegra_ecommerce_app/features/chat/bloc/cubit/chat_state.dart';
import 'package:tegra_ecommerce_app/features/chat/presentation/widgets/chat_item_widget.dart';
import 'package:tegra_ecommerce_app/features/chat/presentation/widgets/chat_list_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/chat/presentation/widgets/chat_list_widget.dart';

class ChatScreen extends StatelessWidget {
  const ChatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ChatCubit, ChatState>(
      buildWhen: (previous, current) =>
          current is GetSingleChatLoadingState ||
          current is GetSingleChatErrorgState ||
          current is GetSingleChatSuccessState ||
          current is SendMassageSuccessState ||
          current is GetSpecificTicketError ||
          current is GetSpecificTicketLoading ||
          current is GetSpecificTicketSuccess,
      builder: (context, state) {
        final singleChatCubit = BlocProvider.of<ChatCubit>(context);

        if (state is GetSingleChatLoadingState) {
          return const LoadingWidget();
        } else {
          return Scaffold(
            backgroundColor: AppColors.scaffoldBackground,
            body: Column(
              children: [
                AppBarWidget(
                  rowWidget: Row(
                    spacing: 16.w,
                    children: [
                      BackButtonWidget(onTap: () => context.pop()),
                      singleChatCubit.ticketDetailsModel == null
                          ? Text(
                              '#TICKET-....',
                              style: Styles.heading2.copyWith(
                                color: AppColors.scaffoldBackground,
                              ),
                            )
                          : Text(
                              '#TICKET-${singleChatCubit.ticketDetailsModel!.data.id}',
                              style: Styles.heading2.copyWith(
                                color: AppColors.scaffoldBackground,
                              ),
                            ),
                    ],
                  ),
                ),
                Expanded(
                  child: singleChatCubit.ticketDetailsModel == null ||
                          state is GetSpecificTicketLoading
                      ? ChatListSkeletonizerWidget()
                      : singleChatCubit
                              .ticketDetailsModel!.data.messages.isEmpty
                          ? Center(
                              child: Text(
                                'support.noMessages'.tr(),
                                style: Styles.heading2.copyWith(
                                  color: AppColors.scaffoldBackground,
                                ),
                              ),
                            )
                          : ChatListWidget(singleChatCubit: singleChatCubit),
                ),
              ],
            ),
            bottomNavigationBar: Container(
              padding: EdgeInsets.only(
                  left: 20.sp,
                  top: 10.sp,
                  right: 20.sp,
                  bottom: MediaQuery.of(context).viewInsets.bottom + 10),
              child: Form(
                key: singleChatCubit.formKey,
                child: Row(
                  children: [
                    Expanded(
                      child: CustomTextFormFieldWidget(
                        backgroundColor: Colors.white,
                        isChat: true,
                        validator: (value) {
                          if (value!.trim().isEmpty) {
                            return "please_enter_valid_message".tr();
                          }
                          return null;
                        },
                        onChanged: (value) {
                          singleChatCubit.showSendBUtton(letters: value);
                        },
                        controller: singleChatCubit.masseageController,
                        labelText: "message".tr(),
                      ),
                    ),
                    BlocConsumer<ChatCubit, ChatState>(
                      buildWhen: (previous, current) =>
                          current is IsTypeingState,
                      listener: (context, state) {
                        if (state is SendMassageSuccessState) {
                          singleChatCubit.masseageController.clear();
                          singleChatCubit.isTypeing = false;
                          singleChatCubit.getSpecificTicket(
                            singleChatCubit.ticketDetailsModel!.data.id,
                          );
                        }
                      },
                      builder: (context, state) {
                        return Visibility(
                          visible: singleChatCubit.isTypeing,
                          child: IconButton(
                              onPressed: () {
                                if (singleChatCubit.formKey.currentState!
                                    .validate()) {
                                  // singleChatCubit.getSpecificTicket(
                                  //       // singleChatCubit.masseageController.text,
                                  // );
                                  singleChatCubit.sendMessage(
                                    ticketId: singleChatCubit
                                        .ticketDetailsModel!.data.id,
                                    message:
                                        singleChatCubit.masseageController.text,
                                  );
                                }
                              },
                              icon: SvgPicture.asset(
                                  Assets.assetsImagesSvgsSendIcon)),
                        );
                      },
                    )
                  ],
                ),
              ),
            ),
          );
        }
      },
    );
  }
}


