import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/features/chat/bloc/cubit/chat_state.dart';
import 'package:tegra_ecommerce_app/features/chat/data/model/ticket_details_data_model/ticket_details_model.dart';
import 'package:tegra_ecommerce_app/features/chat/data/repo/chat_repo.dart';

class ChatCubit extends Cubit<ChatState> {
  ChatCubit(this.chatRepo) : super(ChatInitial());
  ScrollController scrollController = ScrollController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  TicketDetailsModel? ticketDetailsModel;
  final ChatRepo chatRepo;

  void scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent + 50,
          duration: const Duration(milliseconds: 1),
          curve: Curves.easeOut,
        );
      }
    });
  }

  final TextEditingController masseageController = TextEditingController();

  bool isTypeing = false;

  void showSendBUtton({required String letters}) {
    if (letters.isNotEmpty) {
      if (letters.length > 1) {
        return;
      } else {
        isTypeing = true;
        emit(IsTypeingState());
      }
    } else {
      isTypeing = false;
      emit(IsTypeingState());
    }
  }

  /// Get Specific Ticket
  Future getSpecificTicket(int ticketId) async {
    emit(GetSpecificTicketLoading());
    final result = await chatRepo.getSpecificTicket(ticketId);
    result.when(success: (data) {
      ticketDetailsModel = data;
      emit(GetSpecificTicketSuccess());
    }, failure: (error) {
      emit(GetSpecificTicketError());
    });
  }

  /// Send Massage
  Future sendMessage({
    required int ticketId,
    required String message,
  }) async {
    showLoading();
    emit(SendMassageLoadingState());
    final result = await chatRepo.sendMessage(ticketId, message);
    result.when(success: (data) {
      hideLoading();
      emit(SendMassageSuccessState());
    }, failure: (error) {
      hideLoading();
      emit(SendMassageErrorgState());
    });
  }

  // void sendMassage({
  //   required String massage,
  // }) {
  //   chatmassages.add(
  //     ChatMasages(
  //       massage: massage,
  //       time: DateTime.now(),
  //       isFrom: true,
  //     ),
  //   );
  //
  //   masseageController.clear();
  //   isTypeing = false;
  //   scrollToBottom();
  //   emit(IsTypeingState());
  //   emit(SendMassageSuccessState());
  // }

  @override
  Future<void> close() {
    masseageController.dispose();
    scrollController.dispose();

    return super.close();
  }
}

// class ChatMasages {
//   final String massage;
//   final DateTime time;
//   final bool isFrom;
//   ChatMasages(
//       {required this.isFrom, required this.massage, required this.time});
// }
