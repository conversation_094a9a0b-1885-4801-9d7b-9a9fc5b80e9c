import 'package:json_annotation/json_annotation.dart';

part 'login_response_model.g.dart';

@JsonSerializable()
class LoginResponseModel {
  @JsonKey(name: 'data')
  VerificationData? verificationData;

  String? status;
  String? error;
  int? code;

  LoginResponseModel({this.verificationData, this.status, this.error, this.code});

  factory LoginResponseModel.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseModelToJson(this);
}

@JsonSerializable()
class VerificationData {
  @JsonKey(name: 'verification_code')
  int? verificationCode;

  VerificationData({this.verificationCode});

  factory VerificationData.fromJson(Map<String, dynamic> json) =>
      _$VerificationDataFromJson(json);

  Map<String, dynamic> toJson() => _$VerificationDataToJson(this);
}
