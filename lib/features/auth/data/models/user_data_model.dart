// import 'package:json_annotation/json_annotation.dart';

// part 'user_data_model.g.dart';

// @JsonSerializable()
// class UserDataModel {
//   @JsonKey(name: 'data')
//   UserData? user;

//   String? status;
//   String? error;
//   int? code;

//   UserDataModel({this.user, this.status, this.error, this.code});

//   factory UserDataModel.fromJson(Map<String, dynamic> json) =>
//       _$UserDataModelFromJson(json);

//   Map<String, dynamic> toJson() => _$UserDataModelToJson(this);
// }

// @JsonSerializable()
// class UserData {
//   int? id;
//   String? name;
//   String? email;
//   String? phone;
//   String? image;

//   @JsonKey(name: 'token')
//   String? accessToken;

//   UserData({this.id, this.name, this.email, this.phone, this.image, this.accessToken});

//   factory UserData.fromJson(Map<String, dynamic> json) =>
//       _$UserDataFromJson(json);

//   Map<String, dynamic> toJson() => _$UserDataToJson(this);
// }
import 'package:json_annotation/json_annotation.dart';

part 'user_data_model.g.dart';

@JsonSerializable()
class UserDataModel {
  @JsonKey(name: 'data')
  UserData? user;

  String? status;
  String? error;
  int? code;

  UserDataModel({this.user, this.status, this.error, this.code});

  factory UserDataModel.fromJson(Map<String, dynamic> json) =>
      _$UserDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserDataModelToJson(this);
}

@JsonSerializable()
class UserData {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? image;
  String? city;

  @JsonKey(name: 'token')
  String? accessToken;

  UserData({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.image,
    this.city,
    this.accessToken,
  });

  factory UserData.fromJson(Map<String, dynamic> json) =>
      _$UserDataFromJson(json);

  Map<String, dynamic> toJson() => _$UserDataToJson(this);
}
