import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_helper.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_keys.dart';
import 'package:tegra_ecommerce_app/core/helper_functions/flutter_toast.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/auth/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/auth/data/models/user_data_model.dart';

class AuthRepository {
  final AuthApiServices authApiServices;

  AuthRepository(this.authApiServices);

  /// Login
  Future<ApiResult<String>> userLogin({
    required String phone,
    required String password,
    String? verificationCode, // Optional verification code
  }) async {
    final response = await authApiServices.login(
      phone: phone,
      password: password,
      verificationCode: verificationCode, // Pass it if available
    );

    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        final data = response.data['data'];

        if (data != null && data['verification_code'] != null) {
          showCustomSnackbar(
            msg:
                'Verification code is ${response.data['data']['verification_code']}',
            color: Colors.green,
            time: 20,
            context: AppConstants.navigatorKey.currentContext!,
          );
          return ApiResult.success(data['verification_code'].toString());
        } else {
          UserDataModel model = UserDataModel.fromJson(response.data);

          await CacheHelper.saveSecuredString(
              key: CacheKeys.userToken, value: model.user?.accessToken);
          await CacheHelper.saveData(
              key: CacheKeys.userName, value: model.user?.name);
          if (model.user?.image != null && model.user?.image != '') {
            await CacheHelper.saveData(
                key: CacheKeys.userImage, value: model.user?.image);
          }

          AppConstants.userToken =
              await CacheHelper.getSecuredString(key: CacheKeys.userToken);

          return ApiResult.success('Login Success');
        }
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  // Future<ApiResult<String>> userLogin({
  //   required String phone,
  //   required String password,
  // }) async {
  //   final response = await authApiServices.login(
  //     phone: phone,
  //     password: password,
  //   );
  //   try {
  //     if (response!.statusCode == 200 || response.statusCode == 201) {
  //       if (response.data['data']['verification_code'] != null) {
  //         return ApiResult.success(response.data['data']['verification_code'].toString());
  //       } else {
  //         UserDataModel model = UserDataModel.fromJson(response.data);
  //         await CacheHelper.saveSecuredString(
  //             key: CacheKeys.userToken, value: model.user!.accessToken);
  //         await CacheHelper.saveData(
  //             key: CacheKeys.userName, value: model.user!.name);
  //         AppConstants.userToken =
  //             await CacheHelper.getSecuredString(key: CacheKeys.userToken);
  //         customToast(msg: model.status!, color: Colors.green);
  //         return ApiResult.success('Login Success');
  //       }
  //     } else {
  //       return ApiResult.failure(
  //         ServerException.fromResponse(response.statusCode, response),
  //       );
  //     }
  //   } on DioException catch (e) {
  //     try {
  //       handleDioException(e);
  //     } on ServerException catch (ex) {
  //       return ApiResult.failure(ex.errorModel.errorMessage);
  //     }
  //   }
  //
  //   return ApiResult.failure(
  //       FailureException(errMessage: 'Unexpected error occurred'));
  // }

  /// Register
  Future<ApiResult<String>> userRegister({
    required String name,
    required String phone,
    File? image,
    required String email,
    required String password,
    required String rePassword,
  }) async {
    final response = await authApiServices.register(
      name: name,
      phone: phone,
      image: image,
      email: email,
      password: password,
      rePassword: rePassword,
    );
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        // final data = response.data['data'];

        // if (data != null && data['verification_code'] != null) {
        // showCustomSnackbar(
        //   msg:
        //       'Verification code is ${response.data['data']['verification_code']}',
        //   color: Colors.green,
        //   time: 20,
        //   context: AppConstants.navigatorKey.currentContext!,
        // );
        // return ApiResult.success(data['verification_code'].toString());
        // } else
        // {
        UserDataModel model = UserDataModel.fromJson(response.data);

        await CacheHelper.saveSecuredString(
            key: CacheKeys.userToken, value: model.user?.accessToken);
        await CacheHelper.saveData(
            key: CacheKeys.userName, value: model.user?.name);
        await CacheHelper.saveData(
            key: CacheKeys.email, value: model.user?.email);
        if (model.user?.image != null && model.user?.image != '') {
          await CacheHelper.saveData(
              key: CacheKeys.userImage, value: model.user?.image);
        }

        AppConstants.userToken =
            await CacheHelper.getSecuredString(key: CacheKeys.userToken);
        customToast(msg: model.status!, color: Colors.green);
        return ApiResult.success('Register Success');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(
        errMessage: 'Unexpected error occurred',
      ),
    );
  }
}
