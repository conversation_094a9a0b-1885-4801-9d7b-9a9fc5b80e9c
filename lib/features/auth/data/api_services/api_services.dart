import 'dart:io';

import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class AuthApiServices {
  AuthApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Login

  Future<Response?> login({
    required String phone,
    required String password,
    String? verificationCode,
  }) async {
    Map<String, dynamic> data = {};
    if (verificationCode == null) {
      data = {
        'phone': phone,
        'password': password,
      };
    } else {
      data = {
        'phone': phone,
        'password': password,
        "verification_code": verificationCode,
      };
    }
    return _dioFactory.post(endPoint: EndPoints.login, data: data);
  }

  /// Register
  Future<Response?> register({
    required String name,
    required String phone,
    File? image,
    required String password,
    required String rePassword,
    required String email,
  }) async {
    Map<String, dynamic> formDataMap = {
      'email': email,
      'password': password,
      'name': name,
      'phone': phone,
      'repassword': rePassword,
    };

    formDataMap
        .removeWhere((key, value) => value == null || value.toString().isEmpty);

    if (image != null) {
      String userImage = image.path.split('/').last;
      formDataMap['image'] =
          await MultipartFile.fromFile(image.path, filename: userImage);
    }

    FormData formData = FormData.fromMap(formDataMap);

    return _dioFactory.post(
      endPoint: EndPoints.register,
      data: formData,
    );
  }
}
