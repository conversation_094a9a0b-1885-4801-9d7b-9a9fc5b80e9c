import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/text/custom_text_rich_widget.dart';
import 'package:tegra_ecommerce_app/features/auth/business_logic/auth_cubit.dart';

class ResendCodeWidget extends StatelessWidget {
  const ResendCodeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final loginCubit = context.read<AuthCubit>();

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  "otp.didNotReceiveCode".tr(),
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor500,
                  ),
                  maxLines: 2,
                ),
              ),
              BlocBuilder<AuthCubit, AuthState>(
                buildWhen: (previous, current) => current is CountdownUpdated,
                builder: (context, state) {
                  return CustomRichText(
                    text1: loginCubit.canResend
                        ? ''
                        : ' : ${loginCubit.seconds} ${'otp.seconds'.tr()}',
                    text2: loginCubit.canResend ? 'otp.resend'.tr() : '',
                    onTap2: loginCubit.canResend
                        ? () {
                            loginCubit.startTimer();
                          }
                        : null,
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
