import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/helper_functions/validation.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text/custom_text_rich_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/auth/business_logic/auth_cubit.dart';

class RegisterFormWidget extends StatelessWidget {
  const RegisterFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final registerCubit = context.read<AuthCubit>();
    return Form(
      key: registerCubit.formKey,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            CustomTextFormFieldWidget(
                controller: context.read<AuthCubit>().nameController,
                labelText: 'auth.fullName'.tr(),
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.next,
                prefixIcon: SvgPicture.asset(
                  'assets/images/svgs/user_name_icon.svg',
                  fit: BoxFit.scaleDown,
                ),
                backgroundColor: Colors.white,
                borderRadius: AppConstants.borderRadius,
                validator: (value) => AppValidator.validateUsername(value)),
            SizedBox(height: 16.h),
            CustomTextFormFieldWidget(
                controller: context.read<AuthCubit>().emailController,
                labelText: 'auth.email'.tr(),
                prefixIcon: Icon(Icons.email_outlined),
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.next,
                backgroundColor: Colors.white,
                borderRadius: AppConstants.borderRadius,
                validator: (value) => AppValidator.validateEmail(value)),
            SizedBox(height: 16.h),
            CustomTextFormFieldWidget(
              controller: context.read<AuthCubit>().phoneController,
              labelText: 'auth.phoneNumber'.tr(),
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.next,
              inputFormatters: [
                // FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                // LengthLimitingTextInputFormatter(9),
              ],
              prefixIcon: SvgPicture.asset(
                'assets/images/svgs/phone_icon.svg',
                fit: BoxFit.scaleDown,
              ),
              suffixIcon: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
                child: Text(
                  '966+',
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor1200,
                  ),
                ),
              ),
              backgroundColor: Colors.white,
              borderRadius: AppConstants.borderRadius,
              // validator: (value) =>
              //     AppValidator.validateSaudiPhoneNumber(value),
            ),
            SizedBox(height: 16.h),
            BlocBuilder<AuthCubit, AuthState>(
              buildWhen: (previous, current) => current is TogglePasswordState,
              builder: (context, state) {
                return CustomTextFormFieldWidget(
                  controller: context.read<AuthCubit>().passwordController,
                  labelText: 'auth.password'.tr(),
                  keyboardType: TextInputType.visiblePassword,
                  textInputAction: TextInputAction.done,
                  obscureText: context.read<AuthCubit>().isObscure,
                  prefixIcon: SvgPicture.asset(
                    'assets/images/svgs/lock_icon.svg',
                    fit: BoxFit.scaleDown,
                  ),
                  suffixIcon: IconButton(
                    onPressed: () => context.read<AuthCubit>().toggleObscure(),
                    icon: SvgPicture.asset(
                      context.read<AuthCubit>().isObscure
                          ? 'assets/images/svgs/eye_icon_close.svg'
                          : 'assets/images/svgs/eye_icon.svg',
                      fit: BoxFit.scaleDown,
                    ),
                  ),
                  backgroundColor: Colors.white,
                  borderRadius: AppConstants.borderRadius,
                  validator: (value) => AppValidator.validatePassword(value),
                );
              },
            ),
            SizedBox(height: 16.h),
            BlocBuilder<AuthCubit, AuthState>(
              buildWhen: (previous, current) => current is TogglePasswordState2,
              builder: (context, state) {
                return CustomTextFormFieldWidget(
                  controller:
                      context.read<AuthCubit>().confirmPasswordController,
                  labelText: 'auth.confirmPassword'.tr(),
                  keyboardType: TextInputType.visiblePassword,
                  textInputAction: TextInputAction.done,
                  obscureText: context.read<AuthCubit>().isObscure2,
                  prefixIcon: SvgPicture.asset(
                    'assets/images/svgs/lock_icon.svg',
                    fit: BoxFit.scaleDown,
                  ),
                  suffixIcon: IconButton(
                    onPressed: () => context.read<AuthCubit>().toggleObscure2(),
                    icon: SvgPicture.asset(
                      context.read<AuthCubit>().isObscure
                          ? 'assets/images/svgs/eye_icon_close.svg'
                          : 'assets/images/svgs/eye_icon.svg',
                      fit: BoxFit.scaleDown,
                    ),
                  ),
                  backgroundColor: Colors.white,
                  borderRadius: 8.r,
                  validator: (value) => AppValidator.validateConfirmPassword(
                      value, context.read<AuthCubit>().passwordController.text),
                );
              },
            ),
            SizedBox(height: 16.h),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(right: 8.w),
                  child: SvgPicture.asset(
                    Assets.assetsImagesSvgsInfoIcon,
                    fit: BoxFit.cover,
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "auth.confirmationCodeMessage".tr(),
                        style: Styles.captionRegular.copyWith(
                          color: AppColors.neutralColor500,
                        ),
                        overflow: TextOverflow.clip,
                      ),
                      4.verticalSpace,
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 83.h),
            BlocListener<AuthCubit, AuthState>(
              listenWhen: (previous, current) =>
                  current is RegisterSuccess ||
                  current is RegisterError ||
                  current is RegisterLoading,
              listener: (context, state) {
                if (state is RegisterSuccess) {
                  context.pushNamedAndRemoveUntil(Routes.mainlayoutScreen);
                }
              },
              child: CustomButtonWidget(
                text: "auth.confirm".tr(),
                width: double.infinity,
                elevation: 0,
                onPressed: () {
                  if (registerCubit.formKey.currentState!.validate()) {
                    context.read<AuthCubit>().userRegister();
                  }
                },
              ),
            ),
            SizedBox(height: 32.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomRichText(
                  text1: "auth.alreadyHaveAccount".tr(),
                  text2: "auth.signIn".tr(),
                  onTap2: () =>
                      context.pushReplacementNamed(Routes.loginScreen),
                ),
              ],
            ),
            SizedBox(height: 32.h),
          ],
        ),
      ),
    );
  }
}
