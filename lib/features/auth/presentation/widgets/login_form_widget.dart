import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:page_transition/page_transition.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/helper_functions/validation.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text/custom_text_rich_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/auth/business_logic/auth_cubit.dart';
import 'package:tegra_ecommerce_app/features/auth/presentation/screens/verify_otp_screen.dart';

class LoginFormWidget extends StatelessWidget {
  const LoginFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final loginCubit = context.read<AuthCubit>();

    return Form(
      key: loginCubit.formKey,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// Phone Field
            CustomTextFormFieldWidget(
              controller: context.read<AuthCubit>().phoneController,
              labelText: 'auth.phoneNumber'.tr(),
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.next,
              inputFormatters: [
                // FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                // LengthLimitingTextInputFormatter(9),
              ],
              prefixIcon: SvgPicture.asset(
                'assets/images/svgs/phone_icon.svg',
                fit: BoxFit.scaleDown,
              ),
              suffixIcon: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
                child: Text(
                  '966+',
                  style: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor1200,
                  ),
                ),
              ),
              backgroundColor: Colors.white,
              borderRadius: AppConstants.borderRadius,
              // validator: (value) =>
              //     AppValidator.validateSaudiPhoneNumber(value),
            ),
            SizedBox(height: 20.h),

            /// Password Field
            BlocBuilder<AuthCubit, AuthState>(
              buildWhen: (previous, current) => current is TogglePasswordState,
              builder: (context, state) {
                return CustomTextFormFieldWidget(
                  controller: context.read<AuthCubit>().passwordController,
                  labelText: 'auth.password'.tr(),
                  keyboardType: TextInputType.visiblePassword,
                  textInputAction: TextInputAction.done,
                  obscureText: context.read<AuthCubit>().isObscure,
                  prefixIcon: SvgPicture.asset(
                    'assets/images/svgs/lock_icon.svg',
                    fit: BoxFit.scaleDown,
                  ),
                  suffixIcon: IconButton(
                    onPressed: () => context.read<AuthCubit>().toggleObscure(),
                    icon: SvgPicture.asset(
                      context.read<AuthCubit>().isObscure
                          ? 'assets/images/svgs/eye_icon_close.svg'
                          : 'assets/images/svgs/eye_icon.svg',
                      fit: BoxFit.scaleDown,
                    ),
                  ),
                  backgroundColor: Colors.white,
                  borderRadius: AppConstants.borderRadius,
                  validator: (value) => AppValidator.validatePassword(value),
                );
              },
            ),
            SizedBox(height: 20.h),

            /// Forgot Password
            Text(
              'auth.forgotPassword'.tr(),
              style: Styles.contentEmphasis.copyWith(
                color: AppColors.primaryColor900,
              ),
            ),
            SizedBox(height: 71.h),

            /// Login Button
            BlocListener<AuthCubit, AuthState>(
              listenWhen: (previous, current) => current is GoToOTPScreen,
              listener: (context, state) {
                // if (state is LoginSuccess) {
                //   context.pushNamedAndRemoveUntil(
                //     Routes.verifyOtpScreen,
                //   );
                // }

                if (state is GoToOTPScreen) {
                  Navigator.push(
                    context,
                    PageTransition(
                      type: PageTransitionType.fade,
                      child: BlocProvider.value(
                        value: context.read<AuthCubit>(),
                        child: VerifyOtpScreen(),
                      ),
                    ),
                  );
                }
              },
              child: CustomButtonWidget(
                text: 'auth.login'.tr(),
                width: double.infinity,
                elevation: 0,
                onPressed: () {
                  if (loginCubit.formKey.currentState!.validate()) {
                    context.read<AuthCubit>().userLogin(context);
                  }
                },
              ),
            ),
            SizedBox(height: 32.h),

            /// Don't have an account
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomRichText(
                  text1: 'auth.dontHaveAccount'.tr(),
                  text2: 'auth.createAccount'.tr(),
                  onTap2: () {
                    context.pushNamed(
                      Routes.registerScreen,
                    );
                  },
                ),
              ],
            ),

            SizedBox(height: 32.h),
          ],
        ),
      ),
    );
  }
}
