import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:pinput/pinput.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/helper_functions/validation.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/features/auth/business_logic/auth_cubit.dart';
import 'package:tegra_ecommerce_app/features/auth/presentation/widgets/resend_code_widget.dart';

class VerifyOtpFormWidget extends StatelessWidget {
  const VerifyOtpFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final loginCubit = context.read<AuthCubit>();

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Column(
            children: [
              /// OTP
              Pinput(
                controller: loginCubit.pinController,
                length: 5,
                autofocus: true,
                obscureText: false,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.done,
                pinAnimationType: PinAnimationType.fade,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(5),
                ],
                onChanged: (pin) {
                  // print('Pin entered: $pin');
                },
                onCompleted: (pin) {
                  context.read<AuthCubit>().userLogin(context);
                  // }
                },
                defaultPinTheme: PinTheme(
                  width: 50.w,
                  height: 50.h,
                  textStyle: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor1200,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.neutralColor200),
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius,
                    ),
                  ),
                ),
                focusedPinTheme: PinTheme(
                  
                  width: 50.w,
                  height: 50.h,
                  textStyle: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor1200,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.primaryColor900),
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius,
                    ),
                  ),
                ),
                validator: (pin) => AppValidator.validateOTP(pin),
                errorPinTheme: PinTheme(
                  width: 50.w,
                  height: 50.h,
                  textStyle: Styles.contentEmphasis.copyWith(
                    color: AppColors.neutralColor1200,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.redColor100),
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius,
                    ),
                  ),
                ),
                errorText: 'otp.validOtp'.tr(),
                errorTextStyle: Styles.contentEmphasis.copyWith(
                  color: AppColors.redColor100,
                ),
              ),
              SizedBox(height: 16.h),

              /// Implement Resend OTP
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(right: 8.w),
                    child: SvgPicture.asset(
                      'assets/images/svgs/info_icon.svg',
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(width: 4.w),

                  /// Resend Code
                  const ResendCodeWidget(),
                ],
              ),
            ],
          ),
        ),
        SizedBox(height: 142.h),

        /// Confirm Button
        BlocListener<AuthCubit, AuthState>(
          listener: (context, state) {
            if (state is LoginSuccess) {
              context.pushNamedAndRemoveUntil(
                Routes.mainlayoutScreen,
              );
            }
          },
          child: CustomButtonWidget(
            text: 'otp.confirm'.tr(),
            width: double.infinity,
            elevation: 0,
            onPressed: () {
              if (loginCubit.formKey.currentState!.validate()) {
                // logSuccess('Valid');
                if (context.read<AuthCubit>().otpCode ==
                    context.read<AuthCubit>().pinController.text) {
                  context.read<AuthCubit>().userLogin(context);
                } else {
                  showError("otp.validOtp".tr());
                }
              }
            },
          ),
        ),
      ],
    );
  }
}
