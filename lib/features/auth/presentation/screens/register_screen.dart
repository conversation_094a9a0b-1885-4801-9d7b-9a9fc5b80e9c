import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/auth/presentation/widgets/register_form_widget.dart';

class RegisterScreen extends StatelessWidget {
  const RegisterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Image.asset(
            //   Assets.assetsImagesPngsTopCurvedImage,
            //   width: double.infinity,
            //   fit: BoxFit.fill,
            // ),
            SizedBox(height: 64.h),
            Text(
              "auth.registerWelcome".tr(),
              style: Styles.heading1.copyWith(color: AppColors.primaryColor900),
            ),
            Sized<PERSON><PERSON>(height: 8.h),
            Text(
              "auth.registerDescription".tr(),
              style: Styles.highlightEmphasis.copyWith(
                color: AppColors.neutralColor300,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 50.h),
            const RegisterFormWidget(),
          ],
        ),
      ),
    );
  }
}
