import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/auth/presentation/widgets/verify_otp_form.dart';

class VerifyOtpScreen extends StatelessWidget {
  const VerifyOtpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SingleChildScrollView(
          child: Column(
            children: [
              Image.asset(
                'assets/images/pngs/top_curved_image.png',
                width: double.infinity,
                fit: BoxFit.fill,
              ),
              SizedBox(height: 63.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Column(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Column(
                        children: [
                          Text(
                            'otp.accountVerification'.tr(),
                            style: Styles.heading1.copyWith(
                              color: AppColors.primaryColor900,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'otp.enterCodeSentTo'.tr(args: ['83*********010']),
                            style: Styles.highlightEmphasis.copyWith(
                              color: AppColors.neutralColor300,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 50.h),
                    const VerifyOtpFormWidget(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
