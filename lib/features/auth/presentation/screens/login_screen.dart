import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/auth/presentation/widgets/login_form_widget.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            Image.asset(
              Assets.assetsImagesPngsTopCurvedImage,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SizedBox(height: 63.h),
            Text(
              'auth.welcomeBack'.tr(),
              style: Styles.heading1.copyWith(
                color: AppColors.primaryColor900,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'auth.loginToAccount'.tr(),
              style: Styles.highlightEmphasis.copyWith(
                color: AppColors.neutralColor300,
              ),
            ),
            SizedBox(height: 50.h),
            const LoginFormWidget(),
          ],
        ),
      ),
    );
  }
}
