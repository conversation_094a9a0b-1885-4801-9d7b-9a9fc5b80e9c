import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/features/auth/data/repos/auth_repo.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit(this.authRepository) : super(AuthInitial());

  final AuthRepository authRepository;
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  File? pickedImage;
  bool isObscure = true;
  bool isObscure2 = true;
  final formKey = GlobalKey<FormState>();
  final pinController = TextEditingController();

  Timer? timer;
  int seconds = 60;
  bool canResend = false;

  String? otpCode;

  /// Start Countdown Timer
  void startTimer() {
    seconds = 60;
    canResend = false;
    emit(CountdownUpdated(seconds, canResend));

    timer?.cancel();
    timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (seconds > 1) {
        seconds--;
        emit(CountdownUpdated(seconds, canResend));
      } else {
        timer.cancel();
        canResend = true;
        emit(CountdownUpdated(seconds, canResend));
      }
    });
  }

  /// Stop Timer
  void stopTimer() {
    timer?.cancel();
  }

  /// Toggle Password
  void toggleObscure() {
    isObscure = !isObscure;
    emit(TogglePasswordState());
  }

  /// Toggle Password
  void toggleObscure2() {
    isObscure2 = !isObscure2;
    emit(TogglePasswordState2());
  }

  /// Login
  Future userLogin(BuildContext context) async {
    showLoading();
    emit(LoginLoading());

    try {
      final result = await authRepository.userLogin(
        phone: phoneController.text,
        password: passwordController.text,
        verificationCode: pinController.text == '' ? null : pinController.text,
      );

      result.when(
        success: (success) {
          hideLoading();

          if (pinController.text != '') {
            emit(LoginSuccess());
          } else {
            emit(GoToOTPScreen());
          }
        },
        failure: (error) {
          hideLoading();

          ToastManager.showCustomToast(
            message: error,
            backgroundColor: Colors.red,
          );

          emit(LoginError());
        },
      );
    } on DioException catch (e) {
      hideLoading();

      // ✅ استخدم handleDioException للحصول على ServerException
      try {
        handleDioException(e); // هترمي ServerException تلقائياً
      } on ServerException catch (serverError) {
        ToastManager.showCustomToast(
          message: serverError.errorModel.errorMessage,
          backgroundColor: Colors.red,
        );
      }

      emit(LoginError());
    } catch (e) {
      hideLoading();

      ToastManager.showCustomToast(
        message: 'حدث خطأ غير متوقع. حاول مرة أخرى.',
        backgroundColor: Colors.red,
      );

      emit(LoginError());
    }
  }

  // /// Login
  // Future userLogin(BuildContext context) async {
  //   showLoading();
  //   emit(LoginLoading());

  //   final result = await authRepository.userLogin(
  //     phone: phoneController.text,
  //     password: passwordController.text,
  //     verificationCode: pinController.text == '' ? null : pinController.text,
  //   );

  //   result.when(success: (success) {
  //     hideLoading();

  //     // if (success == 'Login Success') {
  //     if (pinController.text != '') {
  //       emit(LoginSuccess());
  //     } else {
  //       emit(GoToOTPScreen());
  //     }
  //   }, failure: (error) {
  //     hideLoading();
  //     emit(LoginError());
  //   });
  // }

  /// Pick Image
  Future pickImage(ImageSource source, context) async {
    try {
      final picker = ImagePicker();
      final image = await picker.pickImage(source: source);
      if (image == null) return;
      final imageTemp = File(image.path);

      pickedImage = imageTemp;

      emit(PickImageSuccessState());
    } catch (e) {
      emit(PickImageErrorState());
    }
  }

  /// Register
  Future userRegister() async {
    showLoading();
    emit(RegisterLoading());
    final result = await authRepository.userRegister(
      name: nameController.text,
      phone: phoneController.text,
      image: null,
      email: emailController.text,
      password: passwordController.text,
      rePassword: confirmPasswordController.text,
    );
    result.when(success: (success) {
      hideLoading();
      emit(RegisterSuccess());
    }, failure: (error) {
      hideLoading();
      emit(RegisterError());
    });
  }

  /// Dispose Controllers
  @override
  Future<void> close() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    passwordController.dispose();
    stopTimer();
    return super.close();
  }
}
