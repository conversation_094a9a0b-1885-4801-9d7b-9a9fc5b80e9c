import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/helper_functions/validation.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/profile/business_logic/profile_cubit.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/widgets/push_fucntion.dart';

class ChangePasswordScreen extends StatelessWidget {
  const ChangePasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    ProfileCubit profileCubit = BlocProvider.of<ProfileCubit>(context);

    return Scaffold(
      backgroundColor: AppColors.neutralColor100,
      body: BlocBuilder<ProfileCubit, ProfileState>(
        buildWhen: (previous, current) =>
            current is IsConfirmGoTo ||
            current is UpdatePasswordLoadingState ||
            current is UpdatePasswordSuccessState ||
            current is UpdatePasswordErrorState,
        builder: (context, state) {
          return Form(
            key: profileCubit.formkey,
            child: Column(
              children: [
                AppBarWidget(
                  rowWidget: Row(
                    spacing: 16.w,
                    children: [
                      BackButtonWidget(onTap: () => context.pop()),
                      Text(
                        'profile.editPassword'.tr(),
                        style: Styles.heading2.copyWith(
                          color: AppColors.scaffoldBackground,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 27.h),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Column(
                    children: [
                      profileCubit.isConfirm == true
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Column(
                                  spacing: 8.h,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(8.sp),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: AppColors.neutralColor700,
                                          width: 1.w,
                                        ),
                                        borderRadius: BorderRadius.circular(
                                          AppConstants.borderRadius / 2,
                                        ),
                                      ),
                                      child: Center(
                                        child: SvgPicture.asset(
                                          'assets/images/svgs/lock_icon2.svg',
                                          colorFilter: ColorFilter.mode(
                                            AppColors.neutralColor700,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Text(
                                      'profile.newPassword'.tr(),
                                      style: Styles.captionRegular.copyWith(
                                        color: AppColors.neutralColor1200,
                                      ),
                                    )
                                  ],
                                ),
                                SizedBox(width: 16.w),
                                Column(
                                  spacing: 8.h,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(8.sp),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: AppColors.secondaryColor500,
                                          width: 1.w,
                                        ),
                                        borderRadius: BorderRadius.circular(
                                          AppConstants.borderRadius / 2,
                                        ),
                                      ),
                                      child: Center(
                                        child: SvgPicture.asset(
                                          'assets/images/svgs/lock_icon2.svg',
                                          colorFilter: ColorFilter.mode(
                                            AppColors.secondaryColor500,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Text(
                                      'profile.oldPassword'.tr(),
                                      style: Styles.captionRegular.copyWith(
                                        color: AppColors.neutralColor1200,
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Column(
                                  spacing: 8.h,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(8.sp),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: AppColors.neutralColor700,
                                          width: 1.w,
                                        ),
                                        borderRadius: BorderRadius.circular(
                                          AppConstants.borderRadius / 2,
                                        ),
                                      ),
                                      child: Center(
                                        child: SvgPicture.asset(
                                          'assets/images/svgs/lock_icon2.svg',
                                          colorFilter: ColorFilter.mode(
                                            AppColors.secondaryColor500,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Text(
                                      'profile.newPassword'.tr(),
                                      style: Styles.captionRegular.copyWith(
                                        color: AppColors.secondaryColor500,
                                      ),
                                    )
                                  ],
                                ),
                                SizedBox(width: 16.w),
                                Column(
                                  spacing: 8.h,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(8.sp),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: AppColors.neutralColor700,
                                          width: 1.w,
                                        ),
                                        borderRadius: BorderRadius.circular(
                                          AppConstants.borderRadius / 2,
                                        ),
                                      ),
                                      child: Center(
                                        child: SvgPicture.asset(
                                          'assets/images/svgs/lock_icon2.svg',
                                          colorFilter: ColorFilter.mode(
                                            AppColors.neutralColor700,
                                            BlendMode.srcIn,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Text(
                                      'profile.oldPassword'.tr(),
                                      style: Styles.captionRegular.copyWith(
                                        color: AppColors.neutralColor1200,
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                    ],
                  ),
                ),
                SizedBox(height: 12.h),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 16.h),
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Row(
                    spacing: 4.w,
                    children: [
                      SvgPicture.asset(
                          'assets/images/svgs/icon2_in_edit_passwrod.svg'),
                      Expanded(
                        child: Container(
                          height: 3,
                          decoration: BoxDecoration(
                              color: profileCubit.isConfirm == true
                                  ? AppColors.greenColor10
                                  : AppColors.greenColor200,
                              borderRadius: BorderRadius.circular(
                                  (AppConstants.borderRadius * 5) - 4)),
                        ),
                      ),
                      profileCubit.isConfirm == true
                          ? SvgPicture.asset(
                              'assets/images/svgs/icon1_in_edit_passwrod.svg',
                            )
                          : SvgPicture.asset(
                              'assets/images/svgs/icon3_in_edit_passwrod.svg',
                            ),
                    ],
                  ),
                ),
                SizedBox(height: 6.h),
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 24.h,
                  ),
                  child: profileCubit.isConfirm == true
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 20.h,
                          children: [
                            BlocBuilder<ProfileCubit, ProfileState>(
                              buildWhen: (previous, current) =>
                                  current is TogglePasswordState,
                              builder: (context, state) {
                                return CustomTextFormFieldWidget(
                                  controller: context
                                      .read<ProfileCubit>()
                                      .oldPasswordController,
                                  labelText: 'profile.oldPassword'.tr(),
                                  keyboardType: TextInputType.visiblePassword,
                                  textInputAction: TextInputAction.done,
                                  obscureText:
                                      context.read<ProfileCubit>().isObscure,
                                  prefixIcon: SvgPicture.asset(
                                    'assets/images/svgs/lock_icon.svg',
                                    fit: BoxFit.scaleDown,
                                  ),
                                  suffixIcon: IconButton(
                                    onPressed: () => context
                                        .read<ProfileCubit>()
                                        .toggleObscure(),
                                    icon: SvgPicture.asset(
                                      context.read<ProfileCubit>().isObscure
                                          ? 'assets/images/svgs/eye_icon_close.svg'
                                          : 'assets/images/svgs/eye_icon.svg',
                                      fit: BoxFit.scaleDown,
                                    ),
                                  ),
                                  backgroundColor: Colors.white,
                                  borderRadius: AppConstants.borderRadius,
                                  validator: (value) =>
                                      AppValidator.validatePassword(value),
                                );
                              },
                            ),
                            Text(
                              'profile.forgetYourPassword'.tr(),
                              style: Styles.contentEmphasis.copyWith(
                                color: AppColors.primaryColor900,
                              ),
                            ),
                          ],
                        )
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          spacing: 20.h,
                          children: [
                            BlocBuilder<ProfileCubit, ProfileState>(
                              buildWhen: (previous, current) =>
                                  current is TogglePasswordState2,
                              builder: (context, state) {
                                return CustomTextFormFieldWidget(
                                  controller: context
                                      .read<ProfileCubit>()
                                      .newPasswordController,
                                  labelText: 'profile.newPassword'.tr(),
                                  keyboardType: TextInputType.visiblePassword,
                                  textInputAction: TextInputAction.done,
                                  obscureText:
                                      context.read<ProfileCubit>().isObscure2,
                                  prefixIcon: SvgPicture.asset(
                                    'assets/images/svgs/lock_icon.svg',
                                    fit: BoxFit.scaleDown,
                                  ),
                                  suffixIcon: IconButton(
                                    onPressed: () => context
                                        .read<ProfileCubit>()
                                        .toggleObscure2(),
                                    icon: SvgPicture.asset(
                                      context.read<ProfileCubit>().isObscure2
                                          ? 'assets/images/svgs/eye_icon_close.svg'
                                          : 'assets/images/svgs/eye_icon.svg',
                                      fit: BoxFit.scaleDown,
                                    ),
                                  ),
                                  backgroundColor: Colors.white,
                                  borderRadius: AppConstants.borderRadius,
                                  validator: (value) =>
                                      AppValidator.validatePassword(value),
                                );
                              },
                            ),
                            BlocBuilder<ProfileCubit, ProfileState>(
                              buildWhen: (previous, current) =>
                                  current is TogglePasswordState3,
                              builder: (context, state) {
                                return CustomTextFormFieldWidget(
                                  controller: context
                                      .read<ProfileCubit>()
                                      .confirmNewPasswordController,
                                  labelText: 'profile.confirmNewPassword'.tr(),
                                  keyboardType: TextInputType.visiblePassword,
                                  textInputAction: TextInputAction.done,
                                  obscureText:
                                      context.read<ProfileCubit>().isObscure3,
                                  prefixIcon: SvgPicture.asset(
                                    'assets/images/svgs/lock_icon.svg',
                                    fit: BoxFit.scaleDown,
                                  ),
                                  suffixIcon: IconButton(
                                    onPressed: () => context
                                        .read<ProfileCubit>()
                                        .toggleObscure3(),
                                    icon: SvgPicture.asset(
                                      context.read<ProfileCubit>().isObscure3
                                          ? 'assets/images/svgs/eye_icon_close.svg'
                                          : 'assets/images/svgs/eye_icon.svg',
                                      fit: BoxFit.scaleDown,
                                    ),
                                  ),
                                  backgroundColor: Colors.white,
                                  borderRadius: AppConstants.borderRadius,
                                  validator: (value) =>
                                      AppValidator.validateConfirmPassword(
                                    context
                                        .read<ProfileCubit>()
                                        .newPasswordController
                                        .text,
                                    value,
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                ),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: BlocListener<ProfileCubit, ProfileState>(
        listener: (context, state) {
          if (state is UpdatePasswordSuccessState) {
            //   PersistentNavBarNavigator.pushDynamicScreen(
            //     withNavBar: ,
            //     context,

            //   );
            pushTOLogin(context);
          }
        },
        child: CustomButtonWidget(
          margin: EdgeInsets.symmetric(vertical: 18.h, horizontal: 16.w),
          text: profileCubit.isConfirm == true
              ? 'profile.theNext'.tr()
              : 'profile.update'.tr(),
          width: double.infinity,
          elevation: 0,
          onPressed: () {
            if (profileCubit.isConfirm) {
              if (profileCubit.formkey.currentState?.validate() ?? false) {
                profileCubit.changeToggleFunction();
                // pushTOLogin(context);
              }
            } else {
              if (profileCubit.formkey.currentState?.validate() ?? false) {
                profileCubit.updatePassword();
              }
            }
          },
        ),
      ),
    );
  }
}
