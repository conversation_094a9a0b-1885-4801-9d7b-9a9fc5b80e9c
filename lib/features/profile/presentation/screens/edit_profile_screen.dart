import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_helper.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_keys.dart';
import 'package:tegra_ecommerce_app/core/helper_functions/flutter_toast.dart';
import 'package:tegra_ecommerce_app/core/helper_functions/validation.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/main%20layout/business_logic/main_layout_cubit.dart';
import 'package:tegra_ecommerce_app/features/main%20layout/main_layout.dart';
import 'package:tegra_ecommerce_app/features/profile/business_logic/profile_cubit.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/widgets/edit%20profile%20widgets/edit_profile_app_bar_widget.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/widgets/edit%20profile%20widgets/edit_profile_image_widget.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/widgets/push_fucntion.dart';

import '../../../../core/utils/easy_loading.dart';

class EditProfileScreen extends StatelessWidget {
  const EditProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    hideLoading();
    ProfileCubit profileCubit = BlocProvider.of(context);
    profileCubit.pickedImage = null;
    profileCubit.nameController.text =
        profileCubit.profileDataModel!.profileData!.name ?? '';
    profileCubit.phoneNumberController.text =
        profileCubit.profileDataModel!.profileData!.phone ?? '';
    profileCubit.cityController.text =
        profileCubit.profileDataModel!.profileData!.city ?? "";
    profileCubit.emailController.text =
        profileCubit.profileDataModel!.profileData!.email ?? '';
    return Scaffold(
      backgroundColor: AppColors.neutralColor100,
      body: Column(
        children: [
          EditProfileAppBarWidget(),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 30.sp),
              child: Form(
                key: profileCubit.formkey,
                child: Column(
                  spacing: 16.sp,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    EditProfileImageWidget(),
                    Text(
                      CacheHelper.getData(key: CacheKeys.userName),
                      style: Styles.contentEmphasis.copyWith(
                          fontSize: 18.sp, color: AppColors.neutralColor1200),
                    ),
                    CustomTextFormFieldWidget(
                      controller: profileCubit.nameController,
                      labelText: "profile.fullName".tr(),
                      validator: (value) {
                        return AppValidator.validateUsername(value!);
                      },
                    ),
                    CustomTextFormFieldWidget(
                      controller: profileCubit.phoneNumberController,
                      keyboardType: TextInputType.phone,
                      labelText: "profile.phoneNumber".tr(),
                      validator: (value) {
                        return AppValidator.validateEmptyText(
                            "profile.phoneNumber", value!);
                      },
                    ),
                    profileCubit.emailController.text.isNotEmpty
                        ? CustomTextFormFieldWidget(
                            controller: profileCubit.emailController,
                            keyboardType: TextInputType.emailAddress,
                            labelText: "profile.email".tr(),
                            validator: (value) {
                              return AppValidator.validateEmail(value);
                            },
                          )
                        : SizedBox.shrink(),
                    CustomTextFormFieldWidget(
                      controller: profileCubit.cityController,
                      keyboardType: TextInputType.text,
                      labelText: "profile.city".tr(),
                      validator: (value) {
                        return AppValidator.validateEmptyText(
                            "profile.phoneNumber", value!);
                      },
                    )
                    // : SizedBox.shrink(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.only(
          bottom: 51.sp,
          left: 16.sp,
          right: 16.sp,
        ),
        child: BlocListener<ProfileCubit, ProfileState>(
          listenWhen: (previous, current) =>
              current is UpdateProfileErrorState ||
              current is UpdateProfileLoadingState ||
              current is UpdateProfileSuccessState,
          listener: (context, state) {
            if (state is UpdateProfileSuccessState) {
              // context.read<ProfileCubit>().getProfileData();
              PersistentNavBarExtensions.pushAndRemoveUntilScreen(
                context,
                screen: BlocProvider(
                  create: (context) => MainLayoutCubit(),
                  child: const MainLayoutScreen(),
                ),
                withNavBar: false,
              );
            }
          },
          child: CustomButtonWidget(
            text: 'profile.update'.tr(),
            onPressed: () {
              if (profileCubit.formkey.currentState!.validate()) {
                if (profileCubit.emailController.text ==
                        profileCubit.profileDataModel!.profileData!.email &&
                    profileCubit.phoneNumberController.text ==
                        profileCubit.profileDataModel!.profileData!.phone &&
                    profileCubit.nameController.text ==
                        profileCubit.profileDataModel!.profileData!.name &&
                    profileCubit.cityController.text ==
                        profileCubit.profileDataModel!.profileData!.city) {
                  customToast(
                      msg: 'profile.noChange'.tr(),
                      color: AppColors.greenColor200);
                } else {
                  profileCubit.updateProfile();
                }
              }
            },
          ),
        ),
      ),
    );
  }
}
