import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page_transition/page_transition.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/main_app_bar_widget.dart';
import 'package:tegra_ecommerce_app/features/address/business_logic/cubit/address_cubit.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/screens/all_address_screen.dart';
import 'package:tegra_ecommerce_app/features/favorite/bloc/cubit/favorite_cubit.dart';
import 'package:tegra_ecommerce_app/features/favorite/persenation/favorite_screen.dart';
import 'package:tegra_ecommerce_app/features/localization/presentation/localization_screen.dart';
import 'package:tegra_ecommerce_app/features/my_orders/business_logic/my_orders_cubit.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/screens/my_orders_screen.dart';
import 'package:tegra_ecommerce_app/features/profile/business_logic/profile_cubit.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/screens/about_us_screen.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/screens/change_password_screen.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/screens/edit_profile_screen.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/screens/vision_screen.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/widgets/profile_item_widget.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/widgets/push_fucntion.dart';
import 'package:tegra_ecommerce_app/features/support/business_logic/support_cubit.dart';
import 'package:tegra_ecommerce_app/features/support/presentation/screens/support_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    ProfileCubit profileCubit = BlocProvider.of<ProfileCubit>(context);
    return BlocBuilder<ProfileCubit, ProfileState>(
      buildWhen: (previous, current) =>
          current is GetProfileDataError ||
          current is GetProfileDataLoading ||
          current is GetProfileDataSuccess,
      builder: (BuildContext context, state) {
        return Scaffold(
          backgroundColor: AppColors.neutralColor100,
          body: Column(
            children: [
              MainAppBarWidget(),
              Expanded(
                child: Skeletonizer(
                  enabled: state is GetProfileDataLoading,
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.only(
                        left: 18.w,
                        right: 18.w,
                        top: 30.h,
                        bottom: 24.h,
                      ),
                      child: Column(
                        children: [
                          ProfileIconWidget(
                            onPressed: () =>
                                PersistentNavBarNavigator.pushNewScreen(
                              context,
                              screen: BlocProvider(
                                create: (context) => MyOrdersCubit(getIt()),
                                child: MyOrdersScreen(),
                              ),
                            ),
                            // context.pushNamed(Routes.myOrdersScreen),
                            label: 'profile.myOrders'.tr(),
                            image: 'assets/images/svgs/my_orders_icon.svg',
                          ),
                          ProfileIconWidget(
                            onPressed: () {
                              Navigator.of(context).push(
                                PageTransition(
                                  type: PageTransitionType.fade,
                                  child: BlocProvider.value(
                                    value: profileCubit,
                                    child: EditProfileScreen(),
                                  ),
                                ),
                              );
                            },
                            label: 'profile.editProfile'.tr(),
                            image: Assets.assetsImagesSvgsEditProfileIcon,
                          ),
                          ProfileIconWidget(
                            onPressed: () =>
                                PersistentNavBarNavigator.pushNewScreen(
                              context,
                              screen: BlocProvider(
                                create: (context) => ProfileCubit(getIt()),
                                child: ChangePasswordScreen(),
                              ),
                            ),
                            // context.pushNamed(Routes.changePasswordScreen),
                            label: 'profile.changePassword'.tr(),
                            image: Assets.assetsImagesSvgsChangePassword,
                          ),
                          ProfileIconWidget(
                            onPressed: () {
                              // context.pushNamed(Routes.favoriteScreen);
                              PersistentNavBarNavigator.pushNewScreen(
                                context,
                                screen: BlocProvider(
                                  create: (context) =>
                                      FavoriteCubit(getIt())..getAllFavorite(),
                                  child: FavoriteScreen(),
                                ),
                                withNavBar: true,
                                pageTransitionAnimation:
                                    PageTransitionAnimation.fade,
                              );
                            },
                            label: 'profile.favorite'.tr(),
                            image:
                                'assets/images/svgs/favorite_icon_in_profile.svg',
                          ),
                          ProfileIconWidget(
                            onPressed: () {
                              PersistentNavBarNavigator.pushNewScreen(
                                context,
                                screen: BlocProvider(
                                  create: (context) =>
                                      AddressCubit(getIt())..getAllAddresses(),
                                  child: AllAddressScreen(),
                                ),
                                withNavBar: true,
                                pageTransitionAnimation:
                                    PageTransitionAnimation.fade,
                              );
                            },
                            label: 'profile.addresses'.tr(),
                            image: 'assets/images/svgs/address_icon.svg',
                          ),
                          ProfileIconWidget(
                            onPressed: () {
                              showLocalizationBottomSheet(context);
                            },
                            label: 'profile.language'.tr(),
                            subTitle: context.locale.toString() == 'ar_EG'
                                ? "العربية"
                                : "English",
                            image: Assets.assetsImagesSvgsLaguageIcon,
                          ),
                          ProfileIconWidget(
                            onPressed: () {
                              PersistentNavBarNavigator.pushNewScreen(
                                context,
                                screen: BlocProvider(
                                  create: (context) =>
                                      SupportCubit(getIt())..getAllTickets(),
                                  child: SupportScreen(),
                                ),
                              );
                            },
                            label: 'profile.support'.tr(),
                            image: Assets.assetsImagesSvgsHelpIcon,
                          ),
                          ProfileIconWidget(
                            onPressed: () {
                              PersistentNavBarNavigator.pushNewScreen(
                                context,
                                screen: BlocProvider(
                                  create: (context) => ProfileCubit(getIt())
                                    ..getSettingsData(title: 'vision'),
                                  child: VisionScreen(),
                                ),
                              );
                            },
                            label: 'profile.vision'.tr(),
                            image: Assets.assetsImagesSvgsTermsAndCondation,
                          ),
                          ProfileIconWidget(
                            onPressed: () {
                              PersistentNavBarNavigator.pushNewScreen(
                                context,
                                screen: BlocProvider(
                                  create: (context) => ProfileCubit(getIt())
                                    ..getSettingsData(title: 'about_us'),
                                  child: AboutUsScreen(),
                                ),
                              );
                              // context.pushNamed(Routes.aboutUsScreen);
                            },
                            label: 'profile.aboutUs'.tr(),
                            image: Assets.assetsImagesSvgsUsagePolicyIcon,
                          ),
                          BlocListener<ProfileCubit, ProfileState>(
                            listener: (context, state) {
                              if (state is LogoutSuccess) {
                              } else if (state is LogoutError) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'profile.logouterror'.tr(),
                                    ),
                                  ),
                                );
                              }
                            },
                            child: ProfileIconWidget(
                              onPressed: () {
                                AppConstants.showDialogForlogout(
                                  context,
                                  () {
                                    profileCubit.logout().then((value) {
                                      print("this");
                                      pushTOLogin(context);
                                      // PersistentNavBarNavigator.pushNewScreen(
                                      //   context,
                                      //   screen: BlocProvider(
                                      //     create: (context) => AuthCubit(getIt()),
                                      //     child: LoginScreen(),
                                      //   ),
                                      // );
                                    });
                                  },
                                );
                              },
                              isLastItem: true,
                              isRedColor: true,
                              label: 'profile.logout'.tr(),
                              image: Assets.assetsImagesSvgsLogoutIcon,
                            ),
                          ),
                          // ProfileIconWidget(
                          //   onPressed: () {
                          //     AppConstants.showDialogForRemoveAccount(
                          //       context,
                          //       () {
                          //         AppConstants.userToken = null;
                          //         CacheHelper.clearAllSecuredData();
                          //         context.pop();
                          //       },
                          //     );
                          //   },
                          //   isRedColor: true,
                          //   isLastItem: true,
                          //   label: 'profile.deleteAccount'.tr(),
                          //   image: Assets.assetsImagesSvgsDeleteAccountIcon,
                          // ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
