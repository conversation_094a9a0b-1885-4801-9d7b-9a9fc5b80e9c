// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
// import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
// import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
// import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
// import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
// import 'package:tegra_ecommerce_app/features/profile/business_logic/profile_cubit.dart';

// class VisionScreen extends StatelessWidget {
//   const VisionScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.white,
//       body: BlocBuilder<ProfileCubit, ProfileState>(
//         builder: (context, state) {
//           final profileCubit = context.read<ProfileCubit>();

//           return Column(
//             children: [
//               AppBarWidget(
//                 rowWidget: Row(
//                   spacing: 16.w,
//                   children: [
//                     BackButtonWidget(onTap: () => context.pop()),
//                     Text(
//                       profileCubit.settingsModel?.settingsData?[0].title ?? '',
// 'termsOfUse.termsofuse'.tr(),
//                       style: Styles.heading2.copyWith(
//                         color: AppColors.scaffoldBackground,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               30.verticalSpace,
//               Padding(
//                 padding: EdgeInsets.symmetric(horizontal: 16.w),
//                 child: Row(
//                   children: [
//                     Expanded(
//                       child: Text(
//                         profileCubit.settingsModel?.settingsData?[0].text ?? '',
// 'termsOfUse.termsText'.tr(),
//                         style: Styles.highlightBold
//                             .copyWith(color: AppColors.neutralColor1200),
//                       ),
//                     )
//                   ],
//                 ),
//               ),
//             ],
//           );
//         },
//       ),
//     );
//   }
// }
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/features/profile/business_logic/profile_cubit.dart';

class VisionScreen extends StatelessWidget {
  const VisionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocBuilder<ProfileCubit, ProfileState>(
        builder: (context, state) {
          final profileCubit = context.read<ProfileCubit>();
          final settingsData = profileCubit.settingsModel?.settingsData;

          final String title = settingsData?.isNotEmpty == true
              ? settingsData![0].title ?? ''
              : '';

          final String text = settingsData?.isNotEmpty == true
              ? profileCubit.removeHtmlTags(settingsData![0].text ?? '')
              : '';

          return Column(
            children: [
              AppBarWidget(
                rowWidget: Row(
                  spacing: 16.w,
                  children: [
                    BackButtonWidget(onTap: () => context.pop()),
                    Text(
                      title,
                      style: Styles.heading2.copyWith(
                        color: AppColors.scaffoldBackground,
                      ),
                    ),
                  ],
                ),
              ),
              30.verticalSpace,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        text,
                        style: Styles.highlightBold.copyWith(
                          color: AppColors.neutralColor1200,
                        ),
                        textAlign: TextAlign.start,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
