import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';

class DialogForLogoutWidget extends StatelessWidget {
  const DialogForLogoutWidget({
    super.key,
    required this.title,
    required this.description,
    required this.buttonText,
    required this.onButtonTap,
  });

  final String title;
  final String description;
  final String buttonText;
  final VoidCallback onButtonTap;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppColors.scaffoldBackground,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius + 4),
      ),
      child: Container(
        width: double.infinity,
        //height: 298.h,
        padding: EdgeInsets.all(20.sp),
        decoration: BoxDecoration(
          color: AppColors.scaffoldBackground,
          borderRadius: BorderRadius.circular((AppConstants.borderRadius + 4)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 20.verticalSpace,
            InkWell(
              onTap: () {
                context.pop();
              },
              child: Container(
                width: 50.w,
                height: 50.h,
                padding: EdgeInsets.all(12.sp),
                decoration: BoxDecoration(
                  color: AppColors.neutralColor100,
                  borderRadius: BorderRadius.circular(
                    AppConstants.borderRadius,
                  ),
                ),
                child: SvgPicture.asset(
                  width: 24,
                  height: 24,
                  Assets.assetsImagesSvgsCloseIcon,
                ),
              ),
            ),
            20.verticalSpace,
            Text(
              title,
              style: Styles.heading3.copyWith(color: AppColors.redColor100),
            ),
            8.verticalSpace,
            Text(
              description,
              textAlign: TextAlign.center,
              style: Styles.contentRegular
                  .copyWith(color: AppColors.neutralColor1000),
            ),
            20.verticalSpace,
            Row(
              children: [
                Expanded(
                  child: CustomButtonWidget(
                    color: AppColors.primaryColor900,
                    height: 55.h,
                    text: 'profile.cancellation'.tr(),
                    onPressed: () => context.pop(),
                  ),
                ),
                10.horizontalSpace,
                Expanded(
                  child: CustomButtonWidget(
                    elevation: 0,
                    borderSide: BorderSide(color: AppColors.primaryColor900),
                    color: AppColors.scaffoldBackground,
                    height: 55.h,
                    text: buttonText,
                    textColor: AppColors.primaryColor900,
                    onPressed: onButtonTap,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
