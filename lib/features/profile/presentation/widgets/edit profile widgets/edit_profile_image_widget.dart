import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/features/profile/business_logic/profile_cubit.dart';

class EditProfileImageWidget extends StatelessWidget {
  const EditProfileImageWidget({super.key});

  @override
  Widget build(BuildContext context) {
    ProfileCubit profileCubit = BlocProvider.of(context);
    return BlocBuilder<ProfileCubit, ProfileState>(
      buildWhen: (previous, current) => current is PickImageSuccessState,
      builder: (context, state) {
        return Stack(
          alignment: Alignment.bottomRight,
          children: [
            Container(
              clipBehavior: Clip.antiAliasWithSaveLayer,
              height: 80.sp,
              width: 80.sp,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  width: 1.sp,
                  color: AppColors.secondaryColor100,
                ),
              ),
              child: profileCubit.pickedImage != null
                  ? Image.file(
                      profileCubit.pickedImage!,
                      fit: BoxFit.fill,
                      errorBuilder: (context, error, stackTrace) {
                        return Image.asset(
                          Assets.assetsImagesPngsProfileImage, // Fallback image
                          fit: BoxFit.fill,
                        );
                      },
                    )
                  : profileCubit.profileDataModel!.profileData!.image == null
                      ? Image.asset(
                          Assets.assetsImagesPngsProfileImage,
                          height: 250.sp,
                          fit: BoxFit.fill,
                        )
                      : CachedNetworkImage(
                          imageUrl: profileCubit
                              .profileDataModel!.profileData!.image!,
                          errorWidget: (context, url, error) => Image.asset(
                            Assets
                                .assetsImagesPngsProfileImage, // Fallback image
                            fit: BoxFit.fill,
                          ),
                          height: 250.sp,
                          fit: BoxFit.cover,
                        ),
            ),
            InkWell(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      title: Text("profile.changeimage".tr()),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          TextButton(
                              onPressed: () {
                                profileCubit.pickImage(
                                    ImageSource.gallery, context);
                                Navigator.pop(context);
                              },
                              child: Text("profile.pickfromdevice".tr())),
                          TextButton(
                              onPressed: () {
                                profileCubit.pickImage(
                                    ImageSource.camera, context);
                                Navigator.pop(context);
                              },
                              child: Text("profile.pickfromCamera".tr())),
                        ],
                      ),
                    );
                  },
                );
              },
              child: Container(
                clipBehavior: Clip.antiAliasWithSaveLayer,
                padding: EdgeInsets.all(4.sp),
                width: 24.sp,
                height: 24.sp,
                decoration: BoxDecoration(
                    shape: BoxShape.circle, color: AppColors.primaryColor900),
                child: SvgPicture.asset(Assets.assetsImagesSvgsCameraIcon),
              ),
            ),
          ],
        );
      },
    );
  }
}
