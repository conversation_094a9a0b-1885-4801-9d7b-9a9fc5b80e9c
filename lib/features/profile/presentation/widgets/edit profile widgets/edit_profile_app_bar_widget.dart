import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';

class EditProfileAppBarWidget extends StatelessWidget {
  const EditProfileAppBarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBarWidget(
      rowWidget: Row(
        spacing: 16.w,
        children: [
          BackButtonWidget(onTap: () => context.pop()),
          Text(
            'profile.editProfile'.tr(),
            style: Styles.heading1
                .copyWith(color: AppColors.scaffoldBackground, fontSize: 24.sp),
          ),
        ],
      ),
    );
  }
}
