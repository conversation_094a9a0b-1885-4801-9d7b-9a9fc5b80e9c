import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/features/auth/business_logic/auth_cubit.dart';
import 'package:tegra_ecommerce_app/features/auth/presentation/screens/login_screen.dart';

void pushTOLogin(context) {
  // PersistentNavBarNavigator.pushNewScreen(
  //   screen: BlocProvider(
  //     create: (context) => AuthCubit(getIt()),
  //     child: const LoginScreen(),
  //   ),
  //   withNavBar: false,
  //   context,
  // );
  PersistentNavBarExtensions.pushAndRemoveUntilScreen(
    context,
    screen: BlocProvider(
      create: (context) => AuthCubit(getIt()),
      child: const LoginScreen(),
    ),
    withNavBar: false,
  );
}

class PersistentNavBarExtensions {
  PersistentNavBarExtensions._();

  /// Push a new screen and replace the current one (like pushReplacement)
  static Future<T?> pushReplacementScreen<T>(
    final BuildContext context, {
    required final Widget screen,
    bool? withNavBar,
    final PageTransitionAnimation pageTransitionAnimation =
        PageTransitionAnimation.cupertino,
    final PageRoute<T>? customPageRoute,
  }) {
    withNavBar ??= true;

    return Navigator.of(context, rootNavigator: !withNavBar)
        .pushReplacement<T, T>(
      customPageRoute ??
          (_getPageRoute(
            pageTransitionAnimation,
            enterPage: screen,
          ) as PageRoute<T>),
    );
  }

  /// Push a screen with route settings and replace the current one
  static Future<T?> pushReplacementScreenWithRouteSettings<T>(
    final BuildContext context, {
    required final Widget screen,
    required final RouteSettings settings,
    bool? withNavBar,
    final PageTransitionAnimation pageTransitionAnimation =
        PageTransitionAnimation.cupertino,
    final PageRoute<T>? customPageRoute,
  }) {
    withNavBar ??= true;

    return Navigator.of(context, rootNavigator: !withNavBar)
        .pushReplacement<T, T>(
      customPageRoute ??
          (_getPageRoute(
            pageTransitionAnimation,
            enterPage: screen,
            settings: settings,
          ) as PageRoute<T>),
    );
  }

  /// Push a screen and remove all previous routes (like pushAndRemoveUntil)
  static Future<T?> pushAndRemoveUntilScreen<T>(
    final BuildContext context, {
    required final Widget screen,
    bool? withNavBar,
    final PageTransitionAnimation pageTransitionAnimation =
        PageTransitionAnimation.cupertino,
    final PageRoute<T>? customPageRoute,
  }) {
    withNavBar ??= true;

    return Navigator.of(context, rootNavigator: !withNavBar)
        .pushAndRemoveUntil<T>(
      customPageRoute ??
          (_getPageRoute(
            pageTransitionAnimation,
            enterPage: screen,
          ) as PageRoute<T>),
      (route) => false,
    );
  }

  // Private helper to build transitions
  static PageRoute _getPageRoute(
    final PageTransitionAnimation transitionAnimation, {
    required final Widget enterPage,
    RouteSettings? settings,
  }) {
    switch (transitionAnimation) {
      case PageTransitionAnimation.fade:
        return PageRouteBuilder(
          settings: settings,
          pageBuilder: (_, __, ___) => enterPage,
          transitionsBuilder: (_, animation, __, child) => FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
      case PageTransitionAnimation.slideUp:
        return PageRouteBuilder(
          settings: settings,
          pageBuilder: (_, __, ___) => enterPage,
          transitionsBuilder: (_, animation, __, child) => SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.0, 1.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          ),
        );
      case PageTransitionAnimation.slideRight:
        return PageRouteBuilder(
          settings: settings,
          pageBuilder: (_, __, ___) => enterPage,
          transitionsBuilder: (_, animation, __, child) => SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(-1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          ),
        );
      case PageTransitionAnimation.cupertino:
      default:
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => enterPage,
        );
    }
  }
}
