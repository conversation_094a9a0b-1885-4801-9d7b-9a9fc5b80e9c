import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';

class ProfileIconWidget extends StatelessWidget {
  final String image;
  final String label;
  final bool isLastItem;
  final String? subTitle;
  final bool isRedColor;
  final VoidCallback onPressed;

  const ProfileIconWidget({
    super.key,
    required this.image,
    required this.label,
    this.subTitle,
    required this.onPressed,
    this.isLastItem = false,
    this.isRedColor = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: onPressed,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                  // width: 48.sp,
                  // height: 48.sp,
                  padding:
                      EdgeInsets.symmetric(vertical: 14.sp, horizontal: 13.sp),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    color: isRedColor
                        ? Color.fromRGBO(251, 55, 72, 0.1)
                        : Color.fromRGBO(241, 198, 23, 0.1),
                  ),
                  child: SvgPicture.asset(image, fit: BoxFit.scaleDown)),
              SizedBox(
                width: 12.w,
              ),
              Text(
                label,
                style: Styles.contentEmphasis.copyWith(
                  color: AppColors.neutralColor1200,
                  fontWeight: FontWeight.w500,
                  fontSize: 16.sp,
                ),
              ),
              const Spacer(),
              if (subTitle != null)
                Text(
                  subTitle!,
                  style: Styles.contentEmphasis.copyWith(
                      color: Color(0xff92929D),
                      fontWeight: FontWeight.w500,
                      fontSize: 16.sp),
                ),
              15.horizontalSpace,
              Icon(
                Icons.arrow_forward_ios,
                size: 20.sp,
                color: isRedColor
                    ? Color.fromRGBO(251, 55, 72, 1)
                    : Color.fromRGBO(241, 198, 23, 1),
              )
            ],
          ),
        ),
        if (!isLastItem) ...[
          16.verticalSpace,
          Container(
            height: 1.sp,
            color: AppColors.dividerColor,
          ),
          16.verticalSpace,
        ],
      ],
    );
  }
}
