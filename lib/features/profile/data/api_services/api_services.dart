import 'dart:io';

import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class ProfileApiServices {
  ProfileApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Logout
  Future<Response?> logout() async {
    return _dioFactory.post(endPoint: EndPoints.logout);
  }

  /// get profile data
  Future<Response?> getProfileData() async {
    return _dioFactory.get(endPoint: EndPoints.getProfileDetails);
  }

  /// update profile data
  Future<Response?> updateProfileData({
    required String name,
    String? email,
    required String phone,
    File? image,
    required String city,
  }) async {
    Map<String, dynamic> formDataMap = {
      'name': name,
      'email': email,
      'phone': phone,
      'image': image,
      'city': city,
    };
    // Remove null or empty values
    formDataMap
        .removeWhere((key, value) => value == null || value.toString().isEmpty);

    if (image != null) {
      String userImage = image.path.split('/').last;
      formDataMap['image'] =
          await MultipartFile.fromFile(image.path, filename: userImage);
    }
    FormData formData = FormData.fromMap(formDataMap);
    return _dioFactory.post(endPoint: EndPoints.updateProfile, data: formData);
  }

  /// Update password
  Future<Response?> updatePassword({
    required String oldPassword,
    required String newPassword,
    required String newRePassword,
  }) async {
    Map<String, dynamic> formDataMap = {
      'old_password': oldPassword,
      'new_password': newPassword,
      'new_password_confirmation': newRePassword,
    };

    FormData formData = FormData.fromMap(formDataMap);
    return _dioFactory.post(endPoint: EndPoints.updatePassword, data: formData);
  }

  /// Settings data
  Future<Response?> getSettingsData(String title) async {
    return _dioFactory.get(
      endPoint: EndPoints.getSettings(title),
    );
  }
}
