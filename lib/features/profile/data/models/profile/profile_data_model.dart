import 'package:json_annotation/json_annotation.dart';

part 'profile_data_model.g.dart';

@JsonSerializable()
class ProfileDataModel {
  @JsonKey(name: 'data')
  ProfileData? profileData;

  String? status;
  String? error;
  int? code;

  ProfileDataModel({this.profileData, this.status, this.error, this.code});

  factory ProfileDataModel.fromJson(Map<String, dynamic> json) =>
      _$ProfileDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProfileDataModelToJson(this);
}

@JsonSerializable()
class ProfileData {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? image;
  String? city;
  String? token;

  ProfileData({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.image,
    this.city,
    this.token,
  });

  factory ProfileData.fromJson(Map<String, dynamic> json) =>
      _$ProfileDataFromJson(json);

  Map<String, dynamic> toJson() => _$ProfileDataToJson(this);
}