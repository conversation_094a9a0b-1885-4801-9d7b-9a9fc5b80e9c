// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SettingsModel _$SettingsModelFromJson(Map<String, dynamic> json) =>
    SettingsModel(
      settingsData: (json['data'] as List<dynamic>?)
          ?.map((e) => SettingsData.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] as String?,
      error: json['error'] as String?,
      code: (json['code'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SettingsModelToJson(SettingsModel instance) =>
    <String, dynamic>{
      'data': instance.settingsData,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

SettingsData _$SettingsDataFromJson(Map<String, dynamic> json) => SettingsData(
      name: json['name'] as String?,
      title: json['title'] as String?,
      text: json['text'] as String?,
      image: json['image'] as String?,
    );

Map<String, dynamic> _$SettingsDataToJson(SettingsData instance) =>
    <String, dynamic>{
      'name': instance.name,
      'title': instance.title,
      'text': instance.text,
      'image': instance.image,
    };
