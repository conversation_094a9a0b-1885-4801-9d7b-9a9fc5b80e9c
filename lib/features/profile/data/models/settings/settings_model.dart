import 'package:json_annotation/json_annotation.dart';

part 'settings_model.g.dart';

@JsonSerializable()
class SettingsModel {
  @JsonKey(name: 'data')
  List<SettingsData>? settingsData;

  String? status;
  String? error;
  int? code;

  SettingsModel({this.settingsData, this.status, this.error, this.code});

  factory SettingsModel.fromJson(Map<String, dynamic> json) =>
      _$SettingsModelFromJson(json);

  Map<String, dynamic> toJson() => _$SettingsModelToJson(this);
}

@JsonSerializable()
class SettingsData {
  String? name;
  String? title;
  String? text;
  String? image;

  SettingsData({this.name, this.title, this.text, this.image});

  factory SettingsData.fromJson(Map<String, dynamic> json) =>
      _$SettingsDataFromJson(json);

  Map<String, dynamic> toJson() => _$SettingsDataToJson(this);
}
