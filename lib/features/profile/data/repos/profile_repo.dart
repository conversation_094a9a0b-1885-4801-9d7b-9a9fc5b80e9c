import 'dart:io';

import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_helper.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_keys.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/profile/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/profile/data/models/profile/profile_data_model.dart';
import 'package:tegra_ecommerce_app/features/profile/data/models/settings/settings_model.dart';

class ProfileRepository {
  final ProfileApiServices profileApiServices;

  ProfileRepository(this.profileApiServices);

  Future<ApiResult<String>> logout() async {
    final response = await profileApiServices.logout();

    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        AppConstants.userToken = null;
        await CacheHelper.clearAllSecuredData();

        // showCustomSnackbar(
        //   msg:
        //       'Verification code is ${response.data['data']['verification_code']}',
        //   color: Colors.green,
        //   time: 20,
        //   context: AppConstants.navigatorKey.currentContext!,
        // );

        return ApiResult.success('Logout Success');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }

      return ApiResult.failure(
        ServerException.fromResponse(e.response!.statusCode, e.response!),
      );
    }
  }

  Future<ApiResult<ProfileDataModel>> getProfileData() async {
    final response = await profileApiServices.getProfileData();
    try {
      if (response!.statusCode == 200) {
        ProfileDataModel profiledataModel =
            ProfileDataModel.fromJson(response.data);
        await CacheHelper.saveData(
            key: CacheKeys.userImage,
            value: profiledataModel.profileData!.image ?? '');
        await CacheHelper.saveData(
            key: CacheKeys.userName,
            value: profiledataModel.profileData!.name ?? '');

        return ApiResult.success(profiledataModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  Future<ApiResult<String>> updateProfile({
    required String name,
    String? email,
    required String phone,
    File? image,
    required String city,
  }) async {
    final response = await profileApiServices.updateProfileData(
      name: name,
      email: email,
      phone: phone,
      image: image,
      city: city,
    );

    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success('Update Success');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  Future<ApiResult<String>> updatePassword({
    required String oldPassword,
    required String newPassword,
    required String newRePassword,
  }) async {
    final response = await profileApiServices.updatePassword(
      oldPassword: oldPassword,
      newPassword: newPassword,
      newRePassword: newRePassword,
    );

    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success('Update Success');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Setting data
  Future<ApiResult<SettingsModel>> getSettingsData({
    required String title,
  }) async {
    final response = await profileApiServices.getSettingsData(title);

    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        SettingsModel settingsModel = SettingsModel.fromJson(response.data);

        return ApiResult.success(settingsModel);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
