part of 'profile_cubit.dart';

abstract class ProfileState {}

final class ProfileInitial extends ProfileState {}



final class PickImageSuccessState extends ProfileState {}

final class PickImageErrorState extends ProfileState {}

final class UpdateImageSuccess extends ProfileState {}

/// Toggle Password States
final class TogglePasswordState extends ProfileState {}

final class TogglePasswordState2 extends ProfileState {}

final class TogglePasswordState3 extends ProfileState {}

final class IsConfirmGoTo extends ProfileState {}

/// Get profile data States
final class GetProfileDataLoading extends ProfileState {}

final class GetProfileDataSuccess extends ProfileState {}

final class GetProfileDataError extends ProfileState {}

/// Logout States
final class LogoutLoading extends ProfileState {}

final class LogoutSuccess extends ProfileState {}

final class LogoutError extends ProfileState {}

/// update profile states
final class UpdateProfileSuccessState extends ProfileState {}

final class UpdateProfileErrorState extends ProfileState {}

final class UpdateProfileLoadingState extends ProfileState {}

/// Update Password States
final class UpdatePasswordLoadingState extends ProfileState {}

final class UpdatePasswordSuccessState extends ProfileState {}

final class UpdatePasswordErrorState extends ProfileState {}

/// Get Settings States
final class GetSettingsLoading extends ProfileState {}

final class GetSettingsSuccess extends ProfileState {}

final class GetSettingsError extends ProfileState {}