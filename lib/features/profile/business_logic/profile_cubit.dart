import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/features/address/data/model/address_model.dart';
import 'package:tegra_ecommerce_app/features/profile/data/models/profile/profile_data_model.dart';
import 'package:tegra_ecommerce_app/features/profile/data/models/settings/settings_model.dart';
import 'package:tegra_ecommerce_app/features/profile/data/repos/profile_repo.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit(this.profileRepository) : super(ProfileInitial());

  final ProfileRepository profileRepository;
  ProfileDataModel? profileDataModel;
  AddressesModel? addressesModel;
  SettingsModel? settingsModel;

  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final GlobalKey<FormState> formkey = GlobalKey<FormState>();

  File? pickedImage;

  Future pickImage(ImageSource source, context) async {
    try {
      final picker = ImagePicker();
      final image = await picker.pickImage(source: source);
      if (image == null) return;
      final imageTemp = File(image.path);

      pickedImage = imageTemp;

      emit(PickImageSuccessState());
    } catch (e) {
      emit(PickImageErrorState());
    }
  }

  final TextEditingController oldPasswordController = TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmNewPasswordController =
      TextEditingController();
  Timer? timer;
  bool isObscure = true;
  bool isObscure2 = true;
  bool isObscure3 = true;

  /// Stop Timer
  void stopTimer() {
    timer?.cancel();
  }

  /// Toggle Password
  void toggleObscure() {
    isObscure = !isObscure;
    emit(TogglePasswordState());
  }

  /// Toggle Password
  void toggleObscure2() {
    isObscure2 = !isObscure2;
    emit(TogglePasswordState2());
  }

  /// Toggle Password
  void toggleObscure3() {
    isObscure3 = !isObscure3;
    emit(TogglePasswordState3());
  }

  bool isConfirm = true;

  void changeToggleFunction() {
    isConfirm = !isConfirm;
    emit(IsConfirmGoTo());
  }

  /// Logout
  Future logout() async {
    showLoading();
    emit(LogoutLoading());
    final result = await profileRepository.logout();
    result.when(success: (success) {
      hideLoading();

      emit(LogoutSuccess());
    }, failure: (error) {
      hideLoading();
      emit(LogoutError());
    });
  }

  /// get profile data
  Future getProfileData() async {
    emit(GetProfileDataLoading());
    final result = await profileRepository.getProfileData();
    result.when(success: (data) {
      profileDataModel = data;
      emit(GetProfileDataSuccess());
    }, failure: (error) {
      emit(GetProfileDataError());
    });
  }

  /// update profile data
  Future updateProfile() async {
    showLoading();
    emit(UpdateProfileLoadingState());
    final result = await profileRepository.updateProfile(
      name: nameController.text,
      email: emailController.text,
      phone: phoneNumberController.text,
      image: pickedImage,
      city: cityController.text,
    );
    result.when(success: (success) async {
      hideLoading();
      await getProfileData();
      emit(UpdateProfileSuccessState());
    }, failure: (error) {
      hideLoading();
      emit(UpdateProfileErrorState());
    });
  }

  /// update password
  Future updatePassword() async {
    showLoading();
    emit(UpdatePasswordLoadingState());
    final result = await profileRepository.updatePassword(
      oldPassword: oldPasswordController.text,
      newPassword: newPasswordController.text,
      newRePassword: confirmNewPasswordController.text,
    );
    result.when(success: (success) {
      hideLoading();
      // context.pushNamedAndRemoveUntil(Routes.loginScreen);
      emit(UpdatePasswordSuccessState());
    }, failure: (error) {
      hideLoading();
      emit(UpdatePasswordErrorState());
    });
  }

  /// Settings data
  Future getSettingsData({required String title}) async {
    emit(GetSettingsLoading());
    final result = await profileRepository.getSettingsData(
      title: title,
    );
    result.when(success: (data) {
      settingsModel = data;
      emit(GetSettingsSuccess());
    }, failure: (error) {
      emit(GetSettingsError());
    });
  }

  String removeHtmlTags(String htmlText) {
    final regex = RegExp(r'<[^>]*>', multiLine: true, caseSensitive: false);
    return htmlText.replaceAll(regex, '').trim();
  }

  // Future<void> close() {
  //   nameController.dispose();
  //   emailController.dispose();
  //   phoneNumberController.dispose();
  //   cityController.dispose();
  //   stopTimer();
  //   return super.close();
  // }
  @override
  Future<void> close() {
    nameController.dispose();
    emailController.dispose();
    phoneNumberController.dispose();
    cityController.dispose();
    stopTimer();
    return super.close();
  }
}
