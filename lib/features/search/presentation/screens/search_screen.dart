import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/empty/empty_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/search/search_results_widget.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/screens/filter_screen.dart';

class SearchScreen extends StatelessWidget {
  const SearchScreen({super.key, this.isShowMore = false});
  final bool isShowMore;

  @override
  Widget build(BuildContext context) {
    final searchCubit = context.read<SearchCubit>();
    if (isShowMore) {
      Future.delayed(Duration(milliseconds: 100)).then((_) {
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: BlocProvider.value(
            value: searchCubit,
            child: FilterScreen(),
          ),
          withNavBar: true,
          // pageTransitionAnimation: PageTransitionAnimation.cupertino,
        );
      });
    }
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 10.sp,
              children: [
                Expanded(
                  child: CustomTextFormFieldWidget(
                    controller: searchCubit.searchController,
                    hintText: "${"search.search".tr()}...",
                    hintStyle: Styles.captionRegular.copyWith(
                      color: AppColors.neutralColor1200,
                    ),
                    onChanged: (value) => searchCubit.checkSearchIsEmpty(),
                  ),
                ),
                InkWell(
                  onTap: () => context.pop(),
                  child: Container(
                    padding: EdgeInsets.all(13.sp),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.circular(AppConstants.borderRadius),
                      color: Colors.white,
                    ),
                    child: SvgPicture.asset(
                      'assets/images/svgs/close_icon.svg',
                      fit: BoxFit.scaleDown,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: BlocBuilder<SearchCubit, SearchState>(
              buildWhen: (previous, current) =>
                  current is CheckIsSearchState ||
                  current is SearchProductsLoadingState ||
                  current is SearchProductsSuccessState ||
                  current is SearchProductsErrorState,
              builder: (context, state) {
                return searchCubit.searchCardProductModel == null ||
                        searchCubit
                            .searchCardProductModel!.data!.product!.isEmpty
                    ? SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(height: 20.h),
                            EmptyWidget(
                              imagePath: 'assets/images/svgs/emptySearch.svg',
                              title: 'search.didnotFindWhatWeWereLooking'.tr(),
                              description: 'search.tryUsingOtherWords'.tr(),
                            ),
                          ],
                        ),
                      )
                    : state is SearchProductsLoadingState
                        ? const Center(child: CircularProgressIndicator())
                        : SearchResultsWidget();
              },
            ),
          ),
        ],
      ),
    );
  }
}
