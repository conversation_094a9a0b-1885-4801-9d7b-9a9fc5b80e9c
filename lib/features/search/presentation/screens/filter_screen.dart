import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/utils/easy_loading.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/drop_down/custom_drop_down_widget.dart';
import 'package:tegra_ecommerce_app/features/brand/business_logic/brand_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand/data/models/brands_by_category/brands_by_category_model.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/filter/category_department_filter_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/filter/price_fom_and_to_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/filter/selected_category_widget.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';

class FilterScreen extends StatelessWidget {
  const FilterScreen({
    super.key,
    // this.isBrandFilter = false,
  });

  // final bool isBrandFilter;

  @override
  Widget build(BuildContext context) {
    SearchCubit searchCubit = context.read<SearchCubit>();
    return Scaffold(
      backgroundColor: Color(0xffffffff),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppBarWidget(
            rowWidget: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "filter.filterAndSelect".tr(),
                  style: Styles.heading1.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
                Row(
                  spacing: 16.sp,
                  children: [
                    Container(
                      padding: EdgeInsets.all(13.sp),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius:
                            BorderRadius.circular(AppConstants.borderRadius),
                        color: Colors.white,
                      ),
                      child: SvgPicture.asset(
                        Assets.assetsImagesSvgsDeleteIcon,
                        fit: BoxFit.scaleDown,
                      ),
                    ),
                    InkWell(
                      onTap: () => context.pop(),
                      child: Container(
                        padding: EdgeInsets.all(13.sp),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius:
                              BorderRadius.circular(AppConstants.borderRadius),
                          color: Colors.white,
                        ),
                        child: SvgPicture.asset(
                          Assets.assetsImagesSvgsCloseIcon,
                          fit: BoxFit.scaleDown,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // isBrandFilter
          // ?
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  32.verticalSpace,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 15.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "filter.price".tr(),
                          style: Styles.heading5.copyWith(color: Colors.black),
                        ),
                        12.verticalSpace,
                        Row(
                          spacing: 16.w,
                          children: [
                            PriceFromANdToWidget(
                              controller: searchCubit.priceFromController,
                              text: 'filter.who'.tr(),
                            ),
                            PriceFromANdToWidget(
                              controller: searchCubit.priceToController,
                              text: 'filter.to'.tr(),
                            ),
                          ],
                        ),
                        32.verticalSpace,
                        CategoryDepartmentFilterWidget(),
                        32.verticalSpace,
                        Text(
                          "filter.evaluation".tr(),
                          style: Styles.heading5.copyWith(
                            color: Colors.black,
                          ),
                        ),
                        12.verticalSpace,
                        BlocBuilder<SearchCubit, SearchState>(
                          buildWhen: (previous, current) =>
                              current is FilterRatingSelected,
                          builder: (context, state) {
                            return SelectedCategoryWidget(
                              title: "filter.sortByMostToLeastRated".tr(),
                              starImage: Assets.assetsImagesSvgsStarIcon,
                              onTap: () {
                                searchCubit.selectRating();
                              },
                              isSelected: searchCubit.isRatingSelected,
                              index: 0,
                              selectedIndex:
                                  searchCubit.isRatingSelected ? 0 : 1,
                            );
                          },
                        ),
                        32.verticalSpace,
                        Text(
                          "filter.theBrand".tr(),
                          style: Styles.heading5.copyWith(
                            color: Colors.black,
                          ),
                        ),
                        12.verticalSpace,
                        BlocProvider(
                          create: (context) =>
                              BrandCubit(getIt())..getAllBrands(),
                          child: BlocBuilder<BrandCubit, BrandState>(
                            builder: (context, state) {
                              if (state is GetAllBrandsLoading) {
                                return Skeletonizer(
                                  enabled: true,
                                  child: CustomDropdownButtonWidget<String>(
                                    hint: 'filter.allBrands'.tr(),
                                    items: ["PUMA", "Adidas", "Nike"],
                                    getItemText: (item) => item,
                                    // Use item name as text
                                    getItemIcon: (item) {
                                      // Assign logos based on brand
                                      switch (item) {
                                        case "PUMA":
                                          return Assets
                                              .assetsImagesPngsAdidasCover;
                                        case "Adidas":
                                          return Assets
                                              .assetsImagesPngsAdidasCover;
                                        case "Nike":
                                          return Assets
                                              .assetsImagesPngsAdidasCover;
                                        default:
                                          return "";
                                      }
                                    },
                                    onChanged: (selectedBrand) {},
                                  ),
                                );
                              } else {
                                return CustomDropdownButtonWidget<
                                    BrandDataItem>(
                                  hint: 'filter.allBrands'.tr(),
                                  isString: false,
                                  items: BlocProvider.of<BrandCubit>(context)
                                      .allBrandsByCategoryModel!
                                      .brandsData!
                                      .data!,
                                  getItemText: (item) =>
                                      item.title ?? "Unknown",
                                  getItemIcon: (item) => item.logo ?? "",
                                  onChanged: (selectedBrand) {
                                    searchCubit.brandId = selectedBrand!.id;
                                  },
                                );
                              }
                            },
                          ),
                        ),
                        32.verticalSpace,
                        // CategoryOfferFilterWidget(),
                        32.verticalSpace,
                        BlocListener<SearchCubit, SearchState>(
                          listener: (context, state) {
                            if (state is SearchProductsLoadingState) {
                              showLoading();
                            } else if (state is SearchProductsSuccessState) {
                              hideLoading();
                              context.pop();
                            } else {
                              hideLoading();
                            }
                          },
                          child: CustomButtonWidget(
                              text: "filter.filtering".tr(),
                              onPressed: () {
                                searchCubit.searchProduct();
                              }),
                        ),
                        32.verticalSpace,
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
