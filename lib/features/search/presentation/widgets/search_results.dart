import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/notification/presentation/widgets/day_history_widget.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/widgets/search_grid_view.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/widgets/search_list_view.dart';

class SearchResults extends StatelessWidget {
  const SearchResults({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final searchCubit = context.read<SearchCubit>();

    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          children: [
            SizedBox(height: 30.h),
            Row(
              spacing: 10.sp,
              children: [
                Text(
                  "search.searchResults".tr(),
                  style: Styles.highlightEmphasis.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                  ),
                ),
                Spacer(),
                BlocBuilder<SearchCubit, SearchState>(
                  buildWhen: (previous, current) =>
                      current is SearchModeToggleStateSearch,
                  builder: (context, state) {
                    if (searchCubit.isListSearch) {
                      return InkWell(
                        onTap: () {
                          searchCubit.toggleSearchMode();
                        },
                        child: SvgPicture.asset(
                          'assets/images/svgs/four_dots_icon.svg',
                        ),
                      );
                    } else {
                      return InkWell(
                        onTap: () {
                          searchCubit.toggleSearchMode();
                        },
                        child: SvgPicture.asset(
                          'assets/images/svgs/drawer_icon.svg',
                          // Assets.assetsImagesPngsIonList,
                        ),
                      );
                    }
                  },
                ),
                Image.asset(Assets.assetsImagesPngsSortIcon),
              ],
            ),
            SizedBox(height: 26.h),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    BlocBuilder<SearchCubit, SearchState>(
                      buildWhen: (previous, current) =>
                          current is SearchModeToggleStateSearch,
                      builder: (context, state) {
                        if (searchCubit.isListSearch) {
                          return SearchListView();
                        } else {
                          return SearchGridView();
                        }
                      },
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 25.sp),
                      child: DayHistoryWidget(
                          dayHistoryText: 'search.resultsAreOut'.tr()),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
