import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class SearchGridView extends StatelessWidget {
  const SearchGridView({super.key});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      physics: NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisExtent: 220.sp,
        crossAxisSpacing: 25.sp,
        mainAxisSpacing: 25.sp,
      ),
      itemCount: AppConstants.productGridViewImages.length,
      itemBuilder: (BuildContext context, int index) {
        return Container(
            // productImage: AppConstants.productGridViewImages[index],
            // productName: "Sar 250",
            // categoryName: 'تيشيرت',
            // productRate: "6.4",
            // productDescription: "تيشيرت بوما",
            // brandImage: Assets.assetsImagesPngsPumaIcon,
            // brandName: "PUMA",
            );
      },
    );
  }
}
