import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class SearchListView extends StatelessWidget {
  const SearchListView({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemCount: AppConstants.productListViewImages.length,
      shrinkWrap: true,
      separatorBuilder: (BuildContext context, int index) {
        return 25.verticalSpace;
      },
      itemBuilder: (BuildContext context, int index) {
        return Container(
            // return ProductListItemWidget(
            //   productImage: AppConstants.productListViewImages[index],
            //   productName: "SAR 250",
            //   productRate: "6.4",
            //   productDescription: "تيشيرت بوما",
            //   brandImage: Assets.assetsImagesPngsPumaIcon,
            //   brandName: "PUMA",
            );
      },
    );
  }
}
