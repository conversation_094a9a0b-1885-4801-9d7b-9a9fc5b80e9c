import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class CategoryItemInCategoryWidget extends StatelessWidget {
  const CategoryItemInCategoryWidget({
    super.key,
    this.imageUrl,
    required this.categoryName,
    required this.onTap,
  });

  final String? imageUrl;
  final String categoryName;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        spacing: 10.h,
        children: [
          Expanded(
            child: Container(
              // padding: EdgeInsets.all(7.37.sp),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(
                  AppConstants.borderRadius - 1,
                ),
              ),
              child: imageUrl == null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadius - 1,
                      ),
                      child: Image.asset(
                        'assets/images/pngs/clothes_icon.png',
                        // 'assets/images/svgs/classes_image.png',
                        height: 95.h,
                        width: 95.w,
                        // width: double.infinity,
                        fit: BoxFit.cover,
                      ),
                    )
                  : ClipRRect(
                      borderRadius: BorderRadius.circular(
                        AppConstants.borderRadius - 1,
                      ),
                      child: CachedNetworkImage(
                        imageUrl: imageUrl!,
                        height: 95.h,
                        width: 95.w,
                        fit: BoxFit.cover,
                        errorWidget: (context, url, error) => Center(
                          child: Icon(Icons.error),
                        ),
                      ),
                    ),
            ),
          ),
          SizedBox(
            width: 68.w,
            child: Text(
              categoryName,
              textAlign: TextAlign.center,
              style: Styles.highlightEmphasis.copyWith(
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          )
        ],
      ),
    );
  }
}
