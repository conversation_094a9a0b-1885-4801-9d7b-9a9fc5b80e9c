part of 'search_cubit.dart';

sealed class SearchState {}

final class SearchInitial extends SearchState {}

/// Check Is Search States
final class CheckIsSearchState extends SearchState {}

/// Search Mode Toggle States
final class SearchModeToggleStateSearch extends SearchState {}

/// Search Products
final class SearchProductsLoadingState extends SearchState {}

final class SearchProductsLoadingMoreState extends SearchState {}

final class SearchProductsSuccessState extends SearchState {}

final class SearchProductsErrorState extends SearchState {}

final class FilterRatingSelected extends SearchState {}

final class FilterCategorySelected extends SearchState {}

final class FilterOfferSelected extends SearchState {}

final class GetVartitonsLoadingState extends SearchState {}

final class GetVartitonsSuccessState extends SearchState {}

final class GetVartitonsErrorState extends SearchState {}
