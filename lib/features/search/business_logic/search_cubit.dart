import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/search/data/model/attribute_model.dart';
import 'package:tegra_ecommerce_app/features/search/data/repos/search_repo.dart';

part 'search_state.dart';

class SearchCubit extends Cubit<SearchState> {
  SearchCubit(this.searchRepository) : super(SearchInitial());
  final SearchRepository searchRepository;
  bool isListSearch = true;
  final ScrollController scrollController = ScrollController();
  bool isLoadingMore = false;
  int currentPage = 1;

  ProductCardPaginatedModel? searchCardProductModel;
  final searchController = TextEditingController();

  void checkSearchIsEmpty() async {
    if (searchController.text.length > 2) {
      emit(CheckIsSearchState());
      await searchProduct();
    } else if (searchController.text.isEmpty) {
      emit(CheckIsSearchState());
    }
  }

  void toggleSearchMode() {
    isListSearch = !isListSearch;
    emit(SearchModeToggleStateSearch());
  }

  final priceFromController = TextEditingController();
  final priceToController = TextEditingController();
  bool isRatingSelected = false;
  int categoryIndex = -1;
  int? categoryId;
  int offerSelectedIndex = -1;
  int? brandId;

  void selectRating() {
    isRatingSelected = !isRatingSelected;
    emit(FilterRatingSelected());
  }

  void selectCategory(int index, int categoryid) {
    categoryIndex = index;
    categoryId = categoryid;

    emit(FilterCategorySelected());
  }

  void selectOffer(int index) {
    offerSelectedIndex = index;
    emit(FilterOfferSelected());
  }

  AttributeModel? attributeModel;
  Future getVaritons() async {
    emit(GetVartitonsLoadingState());
    final result = await searchRepository.getVaritons();

    result.when(
      success: (data) {
        attributeModel = data;
        emit(GetVartitonsSuccessState());
      },
      failure: (errorHandler) {
        emit(GetVartitonsErrorState());
      },
    );
  }

  Future<void> searchProduct({
    Map<String, List<String>>? attributes,
  }) async {
    emit(SearchProductsLoadingState());

    final result = await searchRepository.searchProduct(
      search: searchController.text,
      brandId: brandId,
      categoryId: categoryId,
      isRating: isRatingSelected ? 1 : 0,
      minPrice: double.tryParse(priceFromController.text),
      maxPrice: double.tryParse(priceToController.text),
      attributes: attributes,
    );

    result.when(success: (data) {
      searchCardProductModel = data;
      if (data.data!.product!.isEmpty) {
        brandId = null;
        categoryId = null;
        priceFromController.clear();
        priceToController.clear();
      }

      emit(SearchProductsSuccessState());
    }, failure: (errorHandler) {
      emit(SearchProductsErrorState());
    });
  }

  Future<void> loadMoreSearchProducts({
    Map<String, List<String>>? attributes,
  }) async {
    if (isLoadingMore ||
        (searchCardProductModel?.data?.meta?.currentPage ?? 1) >=
            (searchCardProductModel?.data?.meta?.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(SearchProductsLoadingMoreState());

    final nextPage = currentPage + 1;
    final result = await searchRepository.searchProduct(
      search: searchController.text,
      brandId: brandId,
      categoryId: categoryId,
      minPrice: double.tryParse(priceFromController.text),
      maxPrice: double.tryParse(priceToController.text),
      attributes: attributes,
    );

    result.when(success: (data) {
      searchCardProductModel?.data?.product?.addAll(data.data?.product ?? []);
      currentPage = data.data?.meta?.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(SearchProductsSuccessState());
    }, failure: (error) {
      isLoadingMore = false;
      emit(SearchProductsErrorState());
    });
  }
}
