import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/models/product_card_model/product_card_paginated_model.dart';
import 'package:tegra_ecommerce_app/features/search/data/api_servicess/api_services.dart';
import 'package:tegra_ecommerce_app/features/search/data/model/attribute_model.dart';

class SearchRepository {
  final SearchApiServices searchRepository;

  SearchRepository(this.searchRepository);

  /// Search Product
  Future<ApiResult<ProductCardPaginatedModel>> searchProduct({
    String? search,
    int? brandId,
    int? categoryId,
    double? minPrice,
    double? maxPrice,
    int ? isRating ,
    Map<String, List<String>>? attributes,
  }) async {
    final response = await searchRepository.searchProducts(
      search: search,
      brandId: brandId,
      categoryId: categoryId,
      minPrice: minPrice,
      maxPrice: maxPrice,
      isRating: isRating,
      attributes: attributes,
    );

    try {
      if (response!.statusCode == 200) {
        ProductCardPaginatedModel productCardModel =
            ProductCardPaginatedModel.fromJson(response.data);
        return ApiResult.success(productCardModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  Future<ApiResult<AttributeModel>> getVaritons() async {
    final response = await searchRepository.getVaritons();

    try {
      if (response!.statusCode == 200) {
        AttributeModel productCardModel =
            AttributeModel.fromJson(response.data);
        return ApiResult.success(productCardModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
