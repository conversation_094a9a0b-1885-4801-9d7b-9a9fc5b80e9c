import 'package:json_annotation/json_annotation.dart';

part 'attribute_model.g.dart';

@JsonSerializable()
class AttributeModel {
  final List<Attribute> data;
  final String status;
  final String error;
  final int code;

  AttributeModel({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  // JSON serialization
  factory AttributeModel.fromJson(Map<String, dynamic> json) => _$AttributeModelFromJson(json);
  Map<String, dynamic> toJson() => _$AttributeModelToJson(this);
}

@JsonSerializable()
class Attribute {
  final int id;
  final String name;

  @Json<PERSON>ey(name: "values") // Ensuring the JSON key is correctly mapped
  final List<String> values;

  Attribute({
    required this.id,
    required this.name,
    required this.values,
  });

  // JSON serialization
  factory Attribute.fromJson(Map<String, dynamic> json) => _$AttributeFromJson(json);
  Map<String, dynamic> toJson() => _$AttributeToJson(this);
}
