import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class SearchApiServices {
  SearchApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Search Products
  Future<Response?> searchProducts({
    String? search,
    int? brandId,
    int? categoryId,
    double? minPrice,
    double? maxPrice,
    int? isRating,
    Map<String, List<String>>? attributes,
  }) async {
    final Map<String, dynamic> data = {};

    if (search != null && search.isNotEmpty) {
      data['search'] = search;
    }
    if (brandId != null) {
      data['brand_id'] = brandId;
    }
    if (categoryId != null) {
      data['category_id'] = categoryId;
    }
    if (minPrice != null) {
      data['min_price'] = minPrice;
    }
    if (maxPrice != null) {
      data['max_price'] = maxPrice;
    }
    if (attributes != null && attributes.isNotEmpty) {
      data['attributes'] = attributes;
    }
    data['sort'] = isRating;

    return _dioFactory.post(
      endPoint: EndPoints.searchforproduct,
      data: data,
    );
  }

  Future<Response?> getVaritons() async {
    return _dioFactory.get(
      endPoint: EndPoints.getVariationFilter,
    );
  }
}
