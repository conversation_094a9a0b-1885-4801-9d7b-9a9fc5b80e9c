import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';

import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';

import 'package:tegra_ecommerce_app/core/widgets/empty/empty_widget.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/widgets/custom_header_in_refresh_indicator_widget.dart';

import 'package:tegra_ecommerce_app/features/favorite/bloc/cubit/favorite_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_grid/product_grid_view_item_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/product_grid/product_grid_view_item_widget.dart';

class FavoriteScreen extends StatelessWidget {
  const FavoriteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<FavoriteCubit>();
    final RefreshController refreshController = RefreshController();

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 16.w,
              children: [
                BackButtonWidget(onTap: () => context.pop()),
                Text(
                  'favorite.favorite'.tr(),
                  style: Styles.heading2.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
              ],
            ),
          ),
          30.verticalSpace,
          Expanded(
            child: BlocBuilder<FavoriteCubit, FavoriteState>(
              buildWhen: (previous, current) =>
                  current is GetAllFavoriteErrorState ||
                  current is GetAllFavoriteSuccessState ||
                  current is GetAllFavoritesLoadingState ||
                  current is RemoveProductFromFavoriteSuccessState,
              builder: (context, state) {
                if (state is GetAllFavoritesLoadingState) {
                  return GridView.builder(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.sp,
                      // vertical: 16.sp,
                    ),
                    shrinkWrap: true,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisExtent: 220.sp,
                      crossAxisSpacing: 25.sp,
                      mainAxisSpacing: 25.sp,
                    ),
                    itemCount: 10,
                    itemBuilder: (BuildContext context, int index) {
                      return ProductGridViewItemSkeletonizerWidget();
                    },
                  );
                } else {
                  cubit.productModel == null ? cubit.getAllFavorite() : null;
                  return SmartRefresher(
                    controller: refreshController,
                    onRefresh: () async {
                      await cubit.getAllFavorite();
                      refreshController.refreshCompleted();
                    },
                    header: CustomHeaderInRefreshIndicatorWidget(),
                    child: cubit.productModel!.data.products.isNotEmpty
                        ? GridView.builder(
                            controller: cubit.scrollController,
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.sp,
                            ),
                            shrinkWrap: true,
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              mainAxisExtent: 220.sp,
                              crossAxisSpacing: 25.sp,
                              mainAxisSpacing: 25.sp,
                            ),
                            itemCount: cubit.productModel!.data.products.length,
                            itemBuilder: (BuildContext context, int index) {
                              return ProductGridViewItemWidget(
                                favoriteTap: () {
                                  cubit.removeFromFavorite(
                                      index: index,
                                      id: cubit.productModel!.data
                                          .products[index].id!);
                                },
                                productCard:
                                    cubit.productModel!.data.products[index],
                              );
                            },
                          )
                        : EmptyWidget(
                            imagePath: 'assets/images/svgs/emptyFavorite.svg',
                            title: 'favorite.favoritesEmpty'.tr(),
                            description: 'favorite.favoritesDesc'.tr(),
                          ),
                  );
                }
              },
            ),
          )
        ],
      ),
    );
  }
}
