import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/favorite/data/api_services/favorite_api_services.dart';
import 'package:tegra_ecommerce_app/features/favorite/data/model/favorite_model.dart';

class FavoriteRepository {
  final FavoriteApiServices _apiServices;

  FavoriteRepository(this._apiServices);

  Future<ApiResult<FavoriteResponse>> getFavorite({required int page}) async {
    try {
      final response = await _apiServices.getFavorite(page: page);
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success(FavoriteResponse.fromJson(response.data));
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
      return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'),
      );
    }
  }

  Future<ApiResult<bool>> addToFavorite({required int proudctId}) async {
    try {
      final response = await _apiServices.addToFavorite(proudctId: proudctId);
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return const ApiResult.success(true);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
      return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'),
      );
    }
  }
}
