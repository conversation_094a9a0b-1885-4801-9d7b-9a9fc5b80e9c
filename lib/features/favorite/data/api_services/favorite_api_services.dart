import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class FavoriteApiServices {
  FavoriteApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Login

  Future<Response?> addToFavorite({required int proudctId}) async {
    return _dioFactory.get(
      endPoint: "${EndPoints.addToFavorite}/$proudctId",
    );
  }

  Future<Response?> getFavorite({required int page }) async {
    return _dioFactory.get(endPoint: EndPoints.getFavorite, data: {
      "page": page
    });
  }
}
