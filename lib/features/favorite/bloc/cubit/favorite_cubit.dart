import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/features/favorite/data/model/favorite_model.dart';
import 'package:tegra_ecommerce_app/features/favorite/data/repo/favorite_repo.dart';

part 'favorite_state.dart';

class FavoriteCubit extends Cubit<FavoriteState> {
  FavoriteCubit(this._favoriteRepository) : super(FavoriteInitial());
  bool isFavorite = false;

  final FavoriteRepository _favoriteRepository;

  FavoriteResponse? productModel;
  int currentPage = 1;
  int? lastpage;
  Future<void> getAllFavorite() async {
    emit(GetAllFavoritesLoadingState());
    final result = await _favoriteRepository.getFavorite(page: currentPage);

    result.when(success: (products) {
      productModel = products;
      emit(GetAllFavoriteSuccessState());
    }, failure: (error) {
      emit(GetAllFavoriteErrorState());
    });
  }

  Future<void> addToFavorites({required int productId}) async {
    emit(AddProductToFavoriteSuccessState());
    final result =
        await _favoriteRepository.addToFavorite(proudctId: productId);

    result.when(
      success: (_) {
        isFavorite = !isFavorite;
        emit(AddProductToFavoriteSuccessState());
      },
      failure: (error) {
        emit(AddProductToFavoriteErrorState());
      },
    );
  }

  void removeFromFavorite({required int index, required int id}) async {
    productModel!.data.products.removeAt(index);
    emit(RemoveProductFromFavoriteSuccessState());
    await addToFavorites(productId: id);
  }

  final ScrollController scrollController = ScrollController();

  void intController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (state is LoadFavoriteLoadingState) {
          return;
        } else {
          loadMoreData();
        }
      }
    });
  }

  Future<void> loadMoreData() async {
    if (currentPage < lastpage!) {
      currentPage++;

      emit(LoadFavoriteLoadingState());

      final result = await _favoriteRepository.getFavorite(page: currentPage);

      result.when(success: (products) {
        productModel!.data.products.addAll(products.data.products);
        emit(GetAllFavoriteSuccessState());
      }, failure: (error) {
        emit(LoadFavoriteErrorState());
      });
    }
  }
}
