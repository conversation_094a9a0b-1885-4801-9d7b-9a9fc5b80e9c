part of 'favorite_cubit.dart';

abstract class FavoriteState {}

final class <PERSON><PERSON><PERSON>tial extends FavoriteState {}

/// Get All Favorites States
class GetAllFavoriteSuccessState extends FavoriteState {}

class GetAllFavoriteErrorState extends FavoriteState {}

class GetAllFavoritesLoadingState extends FavoriteState {}

/// Add Product To Favorites States
class AddProductToFavoriteSuccessState extends FavoriteState {}

class AddProductToFavoriteErrorState extends FavoriteState {}

class AddProductToFavoritesLoadingState extends FavoriteState {}

/// Remove Product From Favorite States
class RemoveProductFromFavoriteSuccessState extends FavoriteState {}

/// Load Favorite States
final class LoadFavoriteLoadingState extends FavoriteState {}

final class LoadFavoriteSuccessState extends FavoriteState {}

final class LoadFavoriteErrorState extends FavoriteState {}
