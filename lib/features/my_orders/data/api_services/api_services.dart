import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class MyOrdersApiServices {
  MyOrdersApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Get Delivered My Orders
  Future<Response?> showDeliveredOrders() async {
    return _dioFactory.get(
      endPoint: EndPoints.getDeliveredOrders,
    );
  }

  /// Get Progress My Orders
  Future<Response?> showProgressOrders() async {
    return _dioFactory.get(
      endPoint: EndPoints.getProgressOrders,
    );
  }

  /// Get Cancelled My Orders
  Future<Response?> showCancelledOrders() async {
    return _dioFactory.get(
      endPoint: EndPoints.getCanceledOrders,
    );
  }

   /// Get Show Order Details
  Future<Response?> showOrderDetails(int orderId) async {
    return _dioFactory.get(
      endPoint: EndPoints.showOrderDetails(orderId),
    );
  }
}