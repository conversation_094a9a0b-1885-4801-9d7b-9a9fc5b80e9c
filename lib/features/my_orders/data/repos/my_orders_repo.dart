import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/models/my_orders/my_orders_model.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/models/orders_datails/orders_details_data_model.dart';

class MyOrdersRepository {
  final MyOrdersApiServices myOrdersApiServices;

  MyOrdersRepository(this.myOrdersApiServices);

  /// Get Delivered My Orders
  Future<ApiResult<MyOrdersModel>> showDeliveredOrders() async {
    final response = await myOrdersApiServices.showDeliveredOrders();
    try {
      if (response!.statusCode == 200) {
        MyOrdersModel myOrdersModel =
        MyOrdersModel.fromJson(response.data);
        return ApiResult.success(myOrdersModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// Get Progress My Orders
  Future<ApiResult<MyOrdersModel>> showProgressOrders() async {
    final response = await myOrdersApiServices.showProgressOrders();
    try {
      if (response!.statusCode == 200) {
        MyOrdersModel myOrdersModel =
        MyOrdersModel.fromJson(response.data);
        return ApiResult.success(myOrdersModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred')
    );
  }

  /// Get Cancelled My Orders
  Future<ApiResult<MyOrdersModel>> showCancelledOrders() async {
    final response = await myOrdersApiServices.showCancelledOrders();
    try {
      if (response!.statusCode == 200) {
        MyOrdersModel myOrdersModel =
        MyOrdersModel.fromJson(response.data);
        return ApiResult.success(myOrdersModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred')
    );
  }

  /// Get Cancelled My Orders
  Future<Response?> showConfirmedOrders() async {
    return myOrdersApiServices.showCancelledOrders();
  }
  
   /// Get Show Order Details
  Future<ApiResult<OrderDetailsDataModel>> showOrderDetails(int orderId) async {
    final response = await myOrdersApiServices.showOrderDetails(orderId);
    try {
      if (response!.statusCode == 200) {
        OrderDetailsDataModel orderDetailsDataModel =
            OrderDetailsDataModel.fromJson(response.data);
        return ApiResult.success(orderDetailsDataModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}