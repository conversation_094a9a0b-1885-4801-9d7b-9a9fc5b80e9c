// @JsonSerializable()
// class OrderData {
//   int? id;
//   String? orderNumber;
//   double? total;
//   double? subtotalBeforeApplyCoupon;
//   double? subTotalAfterApplyCoupon;
//   double? couponDiscountAmount;
//   double? tax;
//   double? shipping;
//   String? currency;
//   String? orderStatus;
//   String? paymentMethod;
//   String? paymentStatus;
//   String? customerName;
//   String? customerEmail;
//   String? customerPhone;
//   String? billingAddress;
//   String? shippingAddress;
//   String? buildingNumber; // Keep as String if JSON has quotes around numbers
//   String? flatNumber;     // Keep as String if JSON has quotes around numbers
//   String? long;
//   String? lat;
//   String? shippingMethod;
//   String? trackingNumber;
//   String? appliedCouponCode;
//   String? metadata;
//   String? orderNotes;
//   String? orderSource;
//   int? couponId; // Assuming this can be null
//   int? userId;
//   String? dateCreated;

//   @JsonKey(name: 'orderDetails')
//   List<OrderDetail>? orderDetails;

//   OrderData({
//     this.id,
//     this.orderNumber,
//     this.total,
//     this.subtotalBeforeApplyCoupon,
//     this.subTotalAfterApplyCoupon,
//     this.couponDiscountAmount,
//     this.tax,
//     this.shipping,
//     this.currency,
//     this.orderStatus,
//     this.paymentMethod,
//     this.paymentStatus,
//     this.customerName,
//     this.customerEmail,
//     this.customerPhone,
//     this.billingAddress,
//     this.shippingAddress,
//     this.buildingNumber,
//     this.flatNumber,
//     this.long,
//     this.lat,
//     this.shippingMethod,
//     this.trackingNumber,
//     this.appliedCouponCode,
//     this.metadata,
//     this.orderNotes,
//     this.orderSource,
//     this.couponId,
//     this.userId,
//     this.dateCreated,
//     this.orderDetails,
//   });

//   factory OrderData.fromJson(Map<String, dynamic> json) =>
//       _$OrderDataFromJson(json);

//   Map<String, dynamic> toJson() => _$OrderDataToJson(this);
// }

// @JsonSerializable()
// class OrderDetail {
//   String? productName;
//   int? productId;
//   String? thumbnail;
//   double? price;
//   double? discount;
//   double? priceAfterDiscount;
//   int? quantity;
//   String? productNumber;
//   int? currentStockAmount;

//   @JsonKey(name: 'skuAttributes')
//   List<SkuAttribute>? skuAttributes;

//   Brand? brand; // Add brand field to represent the brand information

//   OrderDetail({
//     this.productName,
//     this.productId,
//     this.thumbnail,
//     this.price,
//     this.discount,
//     this.priceAfterDiscount,
//     this.quantity,
//     this.productNumber,
//     this.currentStockAmount,
//     this.skuAttributes,
//     this.brand, // Include brand in constructor
//   });

//   factory OrderDetail.fromJson(Map<String, dynamic> json) =>
//       _$OrderDetailFromJson(json);

//   Map<String, dynamic> toJson() => _$OrderDetailToJson(this);
// }

// @JsonSerializable()
// class Brand {
//   String? title;
//   String? thumbnail;

//   Brand({this.title, this.thumbnail});

//   factory Brand.fromJson(Map<String, dynamic> json) =>
//       _$BrandFromJson(json);

//   Map<String, dynamic> toJson() => _$BrandToJson(this);
// }

// @JsonSerializable()
// class SkuAttribute {
//   int? skuId;
//   String? value;

//   SkuAttribute({this.skuId, this.value});

//   factory SkuAttribute.fromJson(Map<String, dynamic> json) =>
//       _$SkuAttributeFromJson(json);

//   Map<String, dynamic> toJson() => _$SkuAttributeToJson(this);
// }
import 'package:json_annotation/json_annotation.dart';

part 'orders_details_data_model.g.dart';

@JsonSerializable()
class OrderDetailsDataModel {
  @JsonKey(name: 'data')
  OrderData? data;

  String? status;
  String? error;
  int? code;

  OrderDetailsDataModel({this.data, this.status, this.error, this.code});

  factory OrderDetailsDataModel.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailsDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$OrderDetailsDataModelToJson(this);
}

@JsonSerializable()
class OrderData {
  int? id;
  String? orderNumber;
  double? total;
  double? subtotalBeforeApplyCoupon;
  double? subTotalAfterApplyCoupon;
  double? couponDiscountAmount;
  double? tax;
  double? shipping;
  String? currency;
  String? orderStatus;
  String? paymentMethod;
  String? paymentStatus;
  String? customerName;
  String? customerEmail;
  String? customerPhone;
  String? billingAddress;
  String? shippingAddress;
  String? buildingNumber; 
  String? flatNumber; 
  String? long;
  String? lat;
  String? shippingMethod;
  String? trackingNumber;
  String? appliedCouponCode;
  String? metadata;
  String? orderNotes;
  String? orderSource;
  int? couponId; 
  int? userId;
  String? dateCreated;

  @JsonKey(name: 'orderDetails')
  List<OrderDetail>? orderDetails;

  OrderData({
    this.id,
    this.orderNumber,
    this.total,
    this.subtotalBeforeApplyCoupon,
    this.subTotalAfterApplyCoupon,
    this.couponDiscountAmount,
    this.tax,
    this.shipping,
    this.currency,
    this.orderStatus,
    this.paymentMethod,
    this.paymentStatus,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.billingAddress,
    this.shippingAddress,
    this.buildingNumber,
    this.flatNumber,
    this.long,
    this.lat,
    this.shippingMethod,
    this.trackingNumber,
    this.appliedCouponCode,
    this.metadata,
    this.orderNotes,
    this.orderSource,
    this.couponId,
    this.userId,
    this.dateCreated,
    this.orderDetails,
  });

  factory OrderData.fromJson(Map<String, dynamic> json) =>
      _$OrderDataFromJson(json);

  Map<String, dynamic> toJson() => _$OrderDataToJson(this);
}

@JsonSerializable()
class OrderDetail {
  String? productName;
  int? productId;
  String? thumbnail;
  bool? isRatedBefore;
  double? price;
  double? discount;
  double? priceAfterDiscount;
  int? quantity;
  String? productNumber;
  int? currentStockAmount;

  String? label; 

  @JsonKey(name: 'skuAttributes')
  List<SkuAttribute>? skuAttributes;

  Brand? brand; 

  OrderDetail({
    this.productName,
    this.productId,
    this.thumbnail,
    this.isRatedBefore,
    this.price,
    this.discount,
    this.priceAfterDiscount,
    this.quantity,
    this.productNumber,
    this.currentStockAmount,
    this.label, 
    this.skuAttributes,
    this.brand, 
  });

  factory OrderDetail.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailFromJson(json);

  Map<String, dynamic> toJson() => _$OrderDetailToJson(this);
}

@JsonSerializable()
class Brand {
  String? title;
  String? thumbnail;

  Brand({this.title, this.thumbnail});

  factory Brand.fromJson(Map<String, dynamic> json) => _$BrandFromJson(json);

  Map<String, dynamic> toJson() => _$BrandToJson(this);
}

@JsonSerializable()
class SkuAttribute {
  int? skuId;
  String? value;

  SkuAttribute({this.skuId, this.value});

  factory SkuAttribute.fromJson(Map<String, dynamic> json) =>
      _$SkuAttributeFromJson(json);

  Map<String, dynamic> toJson() => _$SkuAttributeToJson(this);
}
