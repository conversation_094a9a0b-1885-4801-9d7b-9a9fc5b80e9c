// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'orders_details_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderDetailsDataModel _$OrderDetailsDataModelFromJson(
        Map<String, dynamic> json) =>
    OrderDetailsDataModel(
      data: json['data'] == null
          ? null
          : OrderData.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String?,
      error: json['error'] as String?,
      code: (json['code'] as num?)?.toInt(),
    );

Map<String, dynamic> _$OrderDetailsDataModelToJson(
        OrderDetailsDataModel instance) =>
    <String, dynamic>{
      'data': instance.data,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

OrderData _$OrderDataFromJson(Map<String, dynamic> json) => OrderData(
      id: (json['id'] as num?)?.toInt(),
      orderNumber: json['orderNumber'] as String?,
      total: (json['total'] as num?)?.toDouble(),
      subtotalBeforeApplyCoupon:
          (json['subtotalBeforeApplyCoupon'] as num?)?.toDouble(),
      subTotalAfterApplyCoupon:
          (json['subTotalAfterApplyCoupon'] as num?)?.toDouble(),
      couponDiscountAmount: (json['couponDiscountAmount'] as num?)?.toDouble(),
      tax: (json['tax'] as num?)?.toDouble(),
      shipping: (json['shipping'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      orderStatus: json['orderStatus'] as String?,
      paymentMethod: json['paymentMethod'] as String?,
      paymentStatus: json['paymentStatus'] as String?,
      customerName: json['customerName'] as String?,
      customerEmail: json['customerEmail'] as String?,
      customerPhone: json['customerPhone'] as String?,
      billingAddress: json['billingAddress'] as String?,
      shippingAddress: json['shippingAddress'] as String?,
      buildingNumber: json['buildingNumber'] as String?,
      flatNumber: json['flatNumber'] as String?,
      long: json['long'] as String?,
      lat: json['lat'] as String?,
      shippingMethod: json['shippingMethod'] as String?,
      trackingNumber: json['trackingNumber'] as String?,
      appliedCouponCode: json['appliedCouponCode'] as String?,
      metadata: json['metadata'] as String?,
      orderNotes: json['orderNotes'] as String?,
      orderSource: json['orderSource'] as String?,
      couponId: (json['couponId'] as num?)?.toInt(),
      userId: (json['userId'] as num?)?.toInt(),
      dateCreated: json['dateCreated'] as String?,
      orderDetails: (json['orderDetails'] as List<dynamic>?)
          ?.map((e) => OrderDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$OrderDataToJson(OrderData instance) => <String, dynamic>{
      'id': instance.id,
      'orderNumber': instance.orderNumber,
      'total': instance.total,
      'subtotalBeforeApplyCoupon': instance.subtotalBeforeApplyCoupon,
      'subTotalAfterApplyCoupon': instance.subTotalAfterApplyCoupon,
      'couponDiscountAmount': instance.couponDiscountAmount,
      'tax': instance.tax,
      'shipping': instance.shipping,
      'currency': instance.currency,
      'orderStatus': instance.orderStatus,
      'paymentMethod': instance.paymentMethod,
      'paymentStatus': instance.paymentStatus,
      'customerName': instance.customerName,
      'customerEmail': instance.customerEmail,
      'customerPhone': instance.customerPhone,
      'billingAddress': instance.billingAddress,
      'shippingAddress': instance.shippingAddress,
      'buildingNumber': instance.buildingNumber,
      'flatNumber': instance.flatNumber,
      'long': instance.long,
      'lat': instance.lat,
      'shippingMethod': instance.shippingMethod,
      'trackingNumber': instance.trackingNumber,
      'appliedCouponCode': instance.appliedCouponCode,
      'metadata': instance.metadata,
      'orderNotes': instance.orderNotes,
      'orderSource': instance.orderSource,
      'couponId': instance.couponId,
      'userId': instance.userId,
      'dateCreated': instance.dateCreated,
      'orderDetails': instance.orderDetails,
    };

OrderDetail _$OrderDetailFromJson(Map<String, dynamic> json) => OrderDetail(
      productName: json['productName'] as String?,
      productId: (json['productId'] as num?)?.toInt(),
      thumbnail: json['thumbnail'] as String?,
      isRatedBefore: json['isRatedBefore'] as bool?,
      price: (json['price'] as num?)?.toDouble(),
      discount: (json['discount'] as num?)?.toDouble(),
      priceAfterDiscount: (json['priceAfterDiscount'] as num?)?.toDouble(),
      quantity: (json['quantity'] as num?)?.toInt(),
      productNumber: json['productNumber'] as String?,
      currentStockAmount: (json['currentStockAmount'] as num?)?.toInt(),
      label: json['label'] as String?,
      skuAttributes: (json['skuAttributes'] as List<dynamic>?)
          ?.map((e) => SkuAttribute.fromJson(e as Map<String, dynamic>))
          .toList(),
      brand: json['brand'] == null
          ? null
          : Brand.fromJson(json['brand'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$OrderDetailToJson(OrderDetail instance) =>
    <String, dynamic>{
      'productName': instance.productName,
      'productId': instance.productId,
      'thumbnail': instance.thumbnail,
      'isRatedBefore': instance.isRatedBefore,
      'price': instance.price,
      'discount': instance.discount,
      'priceAfterDiscount': instance.priceAfterDiscount,
      'quantity': instance.quantity,
      'productNumber': instance.productNumber,
      'currentStockAmount': instance.currentStockAmount,
      'label': instance.label,
      'skuAttributes': instance.skuAttributes,
      'brand': instance.brand,
    };

Brand _$BrandFromJson(Map<String, dynamic> json) => Brand(
      title: json['title'] as String?,
      thumbnail: json['thumbnail'] as String?,
    );

Map<String, dynamic> _$BrandToJson(Brand instance) => <String, dynamic>{
      'title': instance.title,
      'thumbnail': instance.thumbnail,
    };

SkuAttribute _$SkuAttributeFromJson(Map<String, dynamic> json) => SkuAttribute(
      skuId: (json['skuId'] as num?)?.toInt(),
      value: json['value'] as String?,
    );

Map<String, dynamic> _$SkuAttributeToJson(SkuAttribute instance) =>
    <String, dynamic>{
      'skuId': instance.skuId,
      'value': instance.value,
    };
