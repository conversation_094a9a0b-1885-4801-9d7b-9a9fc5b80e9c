import 'package:json_annotation/json_annotation.dart';

part 'my_orders_model.g.dart';

@JsonSerializable()
class MyOrdersModel {
  @JsonKey(name: 'data')
  OrdersData? ordersData;
  String? status;
  String? error;
  int? code;

  MyOrdersModel({this.ordersData, this.status, this.error, this.code});

  factory MyOrdersModel.fromJson(Map<String, dynamic> json) => _$MyOrdersModelFromJson(json);

  Map<String, dynamic> toJson() => _$MyOrdersModelToJson(this);
}

@JsonSerializable()
class OrdersData {
  @JsonKey(name: 'data')
  List<Order>? orders;
  Links? links;
  Meta? meta;

  OrdersData({this.orders, this.links, this.meta});

  factory OrdersData.fromJson(Map<String, dynamic> json) => _$OrdersDataFromJson(json);

  Map<String, dynamic> toJson() => _$OrdersDataToJson(this);
}

@JsonSerializable()
class Order {
  int? id;
  @JsonKey(name: 'order_number')
  String? orderNumber;
  List<String>? products;

  Order({this.id, this.orderNumber, this.products});

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);

  Map<String, dynamic> toJson() => _$OrderToJson(this);
}

@JsonSerializable()
class Links {
  String? first;
  String? last;
  String? prev;
  String? next;

  Links({this.first, this.last, this.prev, this.next});

  factory Links.fromJson(Map<String, dynamic> json) => _$LinksFromJson(json);

  Map<String, dynamic> toJson() => _$LinksToJson(this);
}

@JsonSerializable()
class Meta {
  @JsonKey(name: 'current_page')
  int? currentPage;
  int? from;
  @JsonKey(name: 'last_page')
  int? lastPage;
  List<PageLink>? links;
  String? path;
  @JsonKey(name: 'per_page')
  int? perPage;
  int? to;
  int? total;

  Meta({this.currentPage, this.from, this.lastPage, this.links, this.path, this.perPage, this.to, this.total});

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);

  Map<String, dynamic> toJson() => _$MetaToJson(this);
}

@JsonSerializable()
class PageLink {
  String? url;
  String? label;
  bool? active;

  PageLink({this.url, this.label, this.active});

  factory PageLink.fromJson(Map<String, dynamic> json) => _$PageLinkFromJson(json);

  Map<String, dynamic> toJson() => _$PageLinkToJson(this);
}