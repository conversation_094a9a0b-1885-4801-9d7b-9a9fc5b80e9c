part of 'my_orders_cubit.dart';

sealed class MyOrdersState {}

final class MyOrdersInitial extends MyOrdersState {}

final class MyOrdersTabChanged extends MyOrdersState {
  final int index;
  MyOrdersTabChanged(this.index);
}

/// Get Delivered My Orders
final class GetDeliveredMyOrdersLoading extends MyOrdersState {}

final class GetDeliveredMyOrdersSuccess extends MyOrdersState {}

final class GetDeliveredMyOrdersLoadingMore extends MyOrdersState {}

final class GetDeliveredMyOrdersError extends MyOrdersState {}

/// Get Progress My Orders
final class GetProgressMyOrdersLoading extends MyOrdersState {}

final class GetProgressMyOrdersSuccess extends MyOrdersState {}

final class GetProgressMyOrdersLoadingMore extends MyOrdersState {}

final class GetProgressMyOrdersError extends MyOrdersState {}

/// Get Canceled My Orders
final class GetCanceledMyOrdersLoading extends MyOrdersState {}

final class GetCanceledMyOrdersSuccess extends MyOrdersState {}

final class GetCanceledMyOrdersLoadingMore extends MyOrdersState {}

final class GetCanceledMyOrdersError extends MyOrdersState {}

/// Get Orders Details  States
class GetOrdersDetailsLoading extends MyOrdersState {}

class GetOrdersDetailsSuccess extends MyOrdersState {}

class GetOrdersDetailsError extends MyOrdersState {}