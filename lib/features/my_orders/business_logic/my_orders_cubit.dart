import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/models/my_orders/my_orders_model.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/models/orders_datails/orders_details_data_model.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/repos/my_orders_repo.dart';

part 'my_orders_state.dart';

class MyOrdersCubit extends Cubit<MyOrdersState> {
  MyOrdersCubit(this.myOrdersRepository) : super(MyOrdersInitial());

  MyOrdersModel? myOrdersModel;
  final MyOrdersRepository myOrdersRepository;

  OrderDetailsDataModel? orderDetailsDataModel;

  int currentIndex = 0;
  final ScrollController scrollPendingController = ScrollController();
  final ScrollController scrollDeliveredController = ScrollController();
  final ScrollController scrollCancelledController = ScrollController();
  bool isLoadingMore = false;

  void changeTab(int index) {
    emit(MyOrdersTabChanged(index));
  }

  /// Get Delivered My Orders
  Future showDeliveredOrders() async {
    emit(GetDeliveredMyOrdersLoading());
    final result = await myOrdersRepository.showDeliveredOrders();

    result.when(success: (data) {
      myOrdersModel = data;
      emit(GetDeliveredMyOrdersSuccess());
    }, failure: (error) {
      emit(GetDeliveredMyOrdersError());
    });
  }

  /// Get Delivered My Orders Pagination
  Future showDeliveredOrdersPagination() async {
    if (isLoadingMore ||
        (myOrdersModel?.ordersData?.meta?.currentPage ?? 1) >=
            (myOrdersModel?.ordersData?.meta?.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(GetDeliveredMyOrdersLoadingMore());

    final nextPage = myOrdersModel?.ordersData?.meta?.currentPage ?? 1;
    final result = await myOrdersRepository.showDeliveredOrders();

    result.when(success: (data) {
      myOrdersModel?.ordersData?.orders?.addAll(data.ordersData?.orders ?? []);
      myOrdersModel?.ordersData?.meta?.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(GetDeliveredMyOrdersSuccess());
    }, failure: (error) {
      isLoadingMore = false;
      emit(GetDeliveredMyOrdersError());
    });
  }

  /// Get Canceled My Orders
  Future showCanceledOrders() async {
    emit(GetCanceledMyOrdersLoading());
    final result = await myOrdersRepository.showCancelledOrders();

    result.when(success: (data) {
      myOrdersModel = data;
      emit(GetCanceledMyOrdersSuccess());
    }, failure: (error) {
      emit(GetCanceledMyOrdersError());
    });
  }

  /// Get Canceled My Orders Pagination
  Future showCanceledOrdersPagination() async {
    if (isLoadingMore ||
        (myOrdersModel?.ordersData?.meta?.currentPage ?? 1) >=
            (myOrdersModel?.ordersData?.meta?.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(GetCanceledMyOrdersLoadingMore());

    final nextPage = myOrdersModel?.ordersData?.meta?.currentPage ?? 1;
    final result = await myOrdersRepository.showCancelledOrders();

    result.when(success: (data) {
      myOrdersModel?.ordersData?.orders?.addAll(data.ordersData?.orders ?? []);
      myOrdersModel?.ordersData?.meta?.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(GetCanceledMyOrdersSuccess());
    }, failure: (error) {
      isLoadingMore = false;
      emit(GetCanceledMyOrdersError());
    });
  }

  /// Get Progress My Orders
  Future showProgressOrders() async {
    emit(GetProgressMyOrdersLoading());
    final result = await myOrdersRepository.showProgressOrders();

    result.when(success: (data) {
      myOrdersModel = data;
      emit(GetProgressMyOrdersSuccess());
    }, failure: (error) {
      emit(GetProgressMyOrdersError());
    });
  }

  /// Get Progress My Orders Pagination
  Future showProgressOrdersPagination() async {
    if (isLoadingMore ||
        (myOrdersModel?.ordersData?.meta?.currentPage ?? 1) >=
            (myOrdersModel?.ordersData?.meta?.lastPage ?? 1)) {
      return;
    }

    isLoadingMore = true;
    emit(GetProgressMyOrdersLoadingMore());

    final nextPage = myOrdersModel?.ordersData?.meta?.currentPage ?? 1;
    final result = await myOrdersRepository.showProgressOrders();

    result.when(success: (data) {
      myOrdersModel?.ordersData?.orders?.addAll(data.ordersData?.orders ?? []);
      myOrdersModel?.ordersData?.meta?.currentPage ?? nextPage;
      isLoadingMore = false;
      emit(GetProgressMyOrdersSuccess());
    }, failure: (error) {
      isLoadingMore = false;
      emit(GetProgressMyOrdersError());
    });
  }

  /// Get show order details
  Future<void> showOrderDetails({required int orderId}) async {
    emit(GetOrdersDetailsLoading());
    final result = await myOrdersRepository.showOrderDetails(orderId);

    result.when(success: (data) {
      orderDetailsDataModel = data;
      emit(GetOrdersDetailsSuccess());
    }, failure: (error) {
      emit(GetOrdersDetailsError());
    });
  }
}
