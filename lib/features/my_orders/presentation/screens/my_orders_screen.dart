import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/business_logic/my_orders_cubit.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/cancelled_listview_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/delivered_listview_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/pending_listview_widget.dart';

class MyOrdersScreen extends StatelessWidget {
  const MyOrdersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        backgroundColor: AppColors.scaffoldBackground,
        body: Column(
          children: [
            AppBarWidget(
              rowWidget: Row(
                children: [
                  BackButtonWidget(onTap: () => context.pop()),
                  SizedBox(width: 16.w),
                  Text(
                    'orders.myorders'.tr(),
                    style: Styles.heading2.copyWith(
                      color: AppColors.scaffoldBackground,
                    ),
                  ),
                ],
              ),
            ),
            24.verticalSpace,

            /// TabBar
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              height: 38.h,
              decoration: BoxDecoration(
                color: AppColors.scaffoldBackground,
              ),
              child: BlocBuilder<MyOrdersCubit, MyOrdersState>(
                builder: (context, state) {
                  return TabBar(
                    onTap: (index) {
                      context.read<MyOrdersCubit>().changeTab(index);
                    },
                    indicator: UnderlineTabIndicator(
                      borderSide: BorderSide(
                        width: 2.w,
                        color: AppColors.primaryColor900,
                      ),
                    ),
                    labelColor: AppColors.primaryColor900,
                    unselectedLabelColor: AppColors.neutralColor600,
                    labelStyle: Styles.highlightEmphasis.copyWith(
                      height: 1.h,
                    ),
                    tabs: [
                      Tab(text: 'orders.Pending'.tr()),
                      Tab(text: 'orders.delivered'.tr()),
                      Tab(text: 'orders.canceled'.tr()),
                    ],
                  );
                },
              ),
            ),

            /// TabBarView
            Expanded(
              child: TabBarView(
                children: [
                  PendingListViewWidget(),
                  DeliveredListViewWidget(),
                  CancelledListViewWidget(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
