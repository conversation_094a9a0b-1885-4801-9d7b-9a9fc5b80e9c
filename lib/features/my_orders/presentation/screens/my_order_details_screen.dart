import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/business_logic/my_orders_cubit.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/my_order_details/my_order_detials_sketlon_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/my_order_details/my_order_detials_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/my_order_details/your_order_widget.dart';

class MyOrderDetailsScreen extends StatelessWidget {
  const MyOrderDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final List<TimelineStep> steps = [
      TimelineStep(
        title: "مؤكد",
        description: "اكمل",
        isActive: true,
        isCompleted: true,
        color: Colors.green,
        icon: Icons.check,
      ),
      TimelineStep(
        title: "التعبئة والتغليف",
        description: "جاري التنفيذ",
        isActive: true,
        isCompleted: false,
        color: Colors.amber,
        icon: Icons.arrow_forward,
      ),
      TimelineStep(
        title: "خارج للتسليم",
        description: "في الانتظار",
        isActive: false,
        isCompleted: false,
        color: Colors.grey,
        icon: Icons.arrow_forward,
      ),
      TimelineStep(
        title: "تم التوصيل",
        description: "في الانتظار",
        isActive: false,
        isCompleted: false,
        color: Colors.grey,
        icon: Icons.arrow_forward,
      ),
    ];

    return BlocBuilder<MyOrdersCubit, MyOrdersState>(
      buildWhen: (previous, current) =>
          current is GetOrdersDetailsError ||
          current is GetOrdersDetailsSuccess ||
          current is GetOrdersDetailsLoading,
      builder: (context, state) {
        if (state is GetOrdersDetailsLoading) {
          return MyOrderDetailsSketlon();
        } else {
          return Scaffold(
            backgroundColor: AppColors.scaffoldBackground,
            body: Column(
              children: [
                AppBarWidget(
                  rowWidget: Row(
                    children: [
                      BackButtonWidget(onTap: () => context.pop()),
                      SizedBox(width: 16.w),
                      SizedBox(
                        width: MediaQuery.sizeOf(context).width * 0.75,
                        child: Row(
                          children: [
                            Text(
                              'orders.orderno'.tr(),
                              style: Styles.heading2.copyWith(
                                color: AppColors.scaffoldBackground,
                              ),
                            ),
                            Expanded(
                              child: Text(
                                context
                                    .read<MyOrdersCubit>()
                                    .orderDetailsDataModel!
                                    .data!
                                    .orderNumber!,
                                style: Styles.heading2.copyWith(
                                  color: AppColors.scaffoldBackground,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                /// Order implementation stages
                Expanded(
                  child: SingleChildScrollView(
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.only(
                        left: 16.h,
                        right: 16.h,
                        top: 24.h,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Text(
                          //   'orders.implementationstages'.tr(),
                          //   style: Styles.highlightEmphasis.copyWith(
                          //     color: AppColors.neutralColor1200,
                          //   ),
                          // ),
                          // 12.verticalSpace,

                          // Column(
                          //   children: List.generate(
                          //     steps.length,
                          //     (index) => TimelineTile(
                          //         step: steps[index],
                          //         isLast: index == steps.length - 1),
                          //   ),
                          // ),
                          // ModernTimelineWidget(steps: steps),

                          // 18.verticalSpace,
                          // Container(
                          //   padding: EdgeInsets.symmetric(
                          //     horizontal: 4.w,
                          //     vertical: 12.h,
                          //   ),
                          //   color: AppColors.redColor200,
                          //   child: Expanded(
                          //     child: Row(
                          //       mainAxisAlignment: MainAxisAlignment.start,
                          //       crossAxisAlignment: CrossAxisAlignment.start,
                          //       children: [
                          //         Container(
                          //           color: Colors.amber,
                          //           child: Column(
                          //             children: [
                          //               Container(
                          //                 padding: EdgeInsets.all(8.r),
                          //                 decoration: BoxDecoration(
                          //                   color: AppColors.greenColor100,
                          //                   borderRadius: BorderRadius.all(
                          //                     Radius.circular(8.r),
                          //                   ),
                          //                 ),
                          //                 child: Center(
                          //                   child: SvgPicture.asset(
                          //                     'assets/images/svgs/check_icon.svg',
                          //                     fit: BoxFit.scaleDown,
                          //                   ),
                          //                 ),
                          //               ),
                          //               Image.asset(
                          //                 'assets/images/pngs/vertical_divider_icon.png',
                          //                 color: AppColors.greenColor100,
                          //               ),
                          //             ],
                          //           ),
                          //         ),
                          //         16.horizontalSpace,
                          //         Column(
                          //           mainAxisAlignment:
                          //               MainAxisAlignment.spaceBetween,
                          //           children: [
                          //             Container(
                          //               color: Colors.blue,
                          //               child: Column(
                          //                 children: [
                          //                   Text(
                          //                     'المرحلة الاولة',
                          //                     style: Styles.contentRegular
                          //                         .copyWith(
                          //                       color:
                          //                           AppColors.neutralColor600,
                          //                     ),
                          //                   ),
                          //                   Text(
                          //                     'مؤكد',
                          //                     style: Styles.contentEmphasis
                          //                         .copyWith(
                          //                       color:
                          //                           AppColors.neutralColor1200,
                          //                     ),
                          //                   ),
                          //                   Text(
                          //                     'وصف',
                          //                     style: Styles.captionRegular
                          //                         .copyWith(
                          //                       color:
                          //                           AppColors.neutralColor600,
                          //                     ),
                          //                   ),
                          //                   Container(
                          //                     padding: EdgeInsets.symmetric(
                          //                       horizontal: 12.r,
                          //                       vertical: 8.h,
                          //                     ),
                          //                     decoration: BoxDecoration(
                          //                       color: AppColors.greenColor200
                          //                           .withOpacity(0.10),
                          //                       borderRadius: BorderRadius.all(
                          //                         Radius.circular(4.r),
                          //                       ),
                          //                     ),
                          //                     child: Center(
                          //                       child: Text(
                          //                         'اكتمل',
                          //                         style: Styles.captionRegular
                          //                             .copyWith(
                          //                           color:
                          //                               AppColors.greenColor200,
                          //                         ),
                          //                       ),
                          //                     ),
                          //                   ),
                          //                 ],
                          //               ),
                          //             ),
                          //             Container(
                          //               color: Colors.black,
                          //               child: Text(
                          //                 'المرحلة التانية',
                          //                 style: Styles.contentRegular.copyWith(
                          //                   color: AppColors.neutralColor600,
                          //                 ),
                          //               ),
                          //             ),
                          //           ],
                          //         ),
                          //       ],
                          //     ),
                          //   ),
                          // ),

                          /// Order Details
                          MyOrderDetailsWidget(),
                          18.verticalSpace,

                          /// Your Order
                          YourOrderWidget(),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }
}

class TimelineStep {
  final String title;
  final String description;
  final bool isActive;
  final bool isCompleted;
  final Color color;
  final IconData icon;

  TimelineStep({
    required this.title,
    required this.description,
    required this.isActive,
    required this.isCompleted,
    required this.color,
    required this.icon,
  });
}

class TimelineTile extends StatelessWidget {
  final TimelineStep step;
  final bool isLast;

  const TimelineTile({super.key, required this.step, required this.isLast});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: step.isActive ? step.color : Colors.grey,
                shape: BoxShape.circle,
              ),
              child: step.isCompleted
                  ? const Icon(Icons.check, color: Colors.white, size: 16)
                  : null,
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: step.isActive ? step.color : Colors.grey,
              ),
          ],
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Card(
            color:
                step.isActive ? step.color.withOpacity(0.2) : Colors.grey[200],
            child: ListTile(
              title: Text(step.title,
                  style: TextStyle(fontWeight: FontWeight.bold)),
              subtitle: Text(step.description),
              trailing: CircleAvatar(
                backgroundColor: step.color,
                child: Icon(step.icon, color: Colors.white),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class ModernTimelineWidget extends StatelessWidget {
  final List<TimelineStep> steps;

  const ModernTimelineWidget({
    super.key,
    required this.steps,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        steps.length,
            (index) => ModernTimelineTile(
          step: steps[index],
          isLast: index == steps.length - 1,
          stageNumber: index + 1,
        ),
      ),
    );
  }
}

class ModernTimelineTile extends StatelessWidget {
  final TimelineStep step;
  final bool isLast;
  final int stageNumber;

  const ModernTimelineTile({
    super.key,
    required this.step,
    required this.isLast,
    required this.stageNumber,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: isLast ? 0 : 8.h),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Timeline indicator and line
            Column(
              children: [
                _buildTimelineIndicator(),
                if (!isLast)
                  Container(
                    width: 2.w,
                    height: 70.h,
                    color: step.isCompleted ? Colors.green : Colors.grey.shade200,
                  ),
              ],
            ),
            SizedBox(width: 16.w),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Stage label
                  Text(
                    'المرحلة ${_getStageText(stageNumber)}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 4.h),

                  // Title
                  Text(
                    step.title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: 4.h),

                  // Description label
                  Text(
                    'وصف',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 4.h),

                  // Description content in a container
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 6.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      'اكتمل',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.green,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineIndicator() {
    if (step.isCompleted) {
      return Container(
        width: 40.w,
        height: 40.h,
        decoration: BoxDecoration(
          color: Colors.green,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          Icons.check,
          color: Colors.white,
          size: 24.sp,
        ),
      );
    } else if (step.isActive) {
      return Container(
        width: 40.w,
        height: 40.h,
        decoration: BoxDecoration(
          color: Colors.amber,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          Icons.arrow_forward,
          color: Colors.white,
          size: 24.sp,
        ),
      );
    } else {
      return Container(
        width: 40.w,
        height: 40.h,
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          Icons.arrow_forward,
          color: Colors.white,
          size: 24.sp,
        ),
      );
    }
  }

  String _getStageText(int stage) {
    switch (stage) {
      case 1:
        return 'الأولى';
      case 2:
        return 'الثانية';
      case 3:
        return 'الثالثة';
      case 4:
        return 'الرابعة';
      default:
        return '$stage';
    }
  }
}