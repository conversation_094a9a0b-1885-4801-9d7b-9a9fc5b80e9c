import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/widgets/empty/empty_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/business_logic/my_orders_cubit.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/my_order_item/my_order_item_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/my_order_item/my_order_item_widget.dart';

class DeliveredListViewWidget extends StatelessWidget {
  const DeliveredListViewWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MyOrdersCubit(getIt())..showDeliveredOrders(),
      child: <PERSON><PERSON><PERSON>er<MyOrdersCubit, MyOrdersState>(
        builder: (context, state) {
          final progressCubit = context.read<MyOrdersCubit>();

          print('Orders: ${progressCubit.myOrdersModel?.ordersData?.orders}');

          /// Add listener for pagination
          progressCubit.scrollDeliveredController.addListener(() {
            if (progressCubit.scrollDeliveredController.position.pixels >=
                progressCubit
                        .scrollDeliveredController.position.maxScrollExtent -
                    100) {
              progressCubit.showDeliveredOrdersPagination();
            }
          });

          return progressCubit.myOrdersModel == null ||
                  progressCubit.myOrdersModel!.ordersData == null ||
                  progressCubit.myOrdersModel!.ordersData!.orders == null
              ? ListView.separated(
                  padding: EdgeInsets.zero,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: EdgeInsets.only(
                        left: 16.w,
                        right: 16.w,
                        top: index == 0 ? 16.h : 0.h,
                      ),
                      child: MyOrderItemSkeletonizerWidget(),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return 16.verticalSpace;
                  },
                  itemCount: 10,
                )
              : progressCubit.myOrdersModel!.ordersData!.orders!.isEmpty
                  ? EmptyWidget(
                      imagePath: 'assets/images/svgs/emptyAddresses.svg',
                      title: 'orders.noDeliveredTitle'.tr(),
                      description: 'orders.noDeliveredDescription'.tr(),
                    )
                  : ListView.separated(
                      controller: progressCubit.scrollDeliveredController,
                      padding: EdgeInsets.zero,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: EdgeInsets.only(
                            left: 16.w,
                            right: 16.w,
                            top: index == 0 ? 16.h : 0.h,
                          ),
                          child: MyOrderItemWidget(
                            order: progressCubit
                                .myOrdersModel!.ordersData!.orders![index],
                          ),
                        );
                      },
                      separatorBuilder: (context, index) {
                        return 16.verticalSpace;
                      },
                      itemCount: progressCubit
                          .myOrdersModel!.ordersData!.orders!.length,
                    );
        },
      ),
    );
  }
}
