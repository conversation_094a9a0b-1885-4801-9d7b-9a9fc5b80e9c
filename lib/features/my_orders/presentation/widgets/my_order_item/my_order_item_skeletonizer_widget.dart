import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/address/data/model/address_model.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/models/my_orders/my_orders_model.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class MyOrderItemSkeletonizerWidget extends StatelessWidget {
  const MyOrderItemSkeletonizerWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.sp, horizontal: 8.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(color: AppColors.neutralColor600),
          // color: AppColors.white,
          // border: Border.all(color: AppColors.primaryColor900),
        ),
        child: Row(
          children: [
            Column(
              spacing: 4.sp,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الاوردر رقم : 65498',
                  style: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 16.sp,
                  ),
                ),
                SizedBox(
                  width: MediaQuery.sizeOf(context).width * .8,
                  child: Text(
                    'kjadfiubdaviu iuw',
                    style: Styles.contentEmphasis.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: 14.sp,
                      color: AppColors.neutralColor600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.sp,
              color: AppColors.primaryColor900,
            ),
          ],
        ),
      ),
    );
  }
}