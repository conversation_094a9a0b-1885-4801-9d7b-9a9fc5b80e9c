import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/my_orders/business_logic/my_orders_cubit.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/models/my_orders/my_orders_model.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/screens/my_order_details_screen.dart';

class MyOrderItemWidget extends StatelessWidget {
  const MyOrderItemWidget({
    super.key,
    required this.order,
  });

  // final VoidCallback? onTap;
  final Order order;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        PersistentNavBarNavigator.pushNewScreen(
          context,
          screen: BlocProvider(
            create: (context) =>
                MyOrdersCubit(getIt())..showOrderDetails(orderId: order.id!),
            child: MyOrderDetailsScreen(),
          ),
          withNavBar: true,
          pageTransitionAnimation: PageTransitionAnimation.cupertino,
        );
        //context.pushNamed(Routes.orderDetailsScreen, arguments: order.id);
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.sp, horizontal: 8.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            AppConstants.borderRadius,
          ),
          border: Border.all(color: AppColors.primaryColor900),
        ),
        child: Row(
          children: [
            Column(
              spacing: 4.sp,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${'orders.orderno'.tr()} ${order.orderNumber}',
                  style: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 16.sp,
                  ),
                ),
                SizedBox(
                  width: MediaQuery.sizeOf(context).width * .8,
                  child: Text(
                    order.products?.join(', ') ?? '',
                    style: Styles.contentEmphasis.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: 14.sp,
                      color: AppColors.neutralColor600,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.sp,
              color: AppColors.primaryColor900,
            ),
          ],
        ),
      ),
    );
  }
}
