import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/text/custom_text_rich_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/business_logic/my_orders_cubit.dart';

class MyOrderDetailsWidget extends StatelessWidget {
  const MyOrderDetailsWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'orders.productinformation'.tr(),
          style: Styles.highlightEmphasis.copyWith(
            color: AppColors.neutralColor1200,
          ),
        ),
        12.verticalSpace,
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColors.scaffoldBackground,
            borderRadius: BorderRadius.circular(
              AppConstants.borderRadius + 4.r,
            ),
            border: Border.all(
              color: AppColors.neutralColor600,
              width: 1.w,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  children: [
                    CustomRichText(
                      text1: 'orders.name'.tr(),
                      text2: context
                          .read<MyOrdersCubit>()
                          .orderDetailsDataModel!
                          .data!
                          .customerName!,
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  children: [
                    CustomRichText(
                      text1: 'orders.phone'.tr(),
                      text2: context
                          .read<MyOrdersCubit>()
                          .orderDetailsDataModel!
                          .data!
                          .customerPhone!,
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  children: [
                    CustomRichText(
                      text1: 'orders.date'.tr(),
                      text2: context
                          .read<MyOrdersCubit>()
                          .orderDetailsDataModel!
                          .data!
                          .dateCreated!,
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  children: [
                    CustomRichText(
                      text1: 'orders.address'.tr(),
                      text2: context
                          .read<MyOrdersCubit>()
                          .orderDetailsDataModel!
                          .data!
                          .billingAddress!,
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  children: [
                    CustomRichText(
                      text1: 'orders.paymentmethod'.tr(),
                      text2: context
                          .read<MyOrdersCubit>()
                          .orderDetailsDataModel!
                          .data!
                          .paymentMethod!,
                      // 'فيزا',
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    BlocBuilder<MyOrdersCubit, MyOrdersState>(
                      builder: (context, state) {
                        return CustomRichText(
                          text1: 'orders.orderno'.tr(),
                          text2: context
                              .read<MyOrdersCubit>()
                              .orderDetailsDataModel!
                              .data!
                              .orderNumber!,
                          // '21343434341',
                          textStyle1: Styles.contentEmphasis.copyWith(
                            color: AppColors.neutralColor600,
                          ),
                          textStyle2: Styles.contentRegular.copyWith(
                            color: AppColors.neutralColor1200,
                          ),
                        );
                      },
                    ),
                    // Container(
                    //   padding: EdgeInsets.symmetric(
                    //     vertical: 6.h,
                    //     horizontal: 10.w,
                    //   ),
                    //   decoration: BoxDecoration(
                    //     border: Border.all(
                    //       color: AppColors.neutralColor1200,
                    //       width: 1.w,
                    //     ),
                    //     borderRadius: BorderRadius.circular(
                    //       AppConstants.borderRadius - 4.r,
                    //     ),
                    //   ),
                    //   child: Row(
                    //     crossAxisAlignment: CrossAxisAlignment.center,
                    //     spacing: 8.w,
                    //     children: [
                    //       Text(
                    //         'orders.copy'.tr(),
                    //         style: Styles.footnoteRegular.copyWith(
                    //           color: AppColors.neutralColor1200,
                    //         ),
                    //       ),
                    //       SvgPicture.asset(
                    //         'assets/images/svgs/copy_icon.svg',
                    //         fit: BoxFit.scaleDown,
                    //       ),
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
              ),
              // Divider(
              //   color: AppColors.neutralColor600,
              // ),
              // Padding(
              //   padding: EdgeInsets.symmetric(
              //     horizontal: 12.w,
              //     vertical: 8.h,
              //   ),
              //   child: Row(
              //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //     children: [
              //       CustomRichText(
              //         text1: 'orders.paymentreceipt'.tr(),
              //         textStyle1: Styles.contentEmphasis.copyWith(
              //           color: AppColors.neutralColor1200,
              //         ),
              //       ),
              //       // Container(
              //       //   padding: EdgeInsets.symmetric(
              //       //     vertical: 6.h,
              //       //     horizontal: 10.w,
              //       //   ),
              //       //   decoration: BoxDecoration(
              //       //     border: Border.all(
              //       //       color: AppColors.neutralColor1200,
              //       //       width: 1.w,
              //       //     ),
              //       //     borderRadius: BorderRadius.circular(
              //       //       AppConstants.borderRadius - 4.r,
              //       //     ),
              //       //   ),
              //       //   child: Row(
              //       //     crossAxisAlignment: CrossAxisAlignment.center,
              //       //     spacing: 8.w,
              //       //     children: [
              //       //       Text(
              //       //         'orders.download'.tr(),
              //       //         style: Styles.footnoteRegular.copyWith(
              //       //           color: AppColors.neutralColor1200,
              //       //         ),
              //       //       ),
              //       //       SvgPicture.asset(
              //       //         'assets/images/svgs/download_icon.svg',
              //       //         fit: BoxFit.scaleDown,
              //       //       ),
              //       //     ],
              //       //   ),
              //       // ),
              //     ],
              //   ),
              // ),
            ],
          ),
        ),
      ],
    );
  }
}
