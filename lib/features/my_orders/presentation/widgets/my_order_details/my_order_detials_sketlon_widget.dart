import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/my_order_details/show_details_sketlon_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/my_order_details/your_order_sketlton_widget.dart';

class MyOrderDetailsSketlon extends StatelessWidget {
  const MyOrderDetailsSketlon({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Scaffold(
        backgroundColor: AppColors.scaffoldBackground,
        body: Column(
          children: [
            AppBarWidget(
              rowWidget: Row(
                children: [
                  BackButtonWidget(onTap: () => context.pop()),
                  SizedBox(width: 16.w),
                  SizedBox(
                    width: MediaQuery.sizeOf(context).width * 0.75,
                    child: Row(
                      children: [
                        Text(
                          'الاوردر رقم : ',
                          style: Styles.heading2.copyWith(
                            color: AppColors.scaffoldBackground,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            '21343434341',
                            style: Styles.heading2.copyWith(
                              color: AppColors.scaffoldBackground,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            /// Order implementation stages
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.only(
                    left: 16.h,
                    right: 16.h,
                    top: 24.h,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مراحل التنفيذ',
                        style: Styles.highlightEmphasis.copyWith(
                          color: AppColors.neutralColor1200,
                        ),
                      ),
                      12.verticalSpace,

                      18.verticalSpace,

                      /// Order Details
                      // MyOrderDetailsWidget(),
                      ShowDetialsWidgetSketlton(),
                      18.verticalSpace,

                      /// Your Order
                      // YourOrderWidget(),
                      YourOrderSketltonWidget(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
