import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/category_name_rotation_widget.dart';

class YourOrderSketltonWidget extends StatelessWidget {
  const YourOrderSketltonWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // final cubit = context.read<MyOrdersCubit>().orderDetailsDataModel!;
    return Skeletonizer(
      enabled: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طلبك',
            style: Styles.highlightEmphasis.copyWith(
              color: AppColors.neutralColor1200,
            ),
          ),
          12.verticalSpace,
          ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(AppConstants.borderRadius),
                  color: Colors.white,
                  border:
                      Border.all(color: AppColors.neutralColor200, width: 1.w),
                ),
                padding: EdgeInsets.all(16.sp),
                child: SizedBox(
                  width: 100.sp,
                  height: 100.sp,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 98.w,
                        height: 100.h,
                        child: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.only(
                                topLeft:
                                    Radius.circular(AppConstants.borderRadius),
                                topRight:
                                    Radius.circular(AppConstants.borderRadius),
                              ),
                              child: Image.asset(
                                "assets/images/pngs/t_shirt_image2.png",
                                fit: BoxFit.scaleDown,
                              ),
                            ),
                            CategoryNameRotationWidget(
                                inListViewInSearch: true, categoryName: ""),
                            Positioned(
                              bottom: 0.sp,
                              left: 0,
                              right: 0,
                              child: Image.asset(
                                Assets.assetsImagesPngsProductStackImage,
                                fit: BoxFit.fill,
                              ),
                            ),
                            Positioned.fill(
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(
                                        AppConstants.borderRadius),
                                    topRight: Radius.circular(
                                        AppConstants.borderRadius),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.black
                                          .withAlpha((0.16 * 255).toInt()),
                                      Colors.black
                                          .withAlpha((0.0 * 255).toInt()),
                                    ],
                                    stops: [0.0, 1.0],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      12.horizontalSpace,
                      Expanded(
                        child: Row(
                          children: [
                            Column(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'SAR 250',
                                  style: Styles.captionRegular.copyWith(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16.sp,
                                  ),
                                ),
                                Text(
                                  // cubit.data!.orderDetails![index].productName!,
                                  "تيشيرت بوما",
                                  style: Styles.contentRegular.copyWith(
                                    color: AppColors.neutralColor600,
                                  ),
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(100),
                                      child: Image.asset(
                                        height: 20.h,
                                        width: 20.w,
                                        fit: BoxFit.cover,
                                        "assets/images/pngs/puma_icon2.png",
                                      ),
                                      //     CachedNetworkImage(
                                      //   imageUrl:
                                      //       cubit.data!.orderDetails!.isEmpty
                                      //           ? ""
                                      //           : cubit.data!.orderDetails![index]
                                      //               .thumbnail!,
                                      //   height: 20.h,
                                      //   width: 20.w,
                                      //   fit: BoxFit.cover,
                                      // ),
                                    ),
                                    5.horizontalSpace,
                                    Text(
                                      "PUMA",
                                      // productCard.brand!.title!,
                                      style: Styles.contentRegular,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Spacer(),
                            Column(
                              children: [
                                Container(
                                  width: 30,
                                  height: 30,
                                  decoration: BoxDecoration(
                                    color: AppColors.primaryColor900,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: AppColors.primaryColor900,
                                      width: 1,
                                    ),
                                  ),
                                ),
                                8.verticalSpace,
                                Container(
                                  width: 30,
                                  height: 30,
                                  decoration: BoxDecoration(
                                    color: AppColors.scaffoldBackground,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.black12,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      textAlign: TextAlign.center,
                                      "1",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                ),
                                8.verticalSpace,
                                Container(
                                  width: 30,
                                  height: 30,
                                  decoration: BoxDecoration(
                                    color: AppColors.scaffoldBackground,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.black12,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      textAlign: TextAlign.center,
                                      "XL",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
              // SearchListViewWidgetSkeletonizer();
            },
            separatorBuilder: (context, index) {
              return 8.verticalSpace;
            },
            itemCount: 5,
          ),
        ],
      ),
    );
  }
}
