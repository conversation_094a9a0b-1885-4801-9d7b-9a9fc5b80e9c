import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/text/custom_text_rich_widget.dart';

class ShowDetialsWidgetSketlton extends StatelessWidget {
  const ShowDetialsWidgetSketlton({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مراحل التنفيذ',
          style: Styles.highlightEmphasis.copyWith(
            color: AppColors.neutralColor1200,
          ),
        ),
        12.verticalSpace,
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColors.scaffoldBackground,
            borderRadius: BorderRadius.circular(
              AppConstants.borderRadius + 4.r,
            ),
            border: Border.all(
              color: AppColors.neutralColor600,
              width: 1.w,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  children: [
                    CustomRichText(
                      text1: 'الاسم : ',
                      text2: 'عمر عبدالعزيز محمد',
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  children: [
                    CustomRichText(
                      text1: 'رقم الهاتف : ',
                      text2: '01023359621',
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  children: [
                    CustomRichText(
                      text1: 'التاريخ : ',
                      text2: '19 ديسمبر 2024',
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  children: [
                    CustomRichText(
                      text1: 'العنوان : ',
                      text2: 'العنوان',
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  children: [
                    CustomRichText(
                      text1: 'طريقة الدفع : ',
                      text2: 'فيزا',
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomRichText(
                      text1: 'رقم الاوردر : ',
                      text2: '21343434341',
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor600,
                      ),
                      textStyle2: Styles.contentRegular.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 6.h,
                        horizontal: 10.w,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.neutralColor1200,
                          width: 1.w,
                        ),
                        borderRadius: BorderRadius.circular(
                          AppConstants.borderRadius - 4.r,
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        spacing: 8.w,
                        children: [
                          Text(
                            'نسخ',
                            style: Styles.footnoteRegular.copyWith(
                              color: AppColors.neutralColor1200,
                            ),
                          ),
                          SvgPicture.asset(
                            'assets/images/svgs/copy_icon.svg',
                            fit: BoxFit.scaleDown,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Divider(
                color: AppColors.neutralColor600,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 8.h,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomRichText(
                      text1: 'إيصال الدفع',
                      textStyle1: Styles.contentEmphasis.copyWith(
                        color: AppColors.neutralColor1200,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 6.h,
                        horizontal: 10.w,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.neutralColor1200,
                          width: 1.w,
                        ),
                        borderRadius: BorderRadius.circular(
                          AppConstants.borderRadius - 4.r,
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        spacing: 8.w,
                        children: [
                          Text(
                            'تنزيل',
                            style: Styles.footnoteRegular.copyWith(
                              color: AppColors.neutralColor1200,
                            ),
                          ),
                          SvgPicture.asset(
                            'assets/images/svgs/download_icon.svg',
                            fit: BoxFit.scaleDown,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
