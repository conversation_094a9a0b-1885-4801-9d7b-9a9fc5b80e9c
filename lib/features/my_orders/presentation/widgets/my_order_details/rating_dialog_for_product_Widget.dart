import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/brand_details/presentation/widgets/custom_rating_widget.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';

class RatingDialogForProductWidget extends StatelessWidget {
  const RatingDialogForProductWidget(
      {super.key, required this.productId, required this.orderId});

  final int productId;
  final int orderId;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          ProductDetailsCubit(getIt())..getProductReviews(productId),
      child: BlocConsumer<ProductDetailsCubit, ProductDetailsState>(
        buildWhen: (previous, current) =>
            current is GetProductReviewsLoadingState ||
            current is GetProductReviewsSuccessState ||
            current is GetProductReviewsErrorState ||
            current is GetProductReviewsLoadingMoreState,
        listener: (context, state) {
          if (state is MakeRateProductSuccessState) {
            context.pop();
          }
        },
        builder: (context, state) {
          final productDetailsCubit = context.read<ProductDetailsCubit>();
          final reviews = productDetailsCubit
              .reviewsResponseModel?.productReviewsData?.reviews;

          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius:
                  BorderRadius.circular(AppConstants.borderRadius + 4),
            ),
            child: Container(
              width: double.infinity,
              //height: 298.h,
              padding: EdgeInsets.all(20.sp),
              decoration: BoxDecoration(
                color: AppColors.scaffoldBackground,
                borderRadius:
                    BorderRadius.circular((AppConstants.borderRadius + 4)),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 20.verticalSpace,
                  InkWell(
                    onTap: () {
                      context.pop();
                    },
                    child: Container(
                      width: 50.w,
                      height: 50.h,
                      padding: EdgeInsets.all(12.sp),
                      decoration: BoxDecoration(
                        color: AppColors.neutralColor100,
                        borderRadius: BorderRadius.circular(
                          AppConstants.borderRadius,
                        ),
                      ),
                      //color: Colors.black,
                      child: SvgPicture.asset(
                        width: 24,
                        height: 24,
                        Assets.assetsImagesSvgsCloseIcon,
                      ),
                    ),
                  ),
                  20.verticalSpace,
                  Text(
                    'orders.productevaluation'.tr(),
                    style: Styles.heading3.copyWith(color: Colors.black),
                  ),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'orders.evaluationDesc'.tr(),
                        textAlign: TextAlign.center,
                        style: Styles.contentRegular
                            .copyWith(color: AppColors.neutralColor1000),
                      ),
                      Text(
                        reviews?.length.toString() ?? '...',
                        textAlign: TextAlign.center,
                        style: Styles.contentRegular
                            .copyWith(color: AppColors.neutralColor1000),
                      ),
                    ],
                  ),
                  20.verticalSpace,
                  CustomRatingWidget(
                    rating: 4.0,
                    totalRatings: productDetailsCubit.selectedRating,
                    onRatingUpdate: (value) {
                      productDetailsCubit.updateRating(value);
                    },
                  ),
                  20.verticalSpace,
                  CustomTextFormFieldWidget(
                    controller: productDetailsCubit.messageController,
                    // maxLines: 2,
                    borderColor: AppColors.primaryColor900,
                    borderRadius: AppConstants.borderRadius + 4,
                    hintText: 'orders.yourreview'.tr(),
                    hintStyle: Styles.contentRegular.copyWith(
                      color: AppColors.neutralColor600,
                    ),
                    labelText: 'orders.description'.tr(),
                    labelStyle: Styles.heading5,
                    backgroundColor: Colors.white,
                    //height: 50.sp,
                  ),
                  20.verticalSpace,
                  CustomButtonWidget(
                    margin: EdgeInsets.symmetric(horizontal: 70.w),
                    height: 55.h,
                    text: 'orders.toBeSure'.tr(),
                    onPressed: () {
                      logSuccess(productDetailsCubit.selectedRating.toString());
                      logSuccess(productDetailsCubit.messageController.text
                          .toString());

                      productDetailsCubit.makeRateProduct(
                        productId,
                        productDetailsCubit.selectedRating,
                        productDetailsCubit.messageController.text,
                      );
                    },
                  )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
