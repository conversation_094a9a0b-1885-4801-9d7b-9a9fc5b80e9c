import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/show_more/show_more_widget.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/category_name_rotation_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/business_logic/my_orders_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_details_screen.dart';

class YourOrderWidget extends StatelessWidget {
  const YourOrderWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<MyOrdersCubit>().orderDetailsDataModel!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'orders.yourorder'.tr(),
          style: Styles.highlightEmphasis.copyWith(
            color: AppColors.neutralColor1200,
          ),
        ),
        12.verticalSpace,
        ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          itemBuilder: (context, index) {
            return InkWell(
              onTap: () {
                PersistentNavBarNavigator.pushNewScreen(
                  context,
                  screen: BlocProvider(
                    create: (context) => ProductDetailsCubit(getIt())
                      ..showProductDetails(
                          cubit.data!.orderDetails![index].productId!,
                          skuId: cubit.data!.orderDetails![index].skuAttributes!
                              .first.skuId!)
                      ..loadSimilarProducts(
                          cubit.data!.orderDetails![index].productId!)
                      ..getProductReviews(
                          cubit.data!.orderDetails![index].productId!),
                    child: ProductDetailsScreen(),
                  ),
                  withNavBar: true,
                  pageTransitionAnimation: PageTransitionAnimation.cupertino,
                );
              },
              // context.pushNamed(
              //   Routes.productsDetailsScreen,
              //   arguments: ProductDetailsArguments(
              //     productId: cubit.data!.orderDetails![index].productId!,
              //     skuAttributes:
              //         cubit.data!.orderDetails![index].skuAttributes!,
              //   ),
              // );

              child: Container(
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(AppConstants.borderRadius),
                  color: Colors.white,
                  border:
                      Border.all(color: AppColors.neutralColor200, width: 1.w),
                ),
                padding: EdgeInsets.all(16.sp),
                child: SizedBox(
                  width: 100.sp,
                  height: 100.sp,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 98.w,
                        height: 100.h,
                        child: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.only(
                                topLeft:
                                    Radius.circular(AppConstants.borderRadius),
                                topRight:
                                    Radius.circular(AppConstants.borderRadius),
                              ),
                              child: CachedNetworkImage(
                                imageUrl: cubit.data!.orderDetails!.isEmpty
                                    ? ""
                                    : cubit
                                        .data!.orderDetails![index].thumbnail!,
                                fit: BoxFit.scaleDown,
                                errorWidget: (context, url, error) => Center(
                                  child: Icon(Icons.error),
                                ),
                              ),
                            ),
                            CategoryNameRotationWidget(
                                inListViewInSearch: true,
                                categoryName:
                                    cubit.data!.orderDetails![index].label!),
                            Positioned(
                              bottom: 0.sp,
                              left: 0,
                              right: 0,
                              child: Image.asset(
                                Assets.assetsImagesPngsProductStackImage,
                                fit: BoxFit.fill,
                              ),
                            ),
                            Positioned.fill(
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(
                                        AppConstants.borderRadius),
                                    topRight: Radius.circular(
                                        AppConstants.borderRadius),
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.black
                                          .withAlpha((0.16 * 255).toInt()),
                                      Colors.black
                                          .withAlpha((0.0 * 255).toInt()),
                                    ],
                                    stops: [0.0, 1.0],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      12.horizontalSpace,
                      Expanded(
                        child: Row(
                          children: [
                            Column(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'SAR ${cubit.data!.orderDetails![index].priceAfterDiscount!}',
                                  style: Styles.captionRegular.copyWith(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16.sp,
                                  ),
                                ),
                                Text(
                                  cubit.data!.orderDetails![index].productName!,
                                  style: Styles.contentRegular.copyWith(
                                    color: AppColors.neutralColor600,
                                  ),
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(100),
                                      child: CachedNetworkImage(
                                        imageUrl:
                                            cubit.data!.orderDetails!.isEmpty
                                                ? ""
                                                : cubit
                                                    .data!
                                                    .orderDetails![index]
                                                    .brand!
                                                    .thumbnail!,
                                        errorWidget: (context, url, error) =>
                                            Center(
                                          child: Icon(Icons.error),
                                        ),
                                        height: 20.h,
                                        width: 20.w,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    5.horizontalSpace,
                                    Text(
                                      cubit.data!.orderDetails![index].brand!
                                          .title!,
                                      style: Styles.contentRegular,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Spacer(),
                            cubit.data!.orderDetails![index].isRatedBefore ==
                                    true
                                ? Container()
                                : Column(
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 2.5.h),
                                        child: ShowMoreWidget(
                                          onTapShowMore: () {
                                            AppConstants
                                                .showRatingDialogForProduct(
                                              context: context,
                                              productId: cubit
                                                  .data!
                                                  .orderDetails![index]
                                                  .productId!,
                                              orderId: cubit.data!.id!,
                                            );
                                          },
                                          showMore: false,
                                          text: 'orders.evaluationproduct'.tr(),
                                        ),
                                      ),
                                    ],
                                  )
                            // Column(
                            //   spacing: 8.h,
                            //   children: [
                            //     for (int i = 0;
                            //         i <
                            //             cubit.data!.orderDetails![index]
                            //                 .skuAttributes!.length;
                            //         i++)
                            //       if (cubit.data!.orderDetails![index]
                            //           .skuAttributes![i].value!
                            //           .startsWith("#"))
                            //         Container(
                            //           width: 30,
                            //           height: 30,
                            //           decoration: BoxDecoration(
                            //             color: Color(int.parse(cubit
                            //                 .data!
                            //                 .orderDetails![index]
                            //                 .skuAttributes![i]
                            //                 .value!
                            //                 .replaceFirst('#', '0xff'))),
                            //             shape: BoxShape.circle,
                            //             border: Border.all(
                            //               color: Colors.black12,
                            //             ),
                            //           ),
                            //         ),

                            //     Container(
                            //       width: 30,
                            //       height: 30,
                            //       decoration: BoxDecoration(
                            //         color: AppColors.scaffoldBackground,
                            //         shape: BoxShape.circle,
                            //         border: Border.all(
                            //           color: Colors.black12,
                            //         ),
                            //       ),
                            //       child: Center(
                            //         child: Text(
                            //           textAlign: TextAlign.center,
                            //           cubit.data!.orderDetails![index].quantity!
                            //               .toString(),
                            //           style: TextStyle(
                            //             fontWeight: FontWeight.bold,
                            //             color: Colors.black,
                            //           ),
                            //         ),
                            //       ),
                            //     ),
                            //     // 8.verticalSpace,
                            //     Container(
                            //       width: 30,
                            //       height: 30,
                            //       decoration: BoxDecoration(
                            //         color: AppColors.scaffoldBackground,
                            //         shape: BoxShape.circle,
                            //         border: Border.all(
                            //           color: Colors.black12,
                            //         ),
                            //       ),
                            //       child: Center(
                            //         child: Text(
                            //           textAlign: TextAlign.center,
                            //           // "XL",
                            //           cubit.data!.orderDetails![index]
                            //                       .skuAttributes![1].value! ==
                            //                   'صغير'
                            //               ? 'S'
                            //               : cubit
                            //                           .data!
                            //                           .orderDetails![index]
                            //                           .skuAttributes![1]
                            //                           .value! ==
                            //                       'كبير'
                            //                   ? 'L'
                            //                   : 'M',
                            //           style: TextStyle(
                            //             fontWeight: FontWeight.bold,
                            //             color: Colors.black,
                            //           ),
                            //         ),
                            //       ),
                            //     ),
                            //   ],
                            // )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
          separatorBuilder: (context, index) {
            return 8.verticalSpace;
          },
          itemCount: cubit.data!.orderDetails!.length,
        ),
      ],
    );
  }
}
