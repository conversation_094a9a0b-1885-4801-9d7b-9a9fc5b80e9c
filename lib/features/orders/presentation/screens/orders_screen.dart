import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart'
    show PersistentNavBarNavigator;
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tegra_ecommerce_app/core/extensions/navigation_extension.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart'
    show getIt;
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/back_button/back_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_cubit.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_state.dart';
import 'package:tegra_ecommerce_app/features/cart/presentation/widget/cart_item/cart_item_widget.dart';
import 'package:tegra_ecommerce_app/features/cart/presentation/widget/cart_recipt/cart_recipt_widget.dart';
import 'package:tegra_ecommerce_app/features/orders/presentation/widgets/total_price_and_discount_code.dart';
import 'package:tegra_ecommerce_app/features/payment/business_logic/payment_cubit.dart';
import 'package:tegra_ecommerce_app/features/payment/presentation/screens/payment_screen.dart';

class OrdersScreen extends StatelessWidget {
  const OrdersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<CartCubit>();
    final RefreshController refreshController = RefreshController();

    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppBarWidget(
            rowWidget: Row(
              spacing: 16.w,
              children: [
                BackButtonWidget(onTap: () => context.pop()),
                Text(
                  'orders.orders'.tr(),
                  style: Styles.heading2.copyWith(
                    color: AppColors.scaffoldBackground,
                  ),
                ),
              ],
            ),
          ),
          // SizedBox(height: 30.h),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  BlocBuilder<CartCubit, CartState>(
                    buildWhen: (previous, current) =>
                        current is RemoveCartSuccessState,
                    builder: (context, state) {
                      return ListView.separated(
                        physics: NeverScrollableScrollPhysics(),
                        itemCount: cubit.productModel!.data.length,
                        shrinkWrap: true,
                        separatorBuilder: (BuildContext context, int index) {
                          return 16.verticalSpace;
                        },
                        itemBuilder: (BuildContext context, int index) {
                          return CartItemWidget(
                            item: cubit.productModel!.data[index],
                            index: index,
                          );
                        },
                      );
                    },
                  ),
                  20.verticalSpace,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CartDetails(),
                      16.verticalSpace,
                      TotalPriceAndDiscountCode()
                    ],
                  )
                ],
              ),
            ),
          ),
          24.verticalSpace,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: CustomButtonWidget(
              text: 'orders.payment'.tr(),
              // onPressed: () => context.pushNamed(
              //   Routes.paymentScreen,
              // ),
              onPressed: () => PersistentNavBarNavigator.pushNewScreen(
                context,
                screen: BlocProvider(
                  create: (context) => PaymentCubit(getIt()),
                  child: PaymentScreen(data: cubit.cartReceiptModel!.data),
                ),
              ),
            ),
          ),
          20.verticalSpace,
        ],
      ),
    );
  }
}
