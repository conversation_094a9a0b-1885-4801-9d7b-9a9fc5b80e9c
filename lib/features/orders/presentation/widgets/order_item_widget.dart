import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/widgets/products/category_name_rotation_widget.dart';

class OrdersItemWidget extends StatelessWidget {
  const OrdersItemWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        color: Colors.white,
        border: Border.all(color: AppColors.neutralColor200, width: 1.w),
      ),
      padding: EdgeInsets.all(16.sp),
      child: SizedBox(
        width: 100.w,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 98.w,
              height: 100.h,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppConstants.borderRadius),
                      topRight: Radius.circular(AppConstants.borderRadius),
                    ),
                    child: Image.asset(
                      "assets/images/pngs/t_shirt_image2.png",
                      fit: BoxFit.scaleDown,
                    ),
                  ),
                  CategoryNameRotationWidget(
                    inListViewInSearch: true,
                    categoryName: AppConstants.categories[1],
                  ),
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Image.asset(
                      Assets.assetsImagesPngsProductStackImage,
                      fit: BoxFit.fill,
                    ),
                  ),
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(AppConstants.borderRadius),
                          topRight: Radius.circular(AppConstants.borderRadius),
                        ),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.black.withAlpha((0.16 * 255).toInt()),
                            Colors.black.withAlpha((0.0 * 255).toInt()),
                          ],
                          stops: [0.0, 1.0],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            12.horizontalSpace,
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    spacing: 4.h,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'SAR ${250}',
                            style: Styles.captionRegular.copyWith(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                              fontSize: 16.sp,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        "تيشيرت بوما",
                        style: Styles.contentRegular.copyWith(
                          color: AppColors.neutralColor600,
                        ),
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset(
                            Assets.assetsImagesPngsPumaIcon,
                            height: 20.h,
                            width: 20.w,
                            fit: BoxFit.cover,
                          ),
                          5.horizontalSpace,
                          Text(
                            "PUMA",
                            style: Styles.contentEmphasis,
                          ),
                        ],
                      ),
                    ],
                  ),
                  12.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.scaffoldBackground,
                          border: Border.all(
                            color: AppColors.neutralColor300,
                          ),
                          borderRadius: BorderRadius.all(
                            Radius.circular(
                              AppConstants.borderRadius - 4,
                            ),
                          ),
                        ),
                        width: 104.w,
                        height: 32,
                        //color: Colors.amber,
                        child: Row(
                          children: [
                            Expanded(
                              child: SvgPicture.asset(
                                height: 16,
                                width: 16,
                                Assets.assetsImagesSvgsAddIcon,
                              ),
                            ),
                            VerticalDivider(
                              color: AppColors.neutralColor300,
                              width: 1.w,
                            ),
                            Expanded(
                              child: Text(
                                textAlign: TextAlign.center,
                                "1",
                                style: Styles.contentRegular,
                              ),
                            ),
                            VerticalDivider(
                              color: AppColors.neutralColor300,
                              width: 1.w,
                            ),
                            Expanded(
                              child: SvgPicture.asset(
                                height: 16,
                                width: 16,
                                Assets.assetsImagesSvgsDecrementIcon,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SvgPicture.asset(
                        Assets.assetsImagesSvgsDeleteRedIcon,
                      )
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
