import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/button/custom_button_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_cubit.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_state.dart';

class TotalPriceAndDiscountCode extends StatelessWidget {
  const TotalPriceAndDiscountCode({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartCubit, CartState>(
      builder: (context, state) {
        final cubit = context.read<CartCubit>();
        return Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "orders.totalPrice".tr(),
                  style: Styles.highlightEmphasis.copyWith(
                    color: AppColors.neutralColor1200,
                  ),
                ),
                Text(
                  "SAR  ${cubit.cartReceiptModel?.data.total ?? 0}",
                  style: Styles.heading3.copyWith(
                    color: AppColors.neutralColor1200,
                  ),
                ),
              ],
            ),
            18.verticalSpace,
            BlocConsumer<CartCubit, CartState>(
              listener: (context, state) {
                if (state is ApplyCouponSuccessState) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        "Coupon applied successfully!",
                        style: TextStyle(color: Colors.white),
                      ),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 2),
                    ),
                  );
                }
                if (state is RemoveCartSuccessState) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        "Coupon removed successfully!",
                        style: TextStyle(color: Colors.white),
                      ),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 2),
                    ),
                  );
                }
              },
              builder: (context, state) {
                logSuccess(cubit.isCancel.toString());
                return Row(
                  children: [
                    Expanded(
                      flex: 4,
                      child: CustomTextFormFieldWidget(
                        controller: cubit.couponTextFormField,
                        //contentPadding: EdgeInsets.all(0),
                        hintText: "orders.discountCode".tr(),
                        hintStyle: Styles.contentRegular.copyWith(
                          color: AppColors.neutralColor600,
                        ),
                        borderColor: AppColors.neutralColor600,
                        borderRadius: AppConstants.borderRadius,
                        prefixIcon: Container(
                          margin: EdgeInsets.all(10.sp),
                          child: SvgPicture.asset(
                            width: 20.w,
                            height: 20.h,
                            Assets.assetsImagesSvgsDiscountCodeIcon,
                          ),
                        ),
                      ),
                    ),
                    12.horizontalSpace,
                    Expanded(
                      flex: 2,
                      child: CustomButtonWidget(
                        text: !cubit.isCancel
                            ? "orders.cancellation".tr()
                            : "orders.activation".tr(),
                        color: !cubit.isCancel
                            ? AppColors.redColor100
                            : AppColors.primaryColor900,
                        onPressed: !cubit.isCancel
                            ? () {
                                cubit.couponTextFormField.clear();
                                cubit.removeCoupon();
                              }
                            : () {
                                cubit.applyCoupon(
                                  coupon: cubit.couponTextFormField.text,
                                );
                              },
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        );
      },
    );
  }
}
