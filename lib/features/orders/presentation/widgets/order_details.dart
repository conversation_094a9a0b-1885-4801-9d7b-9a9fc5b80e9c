import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';

class OrderDetails extends StatelessWidget {
  const OrderDetails({super.key});

  @override
  Widget build(BuildContext context) {
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'orders.details'.tr(),
          style: Styles.heading5,
        ),
        12.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'orders.totalPrice'.tr(),
              style: Styles.contentEmphasis.copyWith(
                color: AppColors.neutralColor600,
              ),
            ),
            Text(
              "SAR 565",
              style: Styles.contentEmphasis.copyWith(
                color: AppColors.neutralColor1200,
              ),
            ),
          ],
        ),
        16.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "orders.discount".tr(),
              style: Styles.contentEmphasis.copyWith(
                color: AppColors.neutralColor600,
              ),
            ),
            Text(
              "SAR 113-",
              style: Styles.contentEmphasis.copyWith(
                color: AppColors.redColor100,
              ),
            ),
          ],
        ),
        16.verticalSpace,
        Divider(
          color: AppColors.neutralColor10,
        ),
      ],
    );
  }
}
