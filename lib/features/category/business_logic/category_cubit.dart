import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/features/category/data/repos/category_repo.dart';
import 'package:tegra_ecommerce_app/features/category/data/model/all_categories_model.dart';

part 'category_state.dart';

class CategoryCubit extends Cubit<CategoryState> {
  CategoryCubit(this.homeRepository) : super(CategoryInitial());

  final CategroyRepository homeRepository;
  CategoriesModel? categoriesModel;
  int currentBannerIndex = 0;

  /// Get All Category
  Future getAllCategories() async {
    emit(GetAllCategoriesLoading());
    final result = await homeRepository.getAllCategories();

    result.when(
      success: (data) {
        categoriesModel = data;
        emit(GetAllCategoriesSuccess());
      },
      failure: (errorHandler) {
        emit(GetAllCategoriesError());
      },
    );
  }
}
