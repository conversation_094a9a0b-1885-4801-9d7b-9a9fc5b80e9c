import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/main_app_bar_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/empty/empty_widget.dart';
import 'package:tegra_ecommerce_app/core/widgets/text_field/custom_text_form_field_widget.dart';
import 'package:tegra_ecommerce_app/features/category/business_logic/category_cubit.dart';
import 'package:tegra_ecommerce_app/features/category/presentation/widgets/category_grid_view_in_category_skeletonizer_widget.dart';
import 'package:tegra_ecommerce_app/features/category/presentation/widgets/category_grid_view_in_category_widget.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/screens/search_screen.dart';

class CategoryScreen extends StatelessWidget {
  const CategoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MainAppBarWidget(),
          SizedBox(height: 20.h),

          /// Search Field
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: CustomTextFormFieldWidget(
              onTap: () => PersistentNavBarNavigator.pushNewScreen(
                context,
                screen: BlocProvider(
                  create: (context) => SearchCubit(getIt()),
                  child: SearchScreen(),
                ),
                withNavBar: true,
                pageTransitionAnimation: PageTransitionAnimation.cupertino,
              ),
              //context.pushNamed(Routes.searchScreen),
              readOnly: true,
              backgroundColor: AppColors.neutralColor100,
              borderRadius: AppConstants.borderRadius + 2,
              borderColor: AppColors.neutralColor100,
              borderWidth: 1.w,
              prefixIcon: SvgPicture.asset(
                'assets/images/svgs/search_icon.svg',
                fit: BoxFit.scaleDown,
              ),
              hintText: 'home.whatAreYouThinkingAbout'.tr(),
              hintStyle: Styles.contentRegular.copyWith(
                color: AppColors.neutralColor1200,
              ),
            ),
          ),
          SizedBox(height: 8.h),

          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 20.h),
                    Text(
                      'categories.categories'.tr(),
                      style: Styles.heading5.copyWith(
                        color: Colors.black,
                      ),
                    ),
                    SizedBox(height: 20.h),
                    BlocBuilder<CategoryCubit, CategoryState>(
                      buildWhen: (previous, current) =>
                          current is GetAllCategoriesLoading ||
                          current is GetAllCategoriesSuccess ||
                          current is GetAllCategoriesError,
                      builder: (context, state) {
                        final homeCubit = context.read<CategoryCubit>();

                        return homeCubit.categoriesModel == null ||
                                state is GetAllCategoriesLoading
                            ? CategoryGridViewInCategorySkeletonizerWidget()
                            : homeCubit.categoriesModel!.categories == null ||
                                    homeCubit
                                        .categoriesModel!.categories!.isEmpty
                                ? EmptyWidget(
                                    imagePath:
                                        'assets/images/svgs/emptyCategory.svg',
                                    title: 'categories.thereAreNoProductsInThisSectionYet'.tr(),
                                    description:
                                        'categories.browseOurOtherSections'.tr(),
                                  )
                                : CategoryGridViewInCategoryWidget();
                        // : CategoryItemInCategorySkeletonizerWidget();
                      },
                    ),
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
