import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/features/category/business_logic/category_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/business_logic/product_by_categrory_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/presentation/screen/products_by_category_screen.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/widgets/category_item_in_category_widget.dart';

class CategoryGridViewInCategoryWidget extends StatelessWidget {
  const CategoryGridViewInCategoryWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final homeCubit = context.read<CategoryCubit>();

    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemCount: homeCubit.categoriesModel!.categories!.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisSpacing: 22.sp,
        mainAxisExtent: 120.sp,
        crossAxisSpacing: 14.sp,
      ),
      itemBuilder: (context, index) {
        final category = homeCubit.categoriesModel!.categories![index];

        return CategoryItemInCategoryWidget(
          onTap: () {
            // context.pushNamed(
            //   Routes.productsByCategoryScreen,
            //   arguments: ProductsByCategoryArgumets(
            //     categoryId: category.id!,
            //     name: category.name!,
            //   ),
            // );
            PersistentNavBarNavigator.pushNewScreen(
              context,
              screen: BlocProvider(
                create: (context) => ProcutsByCategoryCubit(getIt())
                  ..getChildrenCategories(category.id!)
                  ..intController()
                  ..getProcutsByCategory(categoryId: category.id!, page: 1),
                child: ProductsByCategoryScreen(
                  categroyName: category.name!,
                ),
              ),
              withNavBar: true,
              pageTransitionAnimation: PageTransitionAnimation.cupertino,
            );
          },
          imageUrl: category.image,
          categoryName: category.name!,
        );
      },
    );
  }
}
