import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class CategoryGridViewInCategorySkeletonizerWidget extends StatelessWidget {
  const CategoryGridViewInCategorySkeletonizerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        itemCount: 9,
        gridDelegate:
        SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          mainAxisSpacing: 22.sp,
          mainAxisExtent: 120.sp,
          crossAxisSpacing: 14.sp,
        ),
        itemBuilder: (context, index) {
          return Column(
            spacing: 10.h,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius - 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      AppConstants.borderRadius - 1,
                    ),
                    child: Image.asset(
                      'assets/images/pngs/clothes_icon.png',
                      height: 95.h,
                      width: 95.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
              SizedBox(
                width: 68.w,
                child: Text(
                  'Test Test',
                  textAlign: TextAlign.center,
                  style: Styles.highlightEmphasis.copyWith(
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              )
            ],
          );
        },
      ),
    );
  }
}
