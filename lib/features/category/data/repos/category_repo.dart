import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/api_results/api_result.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/exceptions.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/errors/failure.dart';
import 'package:tegra_ecommerce_app/features/category/data/api_services/category_api_services.dart';
import 'package:tegra_ecommerce_app/features/category/data/model/all_categories_model.dart';

class CategroyRepository {
  final CategroyApiServices homeApiServices;

  CategroyRepository(this.homeApiServices);

  Future<ApiResult<CategoriesModel>> getAllCategories() async {
    final response = await homeApiServices.getAllCategories();

    try {  
      if (response!.statusCode == 200) {
        CategoriesModel categoriesModel =
            CategoriesModel.fromJson(response.data);

        return ApiResult.success(categoriesModel);
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }
}
