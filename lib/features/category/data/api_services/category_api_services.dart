import 'package:dio/dio.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/end_points.dart';

class CategroyApiServices {
  CategroyApiServices(this._dioFactory);

  final DioHelper _dioFactory;
  Future<Response?> getAllCategories() async {
    return _dioFactory.get(
      endPoint: EndPoints.getAllCategories,
    );
  }

}
