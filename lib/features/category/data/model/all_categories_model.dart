import 'package:json_annotation/json_annotation.dart';

part 'all_categories_model.g.dart';

@JsonSerializable()
class CategoriesModel {
  @JsonKey(name: 'data')
  List<Category>? categories;
  

  String? status;
  String? error;
  int? code;

  CategoriesModel(
      {this.categories,
      this.status,
      this.error,
      this.code,
      });

  factory CategoriesModel.fromJson(Map<String, dynamic> json) =>
      _$CategoriesModelFromJson(json);

  Map<String, dynamic> toJson() => _$CategoriesModelToJson(this);
}

@JsonSerializable()
class Category {
  int? id;
  String? name;
  String? image;
  int? hasChildren;

  Category({this.id, this.name, this.image, this.hasChildren});

  factory Category.fromJson(Map<String, dynamic> json) =>
      _$CategoryFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryToJson(this);
}
