// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyB8ylp5C_WfzhgfJwuUixfgFGvtM2RsFXM',
    appId: '1:137352411288:web:4d3ea747a2665e372838ed',
    messagingSenderId: '137352411288',
    projectId: 'tgra-c338a',
    authDomain: 'tgra-c338a.firebaseapp.com',
    storageBucket: 'tgra-c338a.firebasestorage.app',
    measurementId: 'G-GVGYB8324E',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDnhbufKQWA7NaxNVWZaYEYxiVQBr_k0TM',
    appId: '1:137352411288:android:64808ff81ac8f32a2838ed',
    messagingSenderId: '137352411288',
    projectId: 'tgra-c338a',
    storageBucket: 'tgra-c338a.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDfzNgKZ89XkOihuAkShPctLxr_AgVV7YA',
    appId: '1:137352411288:ios:baa18bb25e47c0532838ed',
    messagingSenderId: '137352411288',
    projectId: 'tgra-c338a',
    storageBucket: 'tgra-c338a.firebasestorage.app',
    iosBundleId: 'com.example.tegraEcommerceApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDfzNgKZ89XkOihuAkShPctLxr_AgVV7YA',
    appId: '1:137352411288:ios:baa18bb25e47c0532838ed',
    messagingSenderId: '137352411288',
    projectId: 'tgra-c338a',
    storageBucket: 'tgra-c338a.firebasestorage.app',
    iosBundleId: 'com.example.tegraEcommerceApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyB8ylp5C_WfzhgfJwuUixfgFGvtM2RsFXM',
    appId: '1:137352411288:web:a0287e91bfbaedae2838ed',
    messagingSenderId: '137352411288',
    projectId: 'tgra-c338a',
    authDomain: 'tgra-c338a.firebaseapp.com',
    storageBucket: 'tgra-c338a.firebasestorage.app',
    measurementId: 'G-GNG840NC6J',
  );
}
