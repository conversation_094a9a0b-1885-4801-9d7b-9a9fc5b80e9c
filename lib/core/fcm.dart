// import 'dart:io';

// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:we_go_app/core/cache_helper/cache_helper.dart';
// import 'package:we_go_app/core/cache_helper/cache_keys.dart';
// import 'package:we_go_app/core/utils/app_constants.dart';
// import 'package:we_go_app/core/widgets/notifcation_snack_bar.dart';

// Future<void> handleBackgroundMessage(RemoteMessage message) async {}

// class PushNotificationService {
//   final _fcm = FirebaseMessaging.instance;
//   String? fCMToken;

//   // final _androidChannel = const AndroidNotificationChannel(
//   //   'high_importance_channel',
//   //   'High Importance Notifications',
//   //   description: "This channel is used for important notifications",
//   //   importance: Importance.defaultImportance,
//   // );
//   final _localNotifications = FlutterLocalNotificationsPlugin();
//   void handleMessage(RemoteMessage? message) {
//     if (message == null) {
//       return;
//     } else {}
//   }

//   Future initLocalNotifications() async {
//     const android = AndroidInitializationSettings('@mipmap/launcher_icon');
//     const settings = InitializationSettings(android: android);

//     await _localNotifications.initialize(
//       settings,
//     );
//   }

//   Future initPushNotifications() async {
//     await _fcm.requestPermission();
//     await FirebaseMessaging.instance
//         .setForegroundNotificationPresentationOptions(
//       alert: true,
//       badge: true,
//       sound: true,
//     );

//     FirebaseMessaging.onMessageOpenedApp.listen(handleMessage);
//     FirebaseMessaging.onMessage.listen((event) {
//       final notification = event.notification;
//       if (notification == null) return;
//       notificationSnackBar(
//           context: AppConstants.navigatorKey.currentContext,
//           message: notification.title,
//           event: event);
//     });
//   }

//   Future<void> initialize() async {
//     // var androidInitialize =
//     //     const AndroidInitializationSettings('notification_icon');
//     // var iOSInitialize = const DarwinInitializationSettings();
//     // var initializationsSettings =
//     //     InitializationSettings(android: androidInitialize, iOS: iOSInitialize);
//     try {
//       await initLocalNotifications();
//     } on Exception catch (e) {
//       // TODO
//       print(e);
//     }
//     print("here1");
//     try {
//       if (Platform.isAndroid) {
//         _localNotifications
//             .resolvePlatformSpecificImplementation<
//                 AndroidFlutterLocalNotificationsPlugin>()!
//             .requestNotificationsPermission();
//       }
//       if (Platform.isIOS) {
//         _localNotifications
//             .resolvePlatformSpecificImplementation<
//                 IOSFlutterLocalNotificationsPlugin>()!
//             .requestPermissions(
//               alert: true,
//               badge: true,
//               provisional: false,
//               sound: true,
//             )
//             .then((value) async {
//           await FirebaseMessaging.instance
//               .setForegroundNotificationPresentationOptions(
//             alert: true,
//             badge: true,
//             sound: true,
//           );
//         });
//       }
//       await _fcm.requestPermission();
//       await _fcm.getAPNSToken();
//       await Future.delayed(const Duration(seconds: 2));
//       fCMToken = await _fcm.getToken();
//       await CacheHelper.saveData(key: CacheKeys.deviceToken, value: fCMToken);
//       await initPushNotifications();
//     } on Exception catch (e) {
//       // TODO
//       print(e);
//     }
//   }
// }

import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_helper.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_keys.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/notifcation_snack_bar.dart';

class PushNotificationService {
  final FirebaseMessaging _fcm = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  String? fCMToken;

  // Initialization for local notifications
  Future<void> initLocalNotifications() async {
    const android = AndroidInitializationSettings('@mipmap/launcher_icon');
    const settings = InitializationSettings(android: android);
    await _localNotifications.initialize(settings);
  }

  // Initialization for push notifications
  Future<void> initPushNotifications() async {
    await _fcm.requestPermission();
    await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      final notification = message.notification;
      if (notification != null) {
        notificationSnackBar(
          context: AppConstants.navigatorKey.currentContext,
          message: notification.title,
          event: message,
        );
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen(handleMessage);
  }

  // Handle when a message is tapped on
  void handleMessage(RemoteMessage message) {
    if (message != null) {
      // Handle the logic when the app is opened from the notification
    }
  }

  // Initialization process to set up everything
  Future<void> initialize() async {
    try {
      // Initialize local notifications
      await initLocalNotifications();

      // Request permission for notifications based on the platform
      if (Platform.isAndroid) {
        await _localNotifications
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()!
            .requestNotificationsPermission();
      }

      if (Platform.isIOS) {
        await _localNotifications
            .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()!
            .requestPermissions(
              alert: true,
              badge: true,
              sound: true,
            );
      }

      // Get FCM token and save it in the cache
      fCMToken = await _fcm.getToken();
      await CacheHelper.saveData(key: CacheKeys.deviceToken, value: fCMToken);

      // Initialize push notifications
      await initPushNotifications();

    } catch (e) {
      // Handle exceptions
      print("Error during initialization: $e");
    }
  }
}
