class Routes {
  static const splashScreen = 'splash-screen';
  static const supportScreen = 'support-screen';
  static const introScreen = 'intro-screen';
  static const startScreen = 'start-screen';
  static const onBoardingScreen = 'on-boarding-screen';
  static const visionScreen = 'terms-of-use-screen';
  static const aboutUsScreen = 'privacy-policy-screen';
  static const loginScreen = 'login-screen';
  static const contactSupportScreen = 'contact-support-screen';
  static const registerScreen = 'register-screen';
  static const verifyOtpScreen = 'verify-otp_screen';
  // static const localizationScreen = 'localization_screen';
  static const mainlayoutScreen = 'mainlayout-screen';
  static const homeScreen = 'home-screen';
  static const filterScreen = 'filter-screen';
  static const notificationScreen = 'notification-screen';
  static const categoryScreen = 'category-screen';
  static const changePasswordScreen = 'change-password-screen';
  static const favoriteScreen = 'favorite_screen';
  static const allAddressScreen = 'all_address_screen';
  static const addNewAddressScreen = 'add_new_address_screen';
  static const editAddressScreen = 'edit_address_screen';
  static const productsDetailsScreen = 'product-details-screen';
  static const ordersScreen = 'orders-screen';
  static const brandDetailsScreen = 'brand-details-screen';
  static const paymentScreen = 'payment-screen';
  static const similarProductsScreen = 'similar-products-screen';
  static const productFilterInBrandDetails = 'product_filter_in_brand_details';
  static const productReviewsScreen = 'product_reviews_screen';
  static const productsByCategoryScreen = 'products_by_category_screen';
  static const myOrdersScreen = 'my_orders_screen';
  static const orderDetailsScreen = 'order_details_screen';
  static const chatScreen = 'chat_screen';
  static const searchScreen = 'search-screen';
}
