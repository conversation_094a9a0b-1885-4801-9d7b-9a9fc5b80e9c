import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/features/address/business_logic/cubit/address_cubit.dart';
import 'package:tegra_ecommerce_app/features/address/data/model/address_model.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/screens/add_new_address_screen.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/screens/all_address_screen.dart';
import 'package:tegra_ecommerce_app/features/address/presentation/screens/edit_address_screen.dart';
import 'package:tegra_ecommerce_app/features/auth/business_logic/auth_cubit.dart';
import 'package:tegra_ecommerce_app/features/auth/presentation/screens/login_screen.dart';
import 'package:tegra_ecommerce_app/features/auth/presentation/screens/register_screen.dart';
import 'package:tegra_ecommerce_app/features/auth/presentation/screens/verify_otp_screen.dart';
import 'package:tegra_ecommerce_app/features/brand/business_logic/brand_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand/data/models/brands_by_category/brands_by_category_model.dart';
import 'package:tegra_ecommerce_app/features/brand/presentation/screens/brand_screen.dart';
import 'package:tegra_ecommerce_app/features/brand_details/business_logic/brand_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand_details/presentation/screens/brand_details_screen.dart';
import 'package:tegra_ecommerce_app/features/brand_details/presentation/screens/product_filter_in_brand_details.dart';
import 'package:tegra_ecommerce_app/features/cart/bloc/cubit/cart_cubit.dart';
import 'package:tegra_ecommerce_app/features/cart/data/model/cart_recipt_model.dart';
import 'package:tegra_ecommerce_app/features/cart/presentation/screens/cart_screen.dart';
import 'package:tegra_ecommerce_app/features/category/business_logic/category_cubit.dart';
import 'package:tegra_ecommerce_app/features/category/presentation/screens/category_screen.dart';
import 'package:tegra_ecommerce_app/features/chat/bloc/cubit/chat_cubit.dart';
import 'package:tegra_ecommerce_app/features/chat/presentation/screens/chat_screen.dart';
import 'package:tegra_ecommerce_app/features/favorite/bloc/cubit/favorite_cubit.dart';
import 'package:tegra_ecommerce_app/features/favorite/persenation/favorite_screen.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/home_cubit.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/screens/home_screen.dart';
import 'package:tegra_ecommerce_app/features/intro/business_logic/intro_cubit.dart';
import 'package:tegra_ecommerce_app/features/intro/presentation/screens/intro_screen.dart';
import 'package:tegra_ecommerce_app/features/main%20layout/business_logic/main_layout_cubit.dart';
import 'package:tegra_ecommerce_app/features/main%20layout/main_layout.dart';
import 'package:tegra_ecommerce_app/features/my_orders/business_logic/my_orders_cubit.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/models/orders_datails/orders_details_data_model.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/screens/my_order_details_screen.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/screens/my_orders_screen.dart';
import 'package:tegra_ecommerce_app/features/notification/presentation/screens/notifications_screen.dart';
import 'package:tegra_ecommerce_app/features/on_boarding/presentation/screens/on_boarding_screen.dart';
import 'package:tegra_ecommerce_app/features/orders/presentation/screens/orders_screen.dart';
import 'package:tegra_ecommerce_app/features/payment/business_logic/payment_cubit.dart';
import 'package:tegra_ecommerce_app/features/payment/presentation/screens/payment_screen.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/business_logic/product_by_categrory_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/presentation/screen/products_by_category_screen.dart';
import 'package:tegra_ecommerce_app/features/product%20details/business_logic/product_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_details_screen.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/product_reviews_screen.dart';
import 'package:tegra_ecommerce_app/features/product%20details/presentation/screens/similar_products_screen.dart';
import 'package:tegra_ecommerce_app/features/profile/business_logic/profile_cubit.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/screens/about_us_screen.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/screens/change_password_screen.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/screens/profile_screen.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/screens/vision_screen.dart';
import 'package:tegra_ecommerce_app/features/search/business_logic/search_cubit.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/screens/filter_screen.dart';
import 'package:tegra_ecommerce_app/features/search/presentation/screens/search_screen.dart';
import 'package:tegra_ecommerce_app/features/splash/business_logic/splash_cubit.dart';
import 'package:tegra_ecommerce_app/features/splash/presentation/screens/splash_screen.dart';
import 'package:tegra_ecommerce_app/features/start/presentation/screens/start_screen.dart';
import 'package:tegra_ecommerce_app/features/support/business_logic/support_cubit.dart';
import 'package:tegra_ecommerce_app/features/support/presentation/screens/contact_support.dart';
import 'package:tegra_ecommerce_app/features/support/presentation/screens/support_screen.dart';

class AppRouter {
  // Route? generateRoute(RouteSettings settings) {
  //   PageTransition transition<T extends Cubit<Object>>({
  //     required Widget screen,
  //     T? cubit,
  //     Object? arguments,
  //     PageTransitionType type = PageTransitionType.fade,
  //     Duration duration = const Duration(milliseconds: 200),
  //     Alignment alignment = Alignment.center,
  //   }) {
  //     final child = cubit != null
  //         ? BlocProvider<T>(
  //             create: (context) => cubit,
  //             child: screen,
  //           )
  //         : screen;

  //     return PageTransition(
  //       child: child,
  //       type: type,
  //       duration: duration,
  //       alignment: alignment,
  //       settings: settings,
  //     );
  //   }

  Route? generateRoute(RouteSettings settings) {
    MaterialPageRoute transition<T extends Cubit<Object>>({
      required Widget screen,
      T? cubit,
      Object? arguments,
    }) {
      final child = cubit != null
          ? BlocProvider<T>(
              create: (context) => cubit,
              child: screen,
            )
          : screen;

      return MaterialPageRoute(
        settings: settings,
        builder: (context) => child,
      );
    }

    switch (settings.name) {
      case Routes.splashScreen:
        return transition(
          screen: const SplashScreen(),
          cubit: SplashCubit()..startSplash(),
        );
      case Routes.introScreen:
        return transition(
          screen: const IntroScreen(),
          cubit: IntroCubit()..checkTokenAndNavigate(),
        );
      case Routes.startScreen:
        return transition(
          screen: const StartScreen(),
        );
      case Routes.supportScreen:
        return transition(
          screen: const SupportScreen(),
          cubit: SupportCubit(getIt())..getAllTickets(),
        );
      case Routes.contactSupportScreen:
        return transition(
          screen: const ContactSupportScreen(),
          cubit: SupportCubit(getIt()),
        );
      case Routes.visionScreen:
        return transition(
          screen: const VisionScreen(),
          cubit: ProfileCubit(getIt())..getSettingsData(title: 'vision'),
        );
      case Routes.aboutUsScreen:
        return transition(
          screen: const AboutUsScreen(),
          cubit: ProfileCubit(getIt())..getSettingsData(title: 'about_us'),
        );
      case Routes.onBoardingScreen:
        return transition(
          screen: const OnBoardingScreen(),
        );
      case Routes.loginScreen:
        return transition(
          screen: const LoginScreen(),
          cubit: AuthCubit(getIt()),
        );
      case Routes.verifyOtpScreen:
        return transition(
          screen: const VerifyOtpScreen(),
          cubit: AuthCubit(getIt())..startTimer(),
        );
      case Routes.registerScreen:
        return transition(
          screen: RegisterScreen(),
          cubit: AuthCubit(getIt()),
        );

      case Routes.mainlayoutScreen:
        return transition(
          screen: MainLayoutScreen(),
          cubit: MainLayoutCubit(),
        );

      case Routes.filterScreen:
        return transition(
          screen: FilterScreen(),
          cubit: SearchCubit(getIt()),
        );

      case Routes.ordersScreen:
        return transition(
          screen: OrdersScreen(),
        );
      case Routes.productFilterInBrandDetails:
        final brandData = settings.arguments as BrandDataItem;

        return transition(
          screen: ProductFilterInBrandDetails(
              //brandData: brandData,
              ),
          cubit: BrandDetailsCubit(getIt()),
        );
      case Routes.paymentScreen:
        final data = settings.arguments as Data;
        return transition(
          screen: PaymentScreen(
            data: data,
          ),
          cubit: PaymentCubit(getIt()),
        );
      case Routes.searchScreen:
        return transition(
          screen: SearchScreen(),
          cubit: SearchCubit(getIt()),
        );
      case Routes.similarProductsScreen:
        final productId = settings.arguments as int;

        return transition(
          screen: SimilarProductsScreen(productId: productId),
          cubit: ProductDetailsCubit(getIt())..loadSimilarProducts(productId),
        );

      case Routes.notificationScreen:
        return transition(
          screen: NotificationsScreen(),
          cubit: HomeCubit(getIt()),
        );

      case Routes.changePasswordScreen:
        return transition(
          screen: ChangePasswordScreen(),
          cubit: ProfileCubit(getIt()),
        );

      case Routes.favoriteScreen:
        return transition(
          screen: FavoriteScreen(),
          cubit: FavoriteCubit(getIt())
            ..getAllFavorite()
            ..intController(),
        );
      case Routes.allAddressScreen:
        return transition(
          screen: AllAddressScreen(),
          cubit: AddressCubit(getIt())..getAllAddresses(),
        );
      case Routes.addNewAddressScreen:
        return transition(
          screen: AddNewAddressScreen(),
          cubit: AddressCubit(getIt()),
        );
      case Routes.myOrdersScreen:
        return transition(
          screen: MyOrdersScreen(),
          cubit: MyOrdersCubit(getIt()),
        );
      case Routes.orderDetailsScreen:
        // final orderId = settings.arguments as int;

        return transition(
          screen: MyOrderDetailsScreen(),
          cubit: MyOrdersCubit(getIt()),
        );
      case Routes.editAddressScreen:
        final address = settings.arguments as Address;

        return transition(
          screen: EditAddressScreen(
            address: address,
          ),
          cubit: AddressCubit(getIt())..getAllAddresses(),
        );
      case Routes.productsDetailsScreen:
        final product = settings.arguments as ProductDetailsArguments;
        //final Map<String, dynamic> data = settings.arguments as Map<String, dynamic>;
        return transition(
          screen: ProductDetailsScreen(
            skuAttributes: product.skuAttributes,
          ),
          cubit: ProductDetailsCubit(getIt())
            ..showProductDetails(product.productId,
                skuId: product.skuAttributes?.first.skuId)
            ..loadSimilarProducts(product.productId)
            ..getProductReviews(product.productId),
        );
      case Routes.brandDetailsScreen:
        final brandData = settings.arguments as BrandDataItem;
        return transition(
          screen: BrandDetailsScreen(
              //brandData: brandData
              ),
          cubit: BrandDetailsCubit(getIt())..getProductsByBrands(brandData.id!),
        );
      case Routes.productReviewsScreen:
        final productId = settings.arguments as int;
        return transition(
          screen: ProductReviewsScreen(productId: productId),
          cubit: ProductDetailsCubit(getIt())..getProductReviews(productId),
        );
      case Routes.productsByCategoryScreen:
        final category = settings.arguments as ProductsByCategoryArgumets;

        return transition(
          screen: ProductsByCategoryScreen(
            categroyName: category.name,
          ),
          cubit: ProcutsByCategoryCubit(getIt())
            ..getChildrenCategories(category.categoryId)
            ..intController()
            ..getProcutsByCategory(categoryId: category.categoryId, page: 1),
        );
      case Routes.chatScreen:
        final ticketId = settings.arguments as int;
        return transition(
          screen: BlocProvider(
            create: (context) => ChatCubit(getIt())
              ..scrollToBottom()
              ..getSpecificTicket(ticketId),
            child: const ChatScreen(),
          ),
        );
      default:
        return null;
    }
  }

  List<Widget> screen = [
    BlocProvider(
      create: (context) => HomeCubit(getIt())
        ..showAllBanners()
        ..getBestOffers()
        ..getBestSeller(),
      child: HomeScreen(),
    ),
    BlocProvider(
      create: (context) => CategoryCubit(getIt())..getAllCategories(),
      child: CategoryScreen(),
    ),
    BlocProvider(
      create: (context) => CartCubit(getIt())..getCart(),
      child: CartScreen(),
    ),
    BlocProvider(
      create: (context) => BrandCubit(getIt())..getAllBrands(),
      child: BrandScreen(),
    ),
    BlocProvider(
      create: (context) => ProfileCubit(getIt())..getProfileData(),
      child: ProfileScreen(),
    ),
  ];
}

class ProductsByCategoryArgumets {
  final int categoryId;
  final String name;

  ProductsByCategoryArgumets({
    required this.categoryId,
    required this.name,
  });
}

class ProductDetailsArguments {
  final int productId;
  final List<SkuAttribute>? skuAttributes;

  ProductDetailsArguments({
    required this.productId,
    this.skuAttributes, // Now optional
  });
}
