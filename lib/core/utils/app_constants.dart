import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/services/di/dependency_injection.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/features/brand_details/business_logic/brand_details_cubit.dart';
import 'package:tegra_ecommerce_app/features/brand_details/presentation/widgets/rating_dialog_widget.dart';
import 'package:tegra_ecommerce_app/features/my_orders/presentation/widgets/my_order_details/rating_dialog_for_product_Widget.dart';
import 'package:tegra_ecommerce_app/features/profile/presentation/widgets/show_dialog_for_logout.dart';
import 'package:tegra_ecommerce_app/features/support/presentation/widgets/pop_up_send_dialog_widget.dart';

class AppConstants {
  static String? userToken;

  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  static int mainLayoutInitialScreenIndex = 0;

  static screenWidth(context) => MediaQuery.sizeOf(context).width;

  static screenHeight(context) => MediaQuery.sizeOf(context).height;

  static double borderRadius = 8.r;

  /// Build Error Text
  static String buildErrorText(String message) {
    return Text(
      message,
      style: Styles.contentEmphasis.copyWith(
        color: AppColors.redColor100,
      ),
    ).data!;
  }

  /// Product Images List in Search
  static List<String> productListViewImages = [
    'assets/images/pngs/t_shirt_image5.png',
    Assets.assetsImagesPngsTShirtImage,
    'assets/images/pngs/t_shirt_image3.png',
    Assets.assetsImagesPngsTShirtImage,
    'assets/images/pngs/t_shirt_image3.png',
  ];

  /// Product Images List in Search
  static List<String> productGridViewImages = [
    'assets/images/pngs/t_shirt_image10.png',
    'assets/images/pngs/t_shirt_image6.png',
    'assets/images/pngs/t_shirt_image7.png',
    'assets/images/pngs/t_shirt_image5.png',
    'assets/images/pngs/t_shirt_image6.png',
    'assets/images/pngs/t_shirt_image7.png',
  ];

  /// Search History List
  static List<String> searchHistoryList = [
    "تيشيرت بوما",
    "تيشيرت اديدس",
    "بنطلون اوتليج",
    "تيشيرت بوما",
    "تيشيرت اديدس",
    "بنطلون اوتليج",
  ];

  /// Categories selector list in filter
  static List<String> categories = [
    "حذاء",
    "تيشيرت",
    "فستان",
    "إكسسوارات",
  ];

  /// Categories selector list in payment
  // static List<String> categoriesofpayment = [
  //   'payment.cash'.tr(),
  //   'payment.credit'.tr(),
  //   // "محفظة إلكترونية",
  // ];

  /// categoriesgender list in filter
  static List<String> categoriesgender = [
    "الكل",
    "رجالى",
    "نسائي",
    "أطفالى",
    "بدلة",
    "فستان فرح",
  ];

  /// Category List
  // static List<String> categoryImagesList = [
  //   'assets/images/pngs/clothes_icon.png',
  //   'assets/images/pngs/market_icon.png',
  //   'assets/images/pngs/furniture_icon.png',
  //   'assets/images/pngs/health_icon.png',
  //   'assets/images/pngs/furniture_icon.png',
  // ];
  // static List<String> categoryTitleList = [
  //   'home.clothes'.tr(),
  //   'home.market'.tr(),
  //   'home.homeFurniture'.tr(),
  //   'home.healthAndBeauty'.tr(),
  //   'home.homeFurniture'.tr(),
  // ];

  /// Brands Cover List
  static List<String> brandsCoversImage = [
    'assets/images/pngs/puma_cover.png',
    'assets/images/pngs/puma_cover.png',
  ];

  /// Sub Filters List
  static List<String> subFiltersList = [
    'الفئة',
    'اللون',
    'المقاس',
    'الخامة',
    'الفئة',
    'اللون',
    'المقاس',
    'الخامة',
  ];

  /// Show Rating Dialog
  static void showRatingDialogForBrand(
    BuildContext context,
    int brandId,
  ) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return BlocProvider(
          create: (context) => BrandDetailsCubit(getIt()),
          child: RatingDialogForBrandWidget(
            brandId: brandId,
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween =
            Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
        var offsetAnimation = animation.drive(tween);

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  /// showDialogForlogout
  static void showDialogForlogout(
    BuildContext context,
    VoidCallback onButtonTap,
  ) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return DialogForLogoutWidget(
          title: 'profile.logout?'.tr(),
          description: 'profile.wanttologout'.tr(),
          buttonText: 'profile.logout'.tr(),
          onButtonTap: onButtonTap,
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween =
            Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
        var offsetAnimation = animation.drive(tween);

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  /// ShowDialogForRemoveAccount
  static void showDialogForRemoveAccount(
    BuildContext context,
    VoidCallback onButtonTap,
  ) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return DialogForLogoutWidget(
          title: 'profile.deleteaccount?'.tr(),
          description: 'profile.wanttodelete'.tr(),
          buttonText: 'profile.deleteaccount'.tr(),
          onButtonTap: onButtonTap,
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween =
            Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
        var offsetAnimation = animation.drive(tween);

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  /// Rating Dialog For product Widget
  static void showRatingDialogForProduct({
    required BuildContext context,
    required int productId,
    required int orderId,
  }) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return RatingDialogForProductWidget(
          productId: productId,
          orderId: orderId,
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween =
            Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
        var offsetAnimation = animation.drive(tween);

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  /// Show pop up send Dialog
  static void popUpSendDialog(BuildContext context) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return PopUpSendDialogWidget();
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween =
            Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
        var offsetAnimation = animation.drive(tween);

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }
}
