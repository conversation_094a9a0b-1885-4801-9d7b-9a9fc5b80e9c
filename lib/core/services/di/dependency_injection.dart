import 'package:get_it/get_it.dart';
import 'package:tegra_ecommerce_app/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:tegra_ecommerce_app/features/address/data/api_servies/api_services.dart';
import 'package:tegra_ecommerce_app/features/address/data/repos/addresses_repo.dart';
import 'package:tegra_ecommerce_app/features/auth/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/brand/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/brand/data/repos/brand_repo.dart';
import 'package:tegra_ecommerce_app/features/brand_details/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/brand_details/data/repos/brand_details_repo.dart';
import 'package:tegra_ecommerce_app/features/cart/data/api_services/cart_api_services.dart';
import 'package:tegra_ecommerce_app/features/cart/data/repo/cart_repo.dart';
import 'package:tegra_ecommerce_app/features/category/data/api_services/category_api_services.dart';
import 'package:tegra_ecommerce_app/features/category/data/repos/category_repo.dart';
import 'package:tegra_ecommerce_app/features/chat/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/chat/data/repo/chat_repo.dart';
import 'package:tegra_ecommerce_app/features/favorite/data/api_services/favorite_api_services.dart';
import 'package:tegra_ecommerce_app/features/favorite/data/repo/favorite_repo.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/home/<USER>/repos/home_repo.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/my_orders/data/repos/my_orders_repo.dart';
import 'package:tegra_ecommerce_app/features/payment/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/payment/data/repos/payment_repo.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/data/api_services/product_by_categrory_api_services.dart';
import 'package:tegra_ecommerce_app/features/product%20by%20category/data/repos/product_by_categrory_repo.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/product%20details/data/repos/product_details_repo.dart';
import 'package:tegra_ecommerce_app/features/profile/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/profile/data/repos/profile_repo.dart';
import 'package:tegra_ecommerce_app/features/search/data/api_servicess/api_services.dart';
import 'package:tegra_ecommerce_app/features/search/data/repos/search_repo.dart';
import 'package:tegra_ecommerce_app/features/support/data/api_services/api_services.dart';
import 'package:tegra_ecommerce_app/features/support/data/repos/support_repo.dart';

import '../../../features/auth/data/repos/auth_repo.dart';

final getIt = GetIt.instance;

Future<void> setupDependencyInjection() async {
  /// Auth
  /// Dio
  getIt.registerLazySingleton<DioHelper>(() => DioHelper());

  /// ApiServices
  getIt.registerLazySingleton<AuthApiServices>(() => AuthApiServices(getIt()));
  getIt.registerLazySingleton<HomeApiServices>(() => HomeApiServices(getIt()));
  getIt.registerLazySingleton<ProfileApiServices>(
      () => ProfileApiServices(getIt()));
  getIt
      .registerLazySingleton<BrandApiServices>(() => BrandApiServices(getIt()));
  getIt.registerLazySingleton<ProductDetailsApiServices>(
      () => ProductDetailsApiServices(getIt()));
  getIt.registerLazySingleton<BrandDetailsApiServices>(
      () => BrandDetailsApiServices(getIt()));
  getIt.registerLazySingleton<AddressesApiServices>(
      () => AddressesApiServices(getIt()));
  getIt.registerLazySingleton<PaymentApiServices>(
      () => PaymentApiServices(getIt()));
  getIt.registerLazySingleton<MyOrdersApiServices>(
      () => MyOrdersApiServices(getIt()));
  getIt.registerLazySingleton<SearchApiServices>(
      () => SearchApiServices(getIt()));
  getIt.registerLazySingleton<SupportApiServices>(
      () => SupportApiServices(getIt()));
  getIt.registerLazySingleton<ChatApiServices>(
      () => ChatApiServices(getIt()));

  /// Repos
  getIt.registerLazySingleton<AuthRepository>(() => AuthRepository(getIt()));
  getIt.registerLazySingleton<HomeRepository>(() => HomeRepository(getIt()));
  getIt.registerLazySingleton<BrandRepository>(() => BrandRepository(getIt()));
  getIt.registerLazySingleton<PaymentRepo>(() => PaymentRepo(getIt()));
  getIt.registerLazySingleton<ProfileRepository>(
      () => ProfileRepository(getIt()));
  getIt.registerLazySingleton<ProductDetailsRepository>(
      () => ProductDetailsRepository(getIt()));
  getIt.registerLazySingleton<BrandDetailsRepository>(
      () => BrandDetailsRepository(getIt()));

  getIt.registerLazySingleton<FavoriteRepository>(
      () => FavoriteRepository(getIt()));
  getIt.registerLazySingleton<FavoriteApiServices>(
      () => FavoriteApiServices(getIt()));
  getIt.registerLazySingleton<AddressesRepository>(
      () => AddressesRepository(getIt()));

  getIt.registerLazySingleton<ProductByCategroryRepo>(
      () => ProductByCategroryRepo(getIt()));
  getIt.registerLazySingleton<ProductByCategroryApiServices>(
      () => ProductByCategroryApiServices(getIt()));
  getIt.registerLazySingleton<CategroyRepository>(
      () => CategroyRepository(getIt()));
  getIt.registerLazySingleton<CategroyApiServices>(
      () => CategroyApiServices(getIt()));

  getIt.registerLazySingleton<CartApiServices>(() => CartApiServices(getIt()));
  getIt.registerLazySingleton<CartRepository>(() => CartRepository(getIt()));
  getIt.registerLazySingleton<MyOrdersRepository>(() => MyOrdersRepository(getIt()));
  getIt.registerLazySingleton<SearchRepository>(() => SearchRepository(getIt()));
  getIt.registerLazySingleton<SupportRepository>(() => SupportRepository(getIt()));
  getIt.registerLazySingleton<ChatRepo>(() => ChatRepo(getIt()));
}
