import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/assets.dart';

class ClipContainerInSpecificProductWidget extends StatelessWidget {
  const ClipContainerInSpecificProductWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 0.sp,
      right: 0,
      left: 0,
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Image.asset(
          fit: BoxFit.fill,
          Assets.assetsImagesPngsProductStackImage,
        ),
      ),
    );
  }
}
