import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_helper.dart';
import 'package:tegra_ecommerce_app/core/cache_helper/cache_keys.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/themes/text_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';
import 'package:tegra_ecommerce_app/core/widgets/appbar/appbar_widget.dart';
import 'package:tegra_ecommerce_app/features/notification/presentation/screens/notifications_screen.dart';

class MainAppBarWidget extends StatelessWidget {
  const MainAppBarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBarWidget(
      rowWidget: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: MediaQuery.sizeOf(context).width / 1.8,
            child: Row(
              children: [
                CacheHelper.getData(key: CacheKeys.userImage) == null
                    ? Image.asset(
                        'assets/images/pngs/profile_image.png',
                      )
                    // : CircleAvatar(
                    //     backgroundColor: AppColors.scaffoldBackground,
                    //     backgroundImage: CachedNetworkImageProvider(
                    //       CacheHelper.getData(key: CacheKeys.userImage),
                    //       errorListener: (p0) => Icon(Icons.error),
                    //     ),
                    //   ),
                    : Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.scaffoldBackground,
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(50.r),
                          child: CachedNetworkImage(
                            imageUrl:
                                CacheHelper.getData(key: CacheKeys.userImage) ??
                                    '',
                            errorWidget: (context, url, error) => Center(
                              child: Icon(Icons.error),
                            ),
                          ),
                        ),
                      ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Column(
                    spacing: 4.h,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'home.haveANiceDay'.tr(),
                        style: Styles.captionRegular.copyWith(
                          color: AppColors.scaffoldBackground
                              .withValues(alpha: .5),
                        ),
                      ),
                      Text(
                        CacheHelper.getData(key: CacheKeys.userName) ?? "geast",
                        style: Styles.heading3.copyWith(
                          color: AppColors.neutralColor100,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          InkWell(
            onTap: () {
              PersistentNavBarNavigator.pushNewScreen(
                context,
                screen: NotificationsScreen(),
                withNavBar: true,
                pageTransitionAnimation: PageTransitionAnimation.cupertino,
              );
            },
            child: Container(
              padding: EdgeInsets.all(12.sp),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                color: AppColors.scaffoldBackground,
              ),
              child: Center(
                child: Badge.count(
                  count: 2,
                  backgroundColor: AppColors.secondaryColor500,
                  child: SvgPicture.asset(
                    'assets/images/svgs/notification_icon.svg',
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
