import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/themes/app_colors.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class BackButtonWidget extends StatelessWidget {
  const BackButtonWidget({
    super.key, this.onTap,
  });

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(12.sp),
        decoration: BoxDecoration(
          borderRadius:
              BorderRadius.circular(AppConstants.borderRadius),
          color: AppColors.scaffoldBackground,
        ),
        child: Center(
          child: Icon(
            Icons.arrow_back,
            size: 20.sp,
          ),
        ),
      ),
    );
  }
}
