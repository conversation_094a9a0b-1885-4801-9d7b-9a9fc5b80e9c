import 'package:flutter/material.dart';
import 'package:tegra_ecommerce_app/core/themes/hex_colors.dart';

class AppColors {
  /// Primary Colors
  static final primaryColor1000 = hexToColor('#004023');
  static final primaryColor900 = hexToColor('#006638');
  static final primaryColor800 = hexToColor('#008F4E');
  static final primaryColor700 = hexToColor('#00B865');
  static final primaryColor600 = hexToColor('#00E07B');
  static final primaryColor500 = hexToColor('#0AFF91');
  static final primaryColor400 = hexToColor('#33FFA3');
  static final primaryColor300 = hexToColor('#5CFFB5');
  static final primaryColor200 = hexToColor('#85FFC8');
  static final primaryColor100 = hexToColor('#ADFFDA');
  static final primaryColor10 = hexToColor('#0066381A').withValues(alpha: .1);

  /// Secondary Colors
  static final secondaryColor1000 = hexToColor('#584805');
  static final secondaryColor900 = hexToColor('#786207');
  static final secondaryColor800 = hexToColor('#997C09');
  static final secondaryColor700 = hexToColor('#B9960B');
  static final secondaryColor600 = hexToColor('#D9B10D');
  static final secondaryColor500 = hexToColor('#F1C617');
  static final secondaryColor400 = hexToColor('#F4D145');
  static final secondaryColor300 = hexToColor('#F7DD74');
  static final secondaryColor200 = hexToColor('#F9E8A2');
  static final secondaryColor100 = hexToColor('#FCF4D1');
  static final secondaryColor10 = hexToColor('#F1C6171A').withValues(alpha: .1);

  /// Neutral Colors
  static final neutralColor1500 = hexToColor('#030303');
  static final neutralColor1400 = hexToColor('#151515');
  static final neutralColor1300 = hexToColor('#272727');
  static final neutralColor1200 = hexToColor('#383838');
  static final neutralColor1100 = hexToColor('#4A4A4A');
  static final neutralColor1000 = hexToColor('#5C5C5C');
  static final neutralColor900 = hexToColor('#6E6E6E');
  static final neutralColor800 = hexToColor('#808080');
  static final neutralColor700 = hexToColor('#919191');
  static final neutralColor600 = hexToColor('#A3A3A3');
  static final neutralColor500 = hexToColor('#B5B5B5');
  static final neutralColor400 = hexToColor('#C7C7C7');
  static final neutralColor300 = hexToColor('#D8D8D8');
  static final neutralColor200 = hexToColor('#EAEAEA');
  static final neutralColor100 = hexToColor('#FCFCFC');
  static final neutralColor10 = hexToColor('#030303').withValues(alpha: .1);

  /// Red Colors
  static final redColor100 = hexToColor('#FB3748');
  static final redColor200 = hexToColor('#D00416');
  static final redColor10 = hexToColor('#FB37481A').withValues(alpha: .1);

  /// Yellow Colors
  static final yellowColor100 = hexToColor('#FFDB43');
  static final yellowColor200 = hexToColor('#DFB400');
  static final yellowColor10 = hexToColor('#FFDB431A').withValues(alpha: .1);

  /// Green Colors
  static final greenColor100 = hexToColor('#84EBB4');
  static final greenColor200 = hexToColor('#1FC16B');
//  final greenColor10 = hexToColor('#1FC16B1A').withValues(alpha: .1);
  static final greenColor10 = hexToColor('#1FC16B').withValues(alpha: .1);

  static final dividerColor = hexToColor('#92929D');

  static final scaffoldBackground = Colors.white;
}
