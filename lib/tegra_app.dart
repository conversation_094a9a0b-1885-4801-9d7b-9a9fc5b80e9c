import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tegra_ecommerce_app/core/extensions/log_util.dart';
import 'package:tegra_ecommerce_app/core/routing/app_router.dart';
import 'package:tegra_ecommerce_app/core/routing/routes_name.dart';
import 'package:tegra_ecommerce_app/core/utils/app_constants.dart';

class TegraApp extends StatelessWidget {
  final AppRouter appRouter;
  final String? token;

  const TegraApp({
    super.key,
    required this.appRouter,
    this.token,
  });

  @override
  Widget build(BuildContext context) {
    logWarning(AppConstants.userToken.toString());
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.light,
        ),
        child: MaterialApp(
          title: 'Saudi Boutique',
          localizationsDelegates: context.localizationDelegates,
          supportedLocales: context.supportedLocales,
          locale: context.locale,
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            fontFamily: 'DINNextLTArabic',
          ),
          // navigatorKey: ToastManager.navigatorKey,
          navigatorKey: AppConstants.navigatorKey,
          themeMode: ThemeMode.light,
          initialRoute: Routes.introScreen,
          // initialRoute: Routes.onBoardingScreen,
          onGenerateRoute: appRouter.generateRoute,
          builder: EasyLoading.init(),
        ),
      ),
    );
  }
}
